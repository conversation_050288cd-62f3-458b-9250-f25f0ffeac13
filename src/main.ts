import './assets/main.css';
//Import Code Mirror
import 'codemirror/mode/xml/xml.js'
import 'codemirror/lib/codemirror.css';
import 'codemirror/lib/codemirror.js';
import CodeMirror from 'codemirror';
//Import Froala Editor
import 'froala-editor/js/plugins.pkgd.min.js';
//Import third party plugins
import 'froala-editor/js/third_party/embedly.min';
import 'froala-editor/js/third_party/font_awesome.min';
import 'froala-editor/js/third_party/image_tui.min';
import 'froala-editor/js/third_party/spell_checker.min';
import 'froala-editor/js/plugins/code_view.min.js';
import 'froala-editor/js/plugins/code_beautifier.min.js';
// Import Froala Editor css files.
import 'froala-editor/css/froala_editor.pkgd.min.css';
import 'froala-editor/css/froala_style.min.css';
import 'froala-editor/css/plugins/code_view.css';
import 'froala-editor/css/plugins/code_view.min.css';

import { createApp } from 'vue';
import VueFroala from 'vue-froala-wysiwyg';

import MyAuraPreset from '@services/ui-component-library/themes/theme-v4.js';
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import Editor from 'primevue/editor';
import Ripple from 'primevue/ripple';
import TabPanel from 'primevue/tabpanel';
import TabView from 'primevue/tabview';
import ToastService from 'primevue/toastservice';
import Tooltip from 'primevue/tooltip';
// Load bravo-component-library themes last to ensure they take precedence
import AnnounceKit from 'announcekit-vue';

import('@services/ui-component-library/themes/global.css');
import('@services/ui-component-library/themes/primevue-variables.css');

import 'primeicons/primeicons.css';
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';

import App from './App.vue';
import { useDataDogRUM } from './composables/useDataDogRUM';
import { i18n } from './i18n';
import router from './router';
import { pinia } from './stores';

// Initialize MSW in development mode
const initApp = async () => {
    // Initialize Mock Service Worker in development
    if (import.meta.env.DEV) {
        const { setupMocks } = await import('./mock-api');
        const { isMswEnabled } = await import('./mock-api/fixtures-manager');
        await setupMocks({ enabled: isMswEnabled() });
    }

    const { init, disable } = useDataDogRUM();
    // only initialize DataDog RUM if the app id and client token are set
    if (import.meta.env.DATADOG_APP_ID != null && import.meta.env.DATADOG_CLIENT_TOKEN != null) {
        // Initialize DataDog RUM globally
        init({
            applicationId: import.meta.env.DATADOG_APP_ID,
            clientToken: import.meta.env.DATADOG_CLIENT_TOKEN,
            site: 'datadoghq.com',
            service: 'appv5',
            env: import.meta.env.DATADOG_ENV,
            version: import.meta.env.PACKAGE_VERSION,
        });
    } else {
        disable();
    }

    const app = createApp(App);
    app.use(VueFroala);
    app.use(pinia);
    app.use(router);
    window.CodeMirror = CodeMirror;
    app.use(i18n);
    app.use(PrimeVue, {
        theme: {
            preset: MyAuraPreset,
            options: {
                prefix: 'p',
                darkModeSelector: 'false',
                cssLayer: false,
            },
        },
        ripple: true,
        unstyled: false,
        pt: {},
    });
    app.use(ToastService);
    app.use(ConfirmationService);
    app.use(AnnounceKit);
    app.directive('ripple', Ripple);
    app.directive('tooltip', Tooltip);
    app.component('Editor', Editor);
    app.component('TabView', TabView);
    app.component('TabPanel', TabPanel);
    app.component('Accordion', Accordion);
    app.component('AccordionTab', AccordionTab);

    app.mount('#app');
};

initApp();
