<script setup lang="ts">
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import { useRouter } from 'vue-router';
import notFoundImage from '@/assets/404notfound-zero-state.png';
import { computed } from 'vue';

const router = useRouter();

const handleGoHome = () => {
  router.push('/');
};

const formattedMessage = computed(() => {
  // Using explicit newlines which will preserve whitespace in the rendered output
  return "The page you're looking for doesn't exist or has been moved. Please check the URL you are trying to access and your internet connection and try again.\n\n" +
         "If everything looks correct, we may be experiencing some technical challenges and will be back shortly.";
});
</script>

<template>
  <BravoZeroStateScreen
    title="Page Not Found"
    :message="formattedMessage"
    buttonLabel="Back to Home"
    buttonIcon="pi pi-home"
    :imageSrc="notFoundImage"
    imageAlt="Page not found"
    :actionHandler="handleGoHome"
  />
</template>

<style scoped>
/* Add styling to make the BravoZeroStateScreen preserve line breaks */
:deep(.zero-state-message) {
  white-space: pre-line;
}
</style> 