<script setup lang="ts">
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import { useRouter } from 'vue-router';
import notFoundImage from '@/assets/500error-zero-state.png';
import { computed } from 'vue';

const router = useRouter();

const handleGoHome = () => {
  router.push('/');
};

const formattedMessage = computed(() => {
  return "The application is currently not responding. Please check your internet connection and then click refresh or the button below.\n\n" +
         "If that didn't work, we may be experiencing some technical challenges and will be back shortly.";
});
</script>

<template>
  <BravoZeroStateScreen
    title="We seem to be experiencing an issue"
    :message="formattedMessage"
    buttonLabel="Back to Home"
    buttonIcon="pi pi-home"
    :imageSrc="notFoundImage"
    imageAlt="Page not found"
    :actionHandler="handleGoHome"
  />
</template>

<style scoped>
/* Add styling to make the BravoZeroStateScreen preserve line breaks */
:deep(.zero-state-message) {
  white-space: pre-line;
}
</style> 