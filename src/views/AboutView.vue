<script setup lang="ts">
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'

const handleClick = () => {
  alert('Bravo Component Library')
}

</script>

<template>
  <main>
    <BravoTitle1>About</BravoTitle1>
    <BravoButton label="Bravo Component Library" @click="handleClick" />
  </main>
</template>
