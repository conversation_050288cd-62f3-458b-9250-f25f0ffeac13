<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useAnalyticsAPI, type AnalyticsLoginResponse } from '@/composables/services/useAnalyticsAPI';
import { usePermissions } from '@/composables/usePermissions';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';

const analyticsAPI = useAnalyticsAPI();
const { can } = usePermissions();
const loading = ref(true);
const error = ref<string | null>(null);
const dashboardData = ref<AnalyticsLoginResponse | null>(null);
const iframeRef = ref<HTMLIFrameElement | null>(null);

// Check if user can manage dashboards (for UI display only)
const canManageDashboards = can.manageDashboards();

// Message listener for iframe communication
const handleMessage = (event: MessageEvent) => {
    console.log('Analytics: Received post message', event);

    const eventName = event.data.gdc?.event.name;

    if (eventName === 'listeningForApiToken' || eventName === 'apiTokenIsAboutToExpire') {
        if (dashboardData.value?.jwt) {
            const postMessageStructure = {
                gdc: {
                    product: 'dashboard',
                    event: {
                        name: 'setApiToken',
                        data: {
                            token: dashboardData.value.jwt,
                            type: 'jwt',
                            secondsBeforeTokenExpirationToEmitReminder: 60,
                        }
                    }
                }
            };
            
            console.log('Analytics: Sending JWT to embedded window');
            
            const origin = '*';
            const iframe = iframeRef.value?.contentWindow;
            if (iframe) {
                iframe.postMessage(postMessageStructure, origin);
            }
        }
    }
};

const fetchDashboardData = async () => {
    try {
        loading.value = true;
        error.value = null;
        
        const response = await analyticsAPI.fetchGoodDataLogin();
        
        if (response.success) {
            dashboardData.value = response;
        } else {
            error.value = 'Failed to authenticate with analytics service';
        }
    } catch (err) {
        console.error('Error loading analytics dashboard:', err);
        error.value = 'Failed to load analytics dashboard. Please try again.';
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    // Add message listener for iframe communication
    window.addEventListener('message', handleMessage, false);
    
    // Load the dashboard
    fetchDashboardData();
});

onUnmounted(() => {
    // Clean up message listener
    window.removeEventListener('message', handleMessage, false);
});
</script>

<template>
    <div class="analytics-view">


        <div class="analytics-content">
            <div v-if="loading" class="loading-state">
                <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
                <p>Loading analytics dashboard...</p>
            </div>

            <div v-else-if="error" class="error-state">
                <i class="pi pi-exclamation-triangle" style="font-size: 2rem; color: var(--red-500)"></i>
                <p>{{ error }}</p>
                <button @click="fetchDashboardData" class="retry-button">
                    <i class="pi pi-refresh"></i>
                    Retry
                </button>
            </div>

            <div v-else-if="dashboardData" class="dashboard-container">
                <iframe
                    ref="iframeRef"
                    :src="`${dashboardData.dashboard_uri}&apiTokenAuthentication=true`"
                    class="analytics-iframe"
                    frameborder="0"
                    allowfullscreen
                    title="Analytics Dashboard"
                />
            </div>
        </div>
    </div>
</template>

<style scoped>
.analytics-view {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--surface-0);
}

.analytics-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid var(--surface-200);
    background: var(--surface-50);
}

.analytics-subtitle {
    margin: 0.5rem 0 0 0;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
}

.management-badge {
    color: var(--primary-color);
    font-weight: 600;
}

.analytics-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.loading-state,
.error-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: var(--text-color-secondary);
}

.retry-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s;
}

.retry-button:hover {
    background: var(--primary-color-dark);
}

.dashboard-container {
    flex: 1;
    min-height: 0;
}

.analytics-iframe {
    width: 100%;
    height: 100%;
}
</style> 