<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoLargeTitle from '@services/ui-component-library/components/BravoTypography/BravoLargeTitle.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoInputMask from '@services/ui-component-library/components/BravoInputMask.vue'
import BravoSelectField from '@services/ui-component-library/components/BravoSelectField.vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoSquareImageCropper from '@services/ui-component-library/components/BravoSquareImageCropper.vue'
import InputGroup from '@services/ui-component-library/components/BravoInputGroup.vue'
import InputGroupAddon from 'primevue/inputgroupaddon';
import { useUserStore } from '@/stores/user';
import { useAuthStore } from '@/stores/auth';
import { useUserAPI } from '@/composables/services/useUserAPI';
import { useToast } from 'primevue/usetoast';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { setLocale } from '@/i18n';
import BravoPassword from '@services/ui-component-library/components/BravoPassword.vue'

const userStore = useUserStore();
const authStore = useAuthStore();
const userAPI = useUserAPI();
const toast = useToast();
const isEditMode = ref(false);
const isAccessEditMode = ref(false);
const isPasswordEditMode = ref(false);
const showAvatarCropper = ref(false);
const avatarCropperRef = ref<InstanceType<typeof BravoSquareImageCropper>>();
const { locale } = useI18n();
const version = import.meta.env.PACKAGE_VERSION;
const emailError = ref('');

// Available languages for selection
const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'de', name: 'Deutsch' },
];

// Find the current language name based on the locale code
const getCurrentLanguageName = (): string => {
    const lang = languages.find((l) => l.code === locale.value);
    return lang ? lang.name : 'English';
};

// Define common timezones
const timezones = [
    { value: 'Pacific/Honolulu', label: 'Hawaii Time' },
    { value: 'America/Anchorage', label: 'Alaska Time' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (US & Canada)' },
    { value: 'America/Denver', label: 'Mountain Time (US & Canada)' },
    { value: 'America/Chicago', label: 'Central Time (US & Canada)' },
    { value: 'America/New_York', label: 'Eastern Time (US & Canada)' },
    { value: 'America/Halifax', label: 'Atlantic Time (Canada)' },
    { value: 'Europe/London', label: 'London Time' },
    { value: 'Europe/Paris', label: 'Central European Time' },
    { value: 'Asia/Tokyo', label: 'Tokyo Time' },
    { value: 'Australia/Sydney', label: 'Sydney Time' },
];

// Find the timezone label from its value
const getTimezoneLabel = (value: string): string => {
    const timezone = timezones.find((tz) => tz.value === value);
    return timezone ? timezone.label : 'Unknown Timezone';
};

const editedUser = ref({
    first_name: '',
    last_name: '',
    email: '',
    avatar: '',
    nickname: '',
    phone_number: '',
    phone_ext: '',
    language: locale.value,
    time_zone: '',
});

const editedAccess = ref({
  role: '',
  teams: ''
});

// Password form data and errors
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const passwordErrors = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

onMounted(async () => {});

const startEditMode = () => {
    editedUser.value = {
        first_name: userStore.userData?.first_name || '',
        last_name: userStore.userData?.last_name || '',
        email: userStore.userData?.email || '',
        avatar: userStore.userData?.image || '',
        nickname: userStore.userData?.nickname || '',
        phone_number: userStore.userData?.phone_number || '',
        phone_ext: userStore.userData?.phone_ext || '',
        language: locale.value,
        time_zone: userStore.userData?.time_zone || 'America/Los_Angeles',
    };
    isEditMode.value = true;
};

const cancelEdit = () => {
    isEditMode.value = false;
};

const saveEdit = async (event: Event) => {
    event.preventDefault();

    // Validate email
    if (!validateEmail(editedUser.value.email)) {
        emailError.value = 'Please enter a valid email address';
        return;
    }

    try {
        // Update the application language if changed
        if (editedUser.value.language !== locale.value) {
            setLocale(editedUser.value.language as 'en' | 'es' | 'de');
        }

        // Get current user ID from the store
        const userId = userStore.userData?.id;

        if (!userId) {
            throw new Error('User ID not found');
        }

        // Call the API with the updated profile data
        const response = await userAPI.updateUserProfile({
            id: userId,
            first_name: editedUser.value.first_name,
            last_name: editedUser.value.last_name,
            email: editedUser.value.email,
            image: editedUser.value.avatar,
            nickname: editedUser.value.nickname,
            phone_number: editedUser.value.phone_number,
            phone_ext: editedUser.value.phone_ext,
            time_zone: editedUser.value.time_zone,
        });

        // Manually update the user store data
        if (userStore.userData) {
            userStore.userData.first_name = editedUser.value.first_name;
            userStore.userData.last_name = editedUser.value.last_name;
            userStore.userData.email = editedUser.value.email;
            userStore.userData.image = editedUser.value.avatar;
            userStore.userData.nickname = editedUser.value.nickname;
            userStore.userData.phone_number = editedUser.value.phone_number;
            userStore.userData.phone_ext = editedUser.value.phone_ext;
            userStore.userData.time_zone = editedUser.value.time_zone;
        }

        isEditMode.value = false;
        emailError.value = '';
        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Profile updated successfully',
            life: 3000,
        });
    } catch (error) {
        console.error('Failed to update profile:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update profile. Please try again.',
            life: 3000,
        });
    }
};

const validateEmail = (email: string): boolean => {
    // Simple validation: check if email is not empty and has basic email format
    if (!email) {
        emailError.value = 'Please enter your email address';
        return false;
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        emailError.value = 'Please enter a valid email address';
        return false;
    }

    emailError.value = '';
    return true;
};

// Clear error when email changes
const clearEmailError = () => {
    emailError.value = '';
};

const startEditAccessMode = () => {
    editedAccess.value = {
        role: 'Administrator', // This would come from the API in a real implementation
        teams: 'Support, Engineering', // This would come from the API in a real implementation
    };
    isAccessEditMode.value = true;
};

const cancelAccessEdit = () => {
    isAccessEditMode.value = false;
};

const saveAccessEdit = async (event: Event) => {
    event.preventDefault();
    try {
        // This would be an actual API call in a real implementation
        // await userAPI.updateUserAccess(editedAccess.value);

        isAccessEditMode.value = false;
        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Access updated successfully',
            life: 3000,
        });
    } catch (error) {
        console.error('Failed to update access:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update access. Please try again.',
            life: 3000,
        });
    }
};

// Avatar cropper functions
const openAvatarCropper = () => {
  showAvatarCropper.value = true;
};

// Handle the cropped image blob from the dumb component
const handleImageCropped = async (croppedBlob: Blob) => {
  try {
    const userId = userStore.userData?.id;
    
    if (!userId) {
      throw new Error('User ID not found');
    }

    // Call our authenticated uploadAvatar API
    const response = await userAPI.uploadAvatar(croppedBlob, userId);
    
    // Update user avatar in store if we have the avatar URL
    if (response.url_avatar && userStore.userData) {
      userStore.userData.image = response.url_avatar;
    }
    
    // Complete the submission process - this will stop loading and close modal
    avatarCropperRef.value?.completeSubmission();
    
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Avatar updated successfully',
      life: 3000
    });
  } catch (error) {
    console.error('Failed to update avatar:', error);
    
    // Complete the submission even on error to stop loading
    avatarCropperRef.value?.completeSubmission();
    
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to update avatar. Please try again.',
      life: 3000
    });
  }
};

// Handle submission complete event to close modal
const handleSubmissionComplete = () => {
  showAvatarCropper.value = false;
};

// Handle preview event - just show user feedback, don't submit
const handleImagePreview = (croppedBlob: Blob) => {
  // Optional: Could show some feedback that preview is ready
  console.log('Preview generated:', croppedBlob);
  // The component itself will show the preview, so we don't need to do anything special here
};

const handleCropError = (error: any) => {
  console.error('Crop error:', error);
  toast.add({
    severity: 'error',
    summary: 'Error',
    detail: 'Failed to crop image. Please try again.',
    life: 3000
  });
};

const startPasswordEditMode = () => {
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  passwordErrors.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  isPasswordEditMode.value = true;
};

const cancelPasswordEdit = () => {
  isPasswordEditMode.value = false;
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  passwordErrors.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
};

const validatePasswordForm = (): boolean => {
  let isValid = true;
  
  // Clear previous errors
  passwordErrors.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  
  // Validate old password
  if (!passwordForm.value.oldPassword) {
    passwordErrors.value.oldPassword = 'Current password is required';
    isValid = false;
  }
  
  // Validate new password
  if (!passwordForm.value.newPassword) {
    passwordErrors.value.newPassword = 'New password is required';
    isValid = false;
  } else if (passwordForm.value.newPassword.length < 8) {
    passwordErrors.value.newPassword = 'Password must be at least 8 characters long';
    isValid = false;
  }
  
  // Validate confirm password
  if (!passwordForm.value.confirmPassword) {
    passwordErrors.value.confirmPassword = 'Please confirm your new password';
    isValid = false;
  } else if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    passwordErrors.value.confirmPassword = 'Passwords do not match';
    isValid = false;
  }
  
  return isValid;
};

const savePasswordEdit = async (event: Event) => {
  event.preventDefault();
  
  if (!validatePasswordForm()) {
    return;
  }
  
  try {
    // Get current user ID from the store
    const userId = userStore.userData?.id;
    
    if (!userId) {
      throw new Error('User ID not found');
    }

    // Call the API to change password
    await userAPI.changePassword({
      id: userId,
      currentPassword: passwordForm.value.oldPassword,
      newPassword: passwordForm.value.newPassword
    });
    
    isPasswordEditMode.value = false;
    passwordForm.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
    passwordErrors.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
    
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Password changed successfully',
      life: 3000
    });
  } catch (error) {
    console.error('Failed to change password:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to change password. Please try again.',
      life: 3000
    });
  }
};

// Clear password errors when fields change
const clearPasswordError = (field: 'oldPassword' | 'newPassword' | 'confirmPassword') => {
  passwordErrors.value[field] = '';
};

</script>

<!-- This is a layout placeholder for the user profile page and will be replaced with the actual user profile page -->

<template>
  <div class="user-profile">
    <div class="container">
      <div class="profile-title">
        <BravoTitlePage>Profile</BravoTitlePage>
        <div class="avatar-wrapper" @click="openAvatarCropper">
          <BravoAvatar 
            :image="userStore.userData?.image || ''" 
            class="profile-avatar" 
          />
          <div class="avatar-overlay">
            <i class="pi pi-pencil"></i>
          </div>
        </div>
      </div>
      
      <div class="section">
        <div class="section-header">
          <BravoLargeTitle>Details</BravoLargeTitle>
          <div class="button-group">
            <BravoButton 
              v-if="isEditMode"
              :label="'Cancel'" 
              severity="secondary" 
              @click="cancelEdit"
            />
            <BravoButton 
              v-if="isEditMode"
              type="submit"
              form="profile-form"
              label="Save" 
              severity="primary"
            />
            <BravoButton 
              v-if="!isEditMode"
              label="Edit Details" 
              severity="secondary" 
              @click="startEditMode"
            />
          </div>
        </div>
        <div class="section-content">
          <form id="profile-form" @submit="saveEdit" class="access-list">
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="First Name" />
              </div>
              <div class="input-container">
                <BravoInputText 
                  v-if="isEditMode"
                  v-model="editedUser.first_name"
                  placeholder="First Name"
                  required
                />
                <span v-else class="field-text">
                  {{ userStore.userData?.first_name }}
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Last Name" />
              </div>
              <div class="input-container">
                <BravoInputText 
                  v-if="isEditMode"
                  v-model="editedUser.last_name"
                  placeholder="Last Name"
                  required
                />
                <span v-else class="field-text">
                  {{ userStore.userData?.last_name }}
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Email" />
              </div>
              <div class="input-container">
                <div v-if="isEditMode" class="email-field-container">
                  <InputGroup>
                    <InputGroupAddon>
                      <i class="pi pi-envelope"></i>
                    </InputGroupAddon>
                    <BravoInputText 
                      v-model="editedUser.email"
                      type="email"
                      placeholder="Email"
                      required
                      @input="clearEmailError"
                      :class="{'error-input': emailError}"
                    />
                  </InputGroup>
                  <div v-if="emailError" class="error-message">
                    {{ emailError }}
                  </div>
                </div>
                <span v-else class="field-text">
                  {{ userStore.userData?.email }}
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Phone Number" />
              </div>
              <div class="input-container">
                <div v-if="isEditMode" class="phone-field-container">
                  <InputGroup>
                    <InputGroupAddon>
                      <i class="pi pi-phone"></i>
                    </InputGroupAddon>
                    <BravoInputMask
                      v-model="editedUser.phone_number"
                      mask="+****************"
                      placeholder="+****************"
                      class="phone-input"
                    />
                  </InputGroup>
                  <BravoInputText
                    v-model="editedUser.phone_ext"
                    placeholder="Ext."
                    class="extension-input"
                  />
                </div>
                <span v-else class="field-text">
                  {{ userStore.userData?.phone_number || '+****************' }}
                  <span v-if="userStore.userData?.phone_ext">(x{{ userStore.userData.phone_ext }})</span>
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Username" />
              </div>
              <div class="input-container">
                <div v-if="isEditMode" class="username-field-container">
                  <InputGroup>
                    <InputGroupAddon>
                      @
                    </InputGroupAddon>
                    <BravoInputText 
                      v-model="editedUser.nickname"
                      placeholder="Username"
                      required
                    />
                  </InputGroup>
                </div>
                <span v-else class="field-text">
                  {{ userStore.userData?.nickname }}
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Language" />
              </div>
              <div class="input-container">
                <div v-if="isEditMode" class="language-field-container">
                  <BravoSelectField
                    id="language-select"
                    v-model="editedUser.language"
                    :options="languages.map(lang => ({ label: lang.name, value: lang.code }))"
                    placeholder="Select a language"
                    dataTestId="language-select"
                  />
                </div>
                <span v-else class="field-text">
                  {{ getCurrentLanguageName() }}
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="TimeZone" />
              </div>
              <div class="input-container">
                <div v-if="isEditMode" class="timezone-field-container">
                  <BravoSelectField
                    id="timezone-select"
                    v-model="editedUser.time_zone"
                    :options="timezones.map(tz => ({ label: tz.label, value: tz.value }))"
                    placeholder="Select a timezone"
                    dataTestId="timezone-select"
                  />
                </div>
                <span v-else class="field-text">
                  {{ getTimezoneLabel(userStore.userData?.time_zone || 'America/Los_Angeles') }}
                </span>
              </div>
            </div>
          </form>
        </div>
      </div>
      
      <div class="section">
        <div class="section-header">
          <BravoLargeTitle>Access</BravoLargeTitle>
          <div class="button-group">
            <BravoButton 
              v-if="isAccessEditMode"
              :label="'Cancel'" 
              severity="secondary" 
              @click="cancelAccessEdit"
            />
            <BravoButton 
              v-if="isAccessEditMode"
              type="submit"
              form="access-form"
              label="Save" 
              severity="primary"
            />
            <BravoButton 
              v-if="!isAccessEditMode"
              label="Edit Access" 
              severity="secondary" 
              @click="startEditAccessMode"
            />
          </div>
        </div>
        <div class="section-content">
          <form id="access-form" @submit="saveAccessEdit" class="access-list">
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Role" />
              </div>
              <div class="input-container">
                <BravoSelectField
                  v-if="isAccessEditMode"
                  id="role-select"
                  v-model="editedAccess.role"
                  :options="[
                    { label: 'Administrator', value: 'Administrator' },
                    { label: 'Manager', value: 'Manager' },
                    { label: 'Agent', value: 'Agent' }
                  ]"
                  placeholder="Select a role"
                  dataTestId="role-select"
                />
                <span v-else class="field-text">
                  Administrator
                </span>
              </div>
            </div>
            <div class="access-item">
              <div class="label-container">
                <BravoLabel text="Teams" />
              </div>
              <div class="input-container">
                <BravoSelectField
                  v-if="isAccessEditMode"
                  id="teams-select"
                  v-model="editedAccess.teams"
                  :options="[
                    { label: 'Support', value: 'Support' },
                    { label: 'Engineering', value: 'Engineering' },
                    { label: 'Sales', value: 'Sales' }
                  ]"
                  placeholder="Select teams"
                  dataTestId="teams-select"
                />
                <span v-else class="field-text">
                  Support, Engineering
                </span>
              </div>
            </div>
          </form>
        </div>
      </div>
      
      <div class="section">
        <div class="section-header">
          <BravoLargeTitle>Password</BravoLargeTitle>
          <div class="button-group">
            <BravoButton 
              v-if="isPasswordEditMode"
              :label="'Cancel'" 
              severity="secondary" 
              @click="cancelPasswordEdit"
            />
            <BravoButton 
              v-if="isPasswordEditMode"
              type="submit"
              form="password-form"
              label="Save" 
              severity="primary"
            />
            <BravoButton 
              v-if="!isPasswordEditMode"
              label="Change Password" 
              severity="secondary" 
              @click="startPasswordEditMode"
            />
          </div>
        </div>
        <div class="section-content">
          <form id="password-form" @submit="savePasswordEdit" class="access-list">
            <div v-if="!isPasswordEditMode" class="access-item">
              <div class="label-container">
                <BravoLabel text="Password" />
              </div>
              <div class="input-container">
                <span class="field-text password-value">••••••••</span>
              </div>
            </div>
            <div v-if="isPasswordEditMode" class="access-item">
              <div class="label-container">
                <BravoLabel text="Old Password" />
              </div>
              <div class="input-container">
                <div class="password-field-container">
                  <BravoPassword 
                    v-model="passwordForm.oldPassword"
                    placeholder="Current Password"
                    required
                    @input="clearPasswordError('oldPassword')"
                    :class="{'error-input': passwordErrors.oldPassword}"
                  />
                  <div v-if="passwordErrors.oldPassword" class="error-message">
                    {{ passwordErrors.oldPassword }}
                  </div>
                </div>
              </div>
            </div>
            <div v-if="isPasswordEditMode" class="access-item">
              <div class="label-container">
                <BravoLabel text="New Password" />
              </div>
              <div class="input-container">
                <div class="password-field-container">
                  <BravoPassword 
                    v-model="passwordForm.newPassword"
                    placeholder="New Password"
                    required
                    @input="clearPasswordError('newPassword')"
                    :class="{'error-input': passwordErrors.newPassword}"
                  />
                  <div v-if="passwordErrors.newPassword" class="error-message">
                    {{ passwordErrors.newPassword }}
                  </div>
                </div>
              </div>
            </div>
            <div v-if="isPasswordEditMode" class="access-item">
              <div class="label-container">
                <BravoLabel text="Confirm New Password" />
              </div>
              <div class="input-container">
                <div class="password-field-container">
                  <BravoPassword 
                    v-model="passwordForm.confirmPassword"
                    placeholder="Confirm New Password"
                    required
                    @input="clearPasswordError('confirmPassword')"
                    :class="{'error-input': passwordErrors.confirmPassword}"
                  />
                  <div v-if="passwordErrors.confirmPassword" class="error-message">
                    {{ passwordErrors.confirmPassword }}
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
      
      <div class="section">
        <div class="section-header">
          <BravoLargeTitle>Notifications</BravoLargeTitle>
          <BravoButton label="Edit Notifications" severity="secondary" />
        </div>
        <div class="section-content">
          <!-- Notifications content will go here -->
        </div>
      </div>
      
      <div class="version-container">
        <BravoBody class="secondary-text">Application Version: {{ version }}</BravoBody>
      </div>
    </div>

    <!-- Avatar Cropper Dialog -->
    <BravoDialog
      v-model:visible="showAvatarCropper"
      header="Update Profile Picture"
      :style="{ width: '50rem', maxWidth: '90vw' }"
      :modal="true"
      :closable="true"
    >
      <BravoSquareImageCropper
        @crop="handleImagePreview"
        @cropped="handleImageCropped"
        @crop-error="handleCropError"
        @submission-complete="handleSubmissionComplete"
        :waitForApiResponse="true"
        ref="avatarCropperRef"
      />
    </BravoDialog>
  </div>
</template>

<style scoped>
.user-profile {
    display: flex;
    justify-content: center;
    width: 100%;
}

.container {
    width: 800px;
    max-width: 800px;
    box-sizing: border-box;
    padding: 2rem;
}

.profile-title {
    margin: 2rem 0rem 2rem 0rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.avatar-wrapper {
    position: relative;
    cursor: pointer;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  transition: opacity 0.2s;
  display: block;
}

.profile-avatar :deep(img) {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.2s;
}

.avatar-overlay i {
    color: white;
    font-size: 1.5rem;
}

.avatar-wrapper:hover .avatar-overlay {
    opacity: 1;
}

.avatar-wrapper:hover .profile-avatar {
    opacity: 0.8;
}

.section {
    margin-bottom: 2rem;
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-content {
    padding: 1.5rem 0rem;
    margin-top: 1rem;
}

.access-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.access-list form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.access-item {
    display: flex;
    align-items: center;
}

.label-container {
  width: 140px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
}

.label-container :deep(.p-component) {
    margin-bottom: 0;
}

.input-container {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 38px; /* Approximately the height of BravoInputText */
}

.input-container :deep(.p-inputtext) {
    width: 100%;
    max-width: 300px;
}

.access-value {
    color: var(--text-color);
}

.password-value {
    font-weight: bold;
    letter-spacing: 2px;
}

.note {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.edit-input,
select.edit-input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    max-width: 300px;
}

.edit-input:focus,
select.edit-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 56, 202, 0.1);
}

.edit-input:invalid {
    border-color: var(--error-color, #dc2626);
}

.button-group {
    display: flex;
    gap: 0.5rem;
}

.phone-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    width: 100%;
    max-width: 300px;
}

.phone-input {
    flex: 3;
}

.extension-input {
    flex: 1;
}

.phone-input:deep(.p-inputtext),
.extension-input:deep(.p-inputtext) {
    width: 100%;
}

.version-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.secondary-text {
    color: var(--text-color-secondary);
}

.field-text {
    color: var(--text-color);
    display: inline-block;
    padding: 0.5rem 0;
    font-size: 1rem;
    line-height: 1.5;
}

.email-field-container {
    position: relative;
    width: 100%;
    max-width: 300px;
}

.error-message {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    color: var(--red-700, #dc2626);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    line-height: 1;
}

.error-input {
    border-color: var(--red-700, #dc2626) !important;
}

.error-input:deep(.p-inputtext) {
    border-color: var(--red-700, #dc2626) !important;
}

.email-field-container:has(.error-input):deep(.p-inputgroup-addon) {
    border-color: var(--red-700, #dc2626) !important;
}

.username-field-container {
    position: relative;
    width: 100%;
    max-width: 300px;
}

.phone-field-container {
    position: relative;
    width: 100%;
    max-width: 300px;
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.phone-field-container :deep(.p-inputgroup) {
    flex: 3;
}

.extension-input {
    flex: 1;
}

.language-field-container,
.timezone-field-container {
    position: relative;
    width: 100%;
    max-width: 300px;
}

.language-field-container :deep(.p-component),
.timezone-field-container :deep(.p-component) {
    width: 100%;
}

.password-field-container {
  position: relative;
  width: 100%;
  max-width: 300px;
}
</style>
