import type { MetaResponse } from '@/composables/services/useMetaAPI';

/**
 * @deprecated Use the useMetaAPI composable from '@/composables/services' instead
 * This class is kept for backward compatibility but should not be used in new code
 */
class MetaAPI {
    constructor() {
        console.warn('MetaAPI is deprecated. Use useMetaAPI composable from @/composables/services instead.');
    }

    private logDeprecationWarning(methodName: string): any {
        console.warn(`MetaAPI.${methodName} is deprecated. Use useMetaAPI composable from @/composables/services instead.`);
        return { success: false, message: 'Method deprecated. Use useMetaAPI composable.' };
    }

    async fetchMetaData(): Promise<any> {
        return this.logDeprecationWarning('fetchMetaData');
    }

    async fetchPartnerMetaData(): Promise<any> {
        return this.logDeprecationWarning('fetchPartnerMetaData');
    }
}

export { MetaAP<PERSON> };
export type { MetaResponse };
export const metaAPI = new MetaAPI(); 