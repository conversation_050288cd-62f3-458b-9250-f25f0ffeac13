/**
 * @typedef {{headers: object, params: object}} RequestInfo
 */
class AdminHttpClient {
  #baseUrl;
  /**
   * @param {string} baseUrl
   */
  constructor(baseUrl) {
    this.#baseUrl = baseUrl;
    if (!window.fetch) {
      throw new Error("Unable to create AdminHttpClient: Fetch API not found.");
    }
  }
  /**
   *
   * @param {string} endpoint
   * @param {"GET"|"POST"|"PUT"|"PATCH"|"DELETE"} method
   * @param {object|null} body
   * @param {RequestInfo} requestInfo
   * @returns {Promise<Response>}
   */
  async request(endpoint, method, body = null, baseRequestInfo = {}) {
    const { params, ...requestInfo } = baseRequestInfo;
    const url = new URL(this.#baseUrl);

    // Ensure endpoint starts with a slash and baseURL doesn't end with one
    endpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
    url.pathname =
      (url.pathname.endsWith("/") ? url.pathname.slice(0, -1) : url.pathname) +
      endpoint;

    // Append params to url
    let searchParams = url.searchParams.toString();
    if (searchParams.length) {
      searchParams += "&" + new URLSearchParams(params).toString();
    } else {
      searchParams = new URLSearchParams(params).toString();
    }
    url.search = new URLSearchParams(searchParams);

    // Most endpoints receive urlencoded form data
    const methodsWithBody = ["POST", "PUT", "PATCH"];
    if (methodsWithBody.includes(method) && body) {
      requestInfo.headers = {
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        ...requestInfo.headers,
      };
    }
    try {
      const response = await fetch(url.toString(), {
        method,
        headers: BoomtownHeaders.getRequestHeaders('', 'AdminHttpClient'),
        credentials: "include",
        body: this._serializeBody(body, requestInfo.headers["Content-Type"]),
      });
      if (!response.ok) {
        throw new Error(response.statusText);
      }
      return await response.json();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  /**
   *
   * @param {string} endpoint
   * @param {RequestInfo} requestInfo
   * @returns {Promise<object>}
   */
  get(endpoint, requestInfo = {}) {
    return this.request(endpoint, "GET", null, requestInfo);
  }

  /**
   *
   * @param {string} endpoint
   * @param {RequestInfo} requestInfo
   * @returns {Promise<object>}
   */
  post(endpoint, body, requestInfo = {}) {
    return this.request(endpoint, "POST", body, requestInfo);
  }

  /**
   *
   * @param {string} endpoint
   * @param {RequestInfo} requestInfo
   * @returns {Promise<object>}
   */
  put(endpoint, body, requestInfo = {}) {
    return this.request(endpoint, "PUT", body, requestInfo);
  }

  /**
   *
   * @param {string} endpoint
   * @param {RequestInfo} requestInfo
   * @returns {Promise<object>}
   */
  patch(endpoint, body, requestInfo = {}) {
    return this.request(endpoint, "PATCH", body, requestInfo);
  }

  /**
   *
   * @param {string} endpoint
   * @param {RequestInfo} requestInfo
   * @returns {Promise<object>}
   */
  delete(endpoint, requestInfo = {}) {
    return this.request(endpoint, "DELETE", null, requestInfo);
  }
  _serializeBody(body, contentType) {
    if (!body) {
      return null;
    }
    if (contentType === "application/x-www-form-urlencoded") {
      return new URLSearchParams(body).toString();
    } else if (contentType === "application/json") {
      return JSON.stringify(body);
    }
    return body;
  }

  setBaseUrl(baseUrl) {
    this.#baseUrl = baseUrl;
  }
}

export default AdminHttpClient; 