import { HttpClient } from './httpClient';
import { fetchClientInstanceId } from '@/utils/clientInstance';
import router from '@/router';
import type { AuthStoreInterface } from '@/stores/auth';

// Singleton instance of HttpClient
let _httpClient: HttpClient | null = null;

/**
 * Get the HttpClient instance, creating it if it doesn't exist
 * @param authStore Optional auth store reference to set on the client
 */
export function getHttpClient(authStore: AuthStoreInterface): HttpClient {
    if (!_httpClient) {
        // Create a new httpClient instance with client instance ID
        const clientInstanceId = fetchClientInstanceId();

        _httpClient = new HttpClient(
            import.meta.env.VITE_BOOMTOWN_API_HOST,
            clientInstanceId,
            (isAuthenticated: boolean) => {
                if (!isAuthenticated) {
                    // navigate to login
                    router.push('/login');
                }
                // No redirect if authenticated
            }
        );
    }
    _httpClient.setAuthStore(authStore);

    return _httpClient;
}

/**
 * For testing: replace the HttpClient instance
 */
export function setHttpClient(client: HttpClient): void {
    _httpClient = client;
}
