import { getHttpClient } from './httpClientProvider';
import type { AuthStoreInterface } from '@/stores/auth';
import type { View, SettingsResponse } from '@/composables/services/useSettingsAPI';

/**
 * @deprecated Use the useSettingsAPI composable from '@/composables/services' instead
 * This class is kept for backward compatibility but should not be used in new code
 */
class SettingsAPI {
    constructor() {
        console.warn('SettingsAPI is deprecated. Use useSettingsAPI composable from @/composables/services instead.');
    }

    private logDeprecationWarning(methodName: string): any {
        console.warn(`SettingsAPI.${methodName} is deprecated. Use useSettingsAPI composable from @/composables/services instead.`);
        return { success: false, message: 'Method deprecated. Use useSettingsAPI composable.' };
    }

    async fetchViews(): Promise<View[]> {
        return this.logDeprecationWarning('fetchViews');
    }
}

export { SettingsAPI };
export type { View, SettingsResponse };
export const settingsAPI = new SettingsAPI();
