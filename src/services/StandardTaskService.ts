import { v4 as uuidv4 } from 'uuid';
import type { ServiceResponse, ServiceError, FetchTasksParams } from '../types/services.ts';
import type { Task } from '../types';
import type { TaskService } from './TaskService';

/**
 * Standard implementation of TaskService using REST API
 */
export class StandardTaskService implements TaskService {
  private readonly tasksUrl: string;

  constructor(tasksUrl = import.meta.env.VITE_TASKS_SERVICE_URL) {
    console.log('Initializing StandardTaskService tasksUrl', tasksUrl);
    this.tasksUrl = tasksUrl?.endsWith('/') ? tasksUrl.slice(0, -1) : tasksUrl;
  }

  private createServiceError(error: unknown): ServiceError {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    }
    return {
      name: 'ServiceError',
      message: 'An unknown error occurred',
      context: error,
    };
  }

  async fetchUserTasks(params: FetchTasksParams): Promise<ServiceResponse<Task[]>> {
    try {
      const url = new URL(this.tasksUrl);
      url.searchParams.append('orgId', params.orgId);
      if (params.userId) {
        url.searchParams.append('userId', params.userId);
      }
      url.searchParams.append('x', Date.now().toString());

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'x-boomtown-request-id': uuidv4(),
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      const tasks = Array.isArray(result.data) ? result.data : Array.isArray(result) ? result : [];

      return {
        success: true,
        data: tasks,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error: this.createServiceError(error),
      };
    }
  }

  async fetchTeamTasks(params: FetchTasksParams): Promise<ServiceResponse<Task[]>> {
    try {
      const url = new URL(this.tasksUrl);
      url.searchParams.append('orgId', params.orgId);
      if (params.teamId) {
        url.searchParams.append('teamId', params.teamId);
      }
      url.searchParams.append('x', Date.now().toString());

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'x-boomtown-request-id': uuidv4(),
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      const tasks = Array.isArray(result.data) ? result.data : Array.isArray(result) ? result : [];

      return {
        success: true,
        data: tasks,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error: this.createServiceError(error),
      };
    }
  }
}