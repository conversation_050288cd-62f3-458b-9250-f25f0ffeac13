interface MFEConfig {
  key: string;
  splitName: string;
  css: string[];
  esModules: string[];
}

const isDevelopment = import.meta.env.DEV;
const CDN_BASE_URL = isDevelopment ? '' : 'https://cdn.stage.goboomtown.com'; // Empty for development

const SERVICES: Record<string, MFEConfig> = {
  'journey-builder': {
    key: 'journey-builder',
    splitName: 'Journeys_List',
    css: ['cdn:/journeys/latest/service-journeys-ui.css'],
    esModules: ['cdn:/journeys/latest/service-journeys-ui.es.js']
  },
  'journey-workflow': {
    key: 'journey-workflow',
    splitName: 'Journey_Workflow',
    css: ['cdn:/journeys/latest/service-journeys-ui.css'],
    esModules: ['cdn:/journeys/latest/service-journeys-ui.es.js']
  },
  'tasksmywork': {
    key: 'tasksmywork',
    splitName: 'Tasks_on_my_work',
    css: ['cdn:/tasksmywork/service-tasks-mywork-ui.css'],
    esModules: ['cdn:/tasksmywork/service-tasks-mywork-ui.es.js']
  },
  'tasksvite': {
    key: 'tasksvite',
    splitName: 'Tasks',
    css: ['cdn:/tasks-vite/service-tasks-ui-vite.css'],
    esModules: ['cdn:/tasks-vite/service-tasks-ui-vite.es.js']
  },
  'connectors': {
    key: 'connectors',
    splitName: 'Connector_Registry',
    css: ['cdn:/connectors/service-connectors-ui.css'],
    esModules: ['cdn:/connectors/service-connectors-ui.es.js']
  },
  'customer-summarizer': {
    key: 'customer-summarizer',
    splitName: 'Customers_Summary',
    css: ['cdn:/generative-summarizer/service-generative-summarizer-ui.css'],
    esModules: ['cdn:/generative-summarizer/service-generative-summarizer-ui.es.js']
  }
};

class MFELoaderService {
  private loadedScripts: Set<string> = new Set();
  private loadedStyles: Set<string> = new Set();

  private resolveCdnUrl(path: string): string {
    if (path.startsWith('cdn:/')) {
      // In development, use relative path to leverage Vite's proxy
      if (isDevelopment) {
        return '/' + path.slice(5);
      }
      return `${CDN_BASE_URL}/${path.slice(5)}`;
    }
    return path;
  }

  async loadMFE(mfeName: string): Promise<void> {
    const config = SERVICES[mfeName];
    if (!config) {
      throw new Error(`MFE ${mfeName} not found in configurations`);
    }

    console.debug(`Loading MFE ${mfeName}...`);
    
    try {
      // Load CSS files first
      await Promise.all(
        config.css.map(cssPath => this.loadStyleInternal(this.resolveCdnUrl(cssPath)))
      );

      // Then load ES modules
      await Promise.all(
        config.esModules.map(modulePath => this.loadScriptInternal(this.resolveCdnUrl(modulePath)))
      );

      
      // Wait for the adapter to be ready
      await this.waitForDynamicWidgetAdapter();
    } catch (error) {
      console.error(`Failed to load MFE ${mfeName}:`, error);
      throw error;
    }
  }

  private waitForDynamicWidgetAdapter(): Promise<void> {
    return new Promise((resolve) => {
      if (window.DynamicWidgetAdapter) {
        resolve();
        return;
      }

      document.addEventListener('DynamicWidgetAdapterLoaded', () => {
        resolve();
      }, { once: true });
    });
  }

  // Expose loadScript and loadStyle as public methods for dynamic loading
  async loadScript(url: string): Promise<void> {
    return this.loadScriptInternal(url);
  }

  async loadStyle(url: string): Promise<void> {
    return this.loadStyleInternal(url);
  }

  private loadScriptInternal(url: string): Promise<void> {
    if (this.loadedScripts.has(url)) {
      console.debug(`Script already loaded: ${url}`);
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url;
      script.type = 'module';
      
      script.onload = () => {
        this.loadedScripts.add(url);
        resolve();
      };
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
      
      document.head.appendChild(script);
    });
  }

  private loadStyleInternal(url: string): Promise<void> {
    if (this.loadedStyles.has(url)) {
      console.debug(`Style already loaded: ${url}`);
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.href = url;
      link.rel = 'stylesheet';
      
      link.onload = () => {
        this.loadedStyles.add(url);
        resolve();
      };
      link.onerror = () => reject(new Error(`Failed to load stylesheet: ${url}`));
      
      document.head.appendChild(link);
    });
  }

  getMFEConfig(mfeName: string): MFEConfig {
    const config = SERVICES[mfeName];
    if (!config) {
      throw new Error(`MFE ${mfeName} not found in configurations`);
    }
    return config;
  }
}

export const mfeLoader = new MFELoaderService();
export type { MFEConfig }; 