import type { ServiceResponse, FetchTasksParams } from '../types/services.ts';
import type { Task } from '../types';

/**
 * Interface defining the contract for task operations
 */
export interface TaskService {
  /**
   * Retrieves tasks assigned to a specific user
   * @param params - Parameters including orgId and userId
   * @returns Promise resolving to user's tasks
   */
  fetchUserTasks(params: FetchTasksParams): Promise<ServiceResponse<Task[]>>;

  /**
   * Retrieves tasks assigned to a team
   * @param params - Parameters including orgId and teamId
   * @returns Promise resolving to team's tasks
   */
  fetchTeamTasks(params: FetchTasksParams): Promise<ServiceResponse<Task[]>>;
}