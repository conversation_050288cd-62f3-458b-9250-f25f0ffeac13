import { useAuthStore } from '../stores/auth'
import { v4 as uuidv4 } from 'uuid'

// Global headers object
const BoomtownHeaders = {
  csrfToken: '',
  clientInstanceId: '',
  getRequestHeaders(contentType = 'application/json', source = '') {
    const headers: Record<string, string> = {
      'x-boomtown-client-instance-id': this.clientInstanceId,
      'x-request-id': uuidv4(),
      'content-type': contentType,
    }
    if (this.csrfToken) {
      headers['x-boomtown-csrf-token'] = this.csrfToken
    }
    if (source) {
      headers['x-boomtown-source'] = source
    }
    return headers
  },
}

// Call this whenever the CSRF token or clientInstanceId changes
export function updateBoomtownHeaders(csrfToken: string, clientInstanceId: string) {
  BoomtownHeaders.csrfToken = csrfToken
  BoomtownHeaders.clientInstanceId = clientInstanceId
}

// Extend the Window interface for TypeScript
declare global {
  interface Window {
    BoomtownHeaders: typeof BoomtownHeaders
  }
}

if (typeof window !== 'undefined') {
  window.BoomtownHeaders = BoomtownHeaders
}

export default BoomtownHeaders 