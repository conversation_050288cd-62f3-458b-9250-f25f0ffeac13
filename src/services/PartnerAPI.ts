import type { Product, ProductListResponse } from '@/types/partner';

/**
 * @deprecated Use the usePartnerAPI composable from '@/composables/services' instead
 * This class is kept for backward compatibility but should not be used in new code
 */
export const partnerAPI = {
    async fetchProducts(): Promise<ProductListResponse> {
        console.warn('partnerAPI.fetchProducts is deprecated. Use usePartnerAPI composable from @/composables/services instead.');
        // Return a mock response with required properties to satisfy the type
        return {
            success: false,
            message: 'Method deprecated. Use usePartnerAPI composable.',
            partners_product_list: {
                totalCount: '0',
                results: []
            },
            current_server_time: new Date().toISOString()
        } as ProductListResponse;
    }
}; 