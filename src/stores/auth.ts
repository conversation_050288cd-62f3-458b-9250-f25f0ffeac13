
import { defineStore } from 'pinia';
import {ref, watch} from 'vue';
import router from '@/router';
import { Base64 } from '@/utils/Base64';
import { v4 as uuidv4 } from 'uuid';
import { useUserStore } from './user';
import { fetchClientInstanceId } from '@/utils/clientInstance';
import { HttpBaseClient } from '@/services/httpBaseClient';
import { updateBoomtownHeaders } from '@/services/BoomtownHeaders'
import {useDataDogRUM} from "@/composables/useDataDogRUM";

export interface AuthStoreInterface {
    isAuthenticated: boolean;
    csrfToken: string;
}

/**
 * Login credentials for authentication
 */
export interface LoginCredentials {
    email: string;
    password: string;
}

/**
 * Login response structure
 */
export interface LoginResponse {
    success: boolean;
    authenticated?: boolean;
    message?: string;
    csrf_token?: string;
    users?: {
        id: string;
        email: string;
        full_name?: string;
        [key: string]: any;
    };
    xmpp?: {
        password: string;
        [key: string]: any;
    };
    editor_key?: string;
    perm_defs?: {
        [key: string]: number;
    };
    users_group?: {
        [key: string]: number;
    };
    all_access?: string[];
    password_policy?: {
        expiration: string;
        idle_timeout: number;
        lockout: string;
        pattern: string;
        pattern_regex: string;
        rotation: string;
        session_terminate: string;
    };
    screens_url?: string;
};

export interface resetCredentials {
    email: string;
}

export interface changeCredentials {
    email: string;
    password: string;
    confirm_password: string;
    token: string;
}

export interface ResetPasswordResponse {
    success: boolean
    message?: string
    // add any other fields your API returns
  }


export const useAuthStore = defineStore('auth', () => {
    const isAuthenticated = ref(false);
    const isInitialized = ref(false);
    const csrfToken = ref('');
    const clientInstanceId = ref(fetchClientInstanceId());
    const apiBaseUrl = ref(import.meta.env.VITE_BOOMTOWN_API_HOST);
    const userEmail = ref('');
    const editorKey = ref('');
    const allAccess = ref<string[]>([]);

    const { setUser: setDataDogUser, clearUser: clearDataDogUser } = useDataDogRUM();
    watch(isAuthenticated, (newVal) => {
        if(!newVal) {
            // User is no longer authenticated, clear DataDog user
            clearDataDogUser();
            return;
        }
        setTimeout(() => {
            const userStore = useUserStore();
            setDataDogUser({
                id: userStore.userData?.id ?? null,
                name: userStore.userData?.full_name ?? null,
                email: userStore.userData?.email ?? null,
                org_id: userStore.userData?.object_id ?? null,
                org_name: userStore.orgData?.name ?? null,
            });
        }, 0);
    });

    // Create base HTTP client for auth-related API calls
    const baseHttpClient = new HttpBaseClient(apiBaseUrl.value);

    // Initializes the auth state
    async function initialize(): Promise<void> {
        if (isInitialized.value) return;

        try {
            const response = await refreshCsrfToken();
            if (response) {
                isAuthenticated.value = response.authenticated || false;

                if (response.csrf_token) {
                    csrfToken.value = response.csrf_token;
                }

                if (response.users?.email) {
                    userEmail.value = response.users.email;
                }

                if (response.authenticated) {
                    startTokenRefresh();
                }
            }
        } catch (error) {
            console.error('Failed to initialize auth state:', error);
            isAuthenticated.value = false;
        }

        isInitialized.value = true;
    }

    // Refresh CSRF token
    async function refreshCsrfToken(): Promise<LoginResponse | null> {
        try {
            console.debug('Fetching user status...');

            const headers: Record<string, string> = {
                'x-boomtown-client-instance-id': clientInstanceId.value,
                'x-request-id': uuidv4(),
                'content-type': 'application/json',
            };

            if (csrfToken.value) {
                headers['x-boomtown-csrf-token'] = csrfToken.value;
            }

            try {
                const data = await baseHttpClient.get<LoginResponse>(
                    'admin/v4/core/',
                    { sAction: 'userStatus' },
                    headers
                );

                if (data.success) {
                    console.debug('auth flag refreshed');

                    // Defer user store access to avoid circular dependency issues
                    setTimeout(() => {
                        isAuthenticated.value = data.authenticated || false;
                        try {
                            const userStore = useUserStore();
                            userStore.persistUserData(
                                data.users,
                                data.perm_defs || null,
                                data.users?._perms || null,
                                data.xmpp || null,
                                data.password_policy || null,
                                data.users_group || null,
                                data.screens_url || ""
                            );

                            // Update editorKey in auth store if available
                            if (data.editor_key) {
                                editorKey.value = Base64.decode(data.editor_key);
                            }

                            // Update allAccess in auth store if available
                            if (data.all_access && Array.isArray(data.all_access)) {
                                allAccess.value = data.all_access;
                            }
                        } catch (error) {
                            console.error('Failed to update user store:', error);
                        }
                    }, 0);
                }

                if (data.csrf_token) {
                    csrfToken.value = data.csrf_token;
                    console.debug('CSRF token refreshed');
                    // Update global headers
                    updateBoomtownHeaders(csrfToken.value, clientInstanceId.value)
                } else {
                    throw new Error('No CSRF token in response');
                }

                return data;
            } catch (error) {
                if (error instanceof Error && error.message.includes('401')) {
                    const wasAuthenticated = isAuthenticated.value;
                    isAuthenticated.value = false;
                    csrfToken.value = '';
                    userEmail.value = '';

                    if (wasAuthenticated) {
                        handleAuthChange(false);
                    }
                } else {
                    console.error('Failed to refresh token:', error);
                    throw error;
                }
                return null;
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            userEmail.value = '';
            throw error;
        }
    }

    // Start automatic token refresh
    let refreshInterval: number | null = null;

    function startTokenRefresh(): void {
        stopTokenRefresh(); // Clear any existing interval
        refreshInterval = window.setInterval(() => {
            refreshCsrfToken().catch(console.error);
        }, 60 * 1000); // Refresh every minute
    }

    function stopTokenRefresh(): void {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    // Login function
    async function login(credentials: LoginCredentials): Promise<LoginResponse> {
        const formData = new URLSearchParams();
        formData.append('email', credentials.email);
        formData.append('password', credentials.password);

        try {
            const data = await baseHttpClient.post<LoginResponse>(
                'admin/v4/core/index.php',
                formData,
                { sAction: 'userLogin' },
                { 'x-boomtown-client-instance-id': clientInstanceId.value }
            );

            if (data.csrf_token) {
                console.debug('🔑 CSRF token received:', data.csrf_token);
                csrfToken.value = data.csrf_token;
                // Update global headers
                updateBoomtownHeaders(csrfToken.value, clientInstanceId.value)
            }

            if (data.success && data.authenticated) {
                // Set the email from the login response
                if (data.users?.email) {
                    userEmail.value = data.users.email;
                    console.debug('User email set:', userEmail.value);
                }

                // If we have editor key data, decode and store it
                if (data.editor_key) {
                    editorKey.value = Base64.decode(data.editor_key);
                }

                // Store all_access data if available
                if (data.all_access && Array.isArray(data.all_access)) {
                    allAccess.value = data.all_access;
                }

                // Update user store - defer access to avoid circular dependency
                setTimeout(() => {
                    try {
                        const userStore = useUserStore();
                        userStore.persistUserData(data.users, data.perm_defs, data.users?._perms || null, data.xmpp, data.password_policy, data.users_group || null, data.screens_url || "");
                    } catch (error) {
                        console.error('Failed to update user store:', error);
                    }
                }, 0);

                isAuthenticated.value = true;

                startTokenRefresh();
                handleAuthChange(true);
            }

            return data;
        } catch (error) {
            console.error('Login failed:', error);
            throw new Error('Login request failed');
        }
    }

    // Logout function
    async function logout(): Promise<void> {
        try {
            console.log('Auth store: logging out');

            // First clear all client-side state to prevent any race conditions
            const hadToken = Boolean(csrfToken.value);
            isAuthenticated.value = false;
            csrfToken.value = '';
            userEmail.value = '';
            isInitialized.value = false;
            editorKey.value = '';
            allAccess.value = [];
            stopTokenRefresh();

            // Only make the logout API call if we actually had a token
            if (hadToken) {
                try {
                    const headers: Record<string, string> = {
                        'x-boomtown-client-instance-id': clientInstanceId.value,
                        'x-request-id': uuidv4(),
                        'content-type': 'application/json',
                    };

                    // Call logout endpoint to clear server session
                    await baseHttpClient.get('admin/v4/core/', { sAction: 'userLogout' }, headers);
                } catch (error) {
                    console.error('API logout error:', error);
                    // Continue with client-side logout regardless
                }
            }

            // Clear all cookies in the boomtown domain
            document.cookie.split(';').forEach((cookie) => {
                const [name] = cookie.split('=');
                document.cookie = `${name.trim()}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=.boomtown.com`;
                document.cookie = `${name.trim()}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
            });

            // Clear user store data
            const userStore = useUserStore();
            userStore.clearUserData();

            // Always notify about auth change at the end
            handleAuthChange(false);
            console.log('Auth store: logout complete, auth state cleared');

            // Update global headers
            updateBoomtownHeaders('', clientInstanceId.value)
        } catch (error) {
            console.error('Logout error:', error);
            // Still ensure we clear state and notify
            stopTokenRefresh();
            handleAuthChange(false);
        } finally {
            isAuthenticated.value = false;
            csrfToken.value = '';
            userEmail.value = '';
            isInitialized.value = false;
            editorKey.value = '';
            allAccess.value = [];
            // Update global headers
            updateBoomtownHeaders('', clientInstanceId.value)
        }
    }

    // Handle authentication state changes
    function handleAuthChange(isAuthenticated: boolean): void {
        if (!isAuthenticated) {
            // navigate to login
            router.push('/login');
        }
        // If authenticated, do not automatically redirect anywhere
    }

    // Generate common HTTP headers with CSRF token
    function getCommonHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'x-boomtown-client-instance-id': clientInstanceId.value,
            'x-request-id': uuidv4(),
            'content-type': 'application/json',
        };

        if (csrfToken.value) {
            headers['x-boomtown-csrf-token'] = csrfToken.value;
        }

        return headers;
    }

    async function userResetPassword(credentials: resetCredentials): Promise<ResetPasswordResponse> {
        const formData = new URLSearchParams()
        formData.append('email', credentials.email)

        try {
            const data = await baseHttpClient.post<ResetPasswordResponse>(
                '/api/core/',
                formData,
                { sAction: 'userResetPassword' },
                {
                  'Content-Type': 'application/x-www-form-urlencoded',
                  'x-boomtown-client-instance-id': clientInstanceId.value
                }
              );

          // if your reset endpoint also returns a new CSRF token, grab it
          if ((data as any).csrf_token) {
            const token = (data as any).csrf_token as string
            console.debug('🔑 CSRF token received on reset:', token)
            updateBoomtownHeaders(token, clientInstanceId.value)
          }

          return data
        } catch (err) {
          console.error('Password reset request failed:', err)
          // you can throw a more specific error if you like:
          throw new Error(
            err instanceof Error
              ? err.message
              : 'Request to reset password failed'
          )
        }
      }

      async function userChangePassword(credentials: changeCredentials): Promise<ResetPasswordResponse> {
        const formData = new URLSearchParams()
        formData.append('email', credentials.email)
        formData.append('password', credentials.password)
        formData.append('confirm_password', credentials.confirm_password)
        formData.append('token', credentials.token)

        try {
            const data = await baseHttpClient.post<ResetPasswordResponse>(
                '/api/core/',
                formData,
                { sAction: 'userChangePassword' },
                {
                  'Content-Type': 'application/x-www-form-urlencoded',
                  'x-boomtown-client-instance-id': clientInstanceId.value
                }
              );

          // if your reset endpoint also returns a new CSRF token, grab it
          if ((data as any).csrf_token) {
            const token = (data as any).csrf_token as string
            console.debug('🔑 CSRF token received on password change:', token)
            updateBoomtownHeaders(token, clientInstanceId.value)
          }

          return data
        } catch (err) {
          console.error('Password change request failed:', err)
          // you can throw a more specific error if you like:
          throw new Error(
            err instanceof Error
              ? err.message
              : 'Request to change password failed'
          )
        }
      }


    return {
        isAuthenticated,
        isInitialized,
        csrfToken,
        clientInstanceId,
        apiBaseUrl,
        userEmail,
        editorKey,
        allAccess,
        initialize,
        login,
        logout,
        refreshCsrfToken,
        getCommonHeaders,
        userResetPassword,
        userChangePassword
    };
});
