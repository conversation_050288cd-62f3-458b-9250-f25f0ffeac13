import { defineStore } from 'pinia';
import { ref } from 'vue';
import { partnerAPI } from '@/services/PartnerAPI';
import type { Product } from '@/types/partner';
import { usePartnerAPI } from '@/composables/services/usePartnerAPI';
import { useUserStore } from '@/stores/user';

export const usePartnerStore = defineStore('partner', () => {
    const products = ref<Product[]>([]);
    const partnerTeams = ref<any[]>([]);
    const loading = ref(false);
    const loadingTeams = ref(false);
    const error = ref<string | null>(null);
    const teamsError = ref<string | null>(null);

    async function fetchProducts() {
        try {
            loading.value = true;
            error.value = null;
            const partnerAPI = usePartnerAPI();
            const response = await partnerAPI.fetchProducts();
            // debugger
            if (response.success) {
                products.value = response.partners_product_list.results;
            } else {
                throw new Error('Failed to fetch products');
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch products';
            console.error('Error fetching products:', err);
        } finally {
            loading.value = false;
        }
    }

    async function fetchPartnerTeams(ecosystem: boolean = false) {
        try {
            loadingTeams.value = true;
            teamsError.value = null;
            
            const api = usePartnerAPI();
            const response = await api.fetchPartnerTeams(ecosystem);
            
            if (response.success) {
                let teams = response.pl__org_partners_teams || [];
                
                if (!ecosystem) {
                    // Get current user's organization from user store
                    const userStore = useUserStore();
                    const currentOrgId = userStore.userTeams[0]?.substring(0, 3);
                    
                    // Filter teams to only include those from current organization
                    teams = teams.filter((team: any) => team.val.substring(0, 3) === currentOrgId);
                }
                
                partnerTeams.value = teams;
            } else {
                throw new Error('Failed to fetch partner teams');
            }
        } catch (err) {
            teamsError.value = err instanceof Error ? err.message : 'Failed to fetch partner teams';
            console.error('Error fetching partner teams:', err);
        } finally {
            loadingTeams.value = false;
        }
    }

    return {
        products,
        partnerTeams,
        loading,
        loadingTeams,
        error,
        teamsError,
        fetchProducts,
        fetchPartnerTeams
    };
}); 