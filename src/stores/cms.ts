import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useCMSAPI, type CMSLayout } from '@/composables/services/useCMSAPI';

export const useCMSStore = defineStore('cms', () => {
  const layouts = ref<CMSLayout[]>([]);
  const caseLayout = ref<CMSLayout | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const cmsAPI = useCMSAPI();

  /**
   * Fetch all CMS layouts for a specific partner
   */
  async function fetchAllLayouts(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      // Use provided partner ID or fall back to hardcoded default
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: '_no_filter_' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      layouts.value = result.results;
      console.log(`CMS Store: Fetched all layouts for partner: ${partnerIdToUse} (${partnerSource}), Count: ${layouts.value.length}`);
      
      return result.results;
    } catch (err) {
      console.error('CMS Store: Error in fetchAllLayouts:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch the specific case layout for a partner
   */
  async function fetchCaseLayout(partnerId?: string) {
    loading.value = true;
    error.value = null;

    try {
      // Use provided partner ID or fall back to hardcoded default
      const partnerIdToUse = partnerId || 'H3F';
      const partnerSource = partnerId ? 'provided' : 'default fallback';

      const result = await cmsAPI.fetchLayouts({
        filter: [
          { property: 'id', value: '96f47064-16b3-428d-969c-79d561a286f6' },
          { property: 'partners_id', value: partnerIdToUse },
          { property: 'object', value: '_no_filter_' }
        ]
      });

      if (result.results.length > 0) {
        caseLayout.value = result.results[0];
        console.log(`CMS Store: Fetched case layout for partner: ${partnerIdToUse} (${partnerSource})`, caseLayout.value);
      } else {
        caseLayout.value = null;
        console.log(`CMS Store: No case layout found for partner: ${partnerIdToUse} (${partnerSource})`);
      }
      
      return caseLayout.value;
    } catch (err) {
      console.error('CMS Store: Error in fetchCaseLayout:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get a layout by ID from the cached layouts
   */
  function getLayoutById(id: string): CMSLayout | undefined {
    return layouts.value.find(layout => layout.id === id);
  }

  /**
   * Clear all cached data
   */
  function clearCache() {
    layouts.value = [];
    caseLayout.value = null;
    error.value = null;
  }

  return {
    // State
    layouts,
    caseLayout,
    loading,
    error,
    
    // Actions
    fetchAllLayouts,
    fetchCaseLayout,
    getLayoutById,
    clearCache
  };
}); 