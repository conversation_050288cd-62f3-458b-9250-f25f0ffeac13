import { defineStore } from 'pinia'
import { ref } from 'vue'
import { fetchCustomerSummary, createCustomerSummary } from '@/composables/services/useSummarizerApi'
import type { CreateCustomerSummaryParams } from '@/composables/services/useSummarizerApi'

export const useSummarizerStore = defineStore('summarizer', () => {
  const customerSummary = ref<any | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const createLoading = ref(false)
  const createError = ref<string | null>(null)

  async function loadCustomerSummary(customerId: string) {
    loading.value = true
    error.value = null
    try {
      const data = await fetchCustomerSummary(customerId)
      customerSummary.value = Array.isArray(data) && data.length > 0 ? data[0] : null
    } catch (err: any) {
      if (err && err.message && err.message.toLowerCase().includes('404')) {
        error.value = 'not_found'
      } else {
        error.value = err instanceof Error ? err.message : 'Failed to load customer summary'
      }
    } finally {
      loading.value = false
    }
  }

  async function createCustomerSummaryAction(params: CreateCustomerSummaryParams) {
    createLoading.value = true
    createError.value = null
    try {
      const data = await createCustomerSummary(params)
      customerSummary.value = Array.isArray(data) && data.length > 0 ? data[0] : data
    } catch (err) {
      createError.value = err instanceof Error ? err.message : 'Failed to create customer summary'
    } finally {
      createLoading.value = false
    }
  }

  return {
    customerSummary,
    loading,
    error,
    loadCustomerSummary,
    createLoading,
    createError,
    createCustomerSummary: createCustomerSummaryAction
  }
}) 