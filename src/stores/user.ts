import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useUser<PERSON><PERSON> } from '@/composables/services/useUserAPI';
import { useMetaStore } from './meta';

export interface UserStoreInterface {
    persistUserData: (userData: any, permDefs?: any, userPerms?: any, xmppData?: any, password_policy?: object, orgData?: object, screens_url?: string) => boolean;
    isAuthenticated: boolean;
}

interface LoginCredentials {
    email: string;
    password: string;
}

export const useUserStore = defineStore('user', () => {
    const initialized = ref(false);
    const username = ref('');
    const userData = ref<any>(null);
    const userTeams = ref<any>(['H3F-36U']);
    // todo: remove default
    const userPerms = ref<any>(null);
    const permDefs = ref<any>(null);
    const xmppData = ref<any>(null);
    const passwordPolicy = ref<any>(null);
    const orgData = ref<any>(null);
    const screensUrl = ref<string | null>(null);


        async function initialize() {
            // Only do one initialization
            if (initialized.value) return;
            initialized.value = true;
        }

        // Persist user data to store
        function persistUserData(
            userDataObj: any,
            permDefsObj: any = null,
            userPermsObj: any = null,
            xmppDataObj: any = null,
            passwordPolicyObj: any = null,
            orgDataObj: any = null,
            screens_url:string = "",
        ): boolean {
            try {
                if (userDataObj) {
                    username.value = userDataObj.name || userDataObj.full_name || '';
                    // Store the full user data
                    userData.value = userDataObj;
                }

                if (userPermsObj) {
                    userPerms.value = userPermsObj;
                }

                if (permDefsObj) {
                    permDefs.value = permDefsObj;
                }

                if (xmppDataObj) {
                    xmppData.value = xmppDataObj;
                }

                if (passwordPolicyObj) {
                    passwordPolicy.value = passwordPolicyObj;
                }

                if (orgDataObj) {
                    orgData.value = orgDataObj;
                }

                if (screens_url) {
                    screensUrl.value = screens_url;
                }

                return true;
            } catch (error) {
                console.error('Failed to persist user data:', error);
                username.value = '';
                userData.value = null;
                permDefs.value = null;
                xmppData.value = null;
                orgData.value = null;
            }
            return false;
        }

        function clearUserData() {
            username.value = '';
            userData.value = null;
            userPerms.value = null;
            permDefs.value = null;
            xmppData.value = null;
            orgData.value = null;
        }

        /**
         * Checks if the current user has a specific permission
         * @param permission The permission string to check for
         * @returns True if the user has the permission, false otherwise
         */
        function hasPermission(permission: string): boolean {
            // Check if we have the permission definitions available
            if (!permDefs.value) {
                console.warn('Permission definitions not available for permission check', permission);
                return false;
            }

            // Get the permission ID from the permission definitions
            const permId = permDefs.value[permission];
            if (!permId) {
                console.debug(`Permission "${permission}" not found in permission definitions`);
                return false;
            }

            // Make sure we have user data with permissions
            if (!userPerms.value) {
                console.warn('Permission IDs not available in userPerms', permission);
                return false;
            }

        // Check if permissions are in userPerms directly or in the users property
        const perms = userPerms.value;

            // If perms is an array, check if it includes the permission ID or name
            if (Array.isArray(perms)) {
                return (
                    perms.some((value) => value === permId) ||
                    perms.includes(permId.toString()) ||
                    perms.includes(permission)
                );
            }

            return false;
        }

        function getUserAccessLevel(key: string): object {
            return userData?.value?._access[key];
        }

        /**
         * Gets all permissions for the current user
         * @returns Array of permission strings or empty array if no permissions
         */
        function getUserPermissions(): string[] {
            // Make sure we have user data with permissions
            if (!userData.value?.perms && !userData.value?.users?.perms) {
                return [];
            }

            // Check if permissions are in userData directly or in the users property
            const perms = userData.value?.perms || userData.value?.users?.perms;

            // If perms is already an array, return it
            if (Array.isArray(perms)) {
                return [...perms];
            }

            // If perms is an object with a permSet property (as seen in some responses)
            if (perms?.permSet && typeof perms.permSet === 'object') {
                return Object.keys(perms.permSet);
            }

            return [];
        }

        /**
         * Checks if the user has any of the specified permissions
         * @param permissions Array of permissions to check
         * @returns True if the user has at least one of the permissions, false otherwise
         */
        function hasAnyPermission(permissions: string[]): boolean {
            return permissions.some((permission) => hasPermission(permission));
        }

        /**
         * Checks if the user has all of the specified permissions
         * @param permissions Array of permissions to check
         * @returns True if the user has all of the permissions, false otherwise
         */
        function hasAllPermissions(permissions: string[]): boolean {
            return permissions.every((permission) => hasPermission(permission));
        }

        const updateUserProfile = async (profileData: {
            first_name: string;
            last_name: string;
            email: string;
            image: string;
            nickname?: string;
            phone_number?: string;
            phone_ext?: string;
            time_zone?: string;
        }) => {
            try {
                const userAPI = useUserAPI();
                const response = await userAPI.updateUserProfile({
                    id: userData.value?.id || '',
                    first_name: profileData.first_name,
                    last_name: profileData.last_name,
                    email: profileData.email,
                    image: profileData.image,
                    nickname: profileData.nickname,
                    phone_number: profileData.phone_number,
                    phone_ext: profileData.phone_ext,
                    time_zone: profileData.time_zone,
                });

                if (response.success) {
                    // Update local store data
                    if (userData.value) {
                        userData.value = {
                            ...userData.value,
                            first_name: profileData.first_name,
                            last_name: profileData.last_name,
                            email: profileData.email,
                            image: profileData.image,
                            nickname: profileData.nickname,
                            phone_number: profileData.phone_number,
                            phone_ext: profileData.phone_ext,
                            time_zone: profileData.time_zone,
                        };
                    }
                    return response;
                }

                throw new Error(response.message || 'Failed to update user profile');
            } catch (error) {
                console.error('Failed to update user profile:', error);
                throw error;
            }
        };

        // Get reference to metaStore
        const metaStore = useMetaStore();

        /**
         * Gets the appropriate props for BravoAvatar component based on user data
         * @param user The user object (defaults to current user if not provided)
         * @returns Object with props for BravoAvatar component
         */
        function getUserAvatarProps(user?: any) {
            const targetUser = user || userData.value;

            if (!targetUser) {
                return {
                    firstName: 'U',
                    lastName: '',
                    image: undefined
                };
            }

            // Check if user has an uploaded avatar
            const avatarUrl = targetUser.url_avatar || targetUser.image;

            // Only use the avatar URL if it contains "/users" (real user avatar)
            // URLs with "/partners" are fallback images and should not be used
            const hasRealAvatar = avatarUrl && typeof avatarUrl === 'string' && avatarUrl.includes('/users');

            return {
                image: hasRealAvatar ? avatarUrl : undefined,
                firstName: targetUser.first_name || 'U',
                lastName: targetUser.last_name || '',
            };
        }

    return {
        initialize,
        isInitialized: initialized,
        username,
        userData,
        userTeams,
        userPerms,
        permDefs,
        xmppData,
        orgData,
        screensUrl,
        passwordPolicy,
        persistUserData,
        clearUserData,
        // Use metaStore's function instead of directly referencing it
        getPartnerLabelById: (id: string) => metaStore.getPartnerLabelById(id),
        hasPermission,
        getUserAccessLevel,
        getUserPermissions,
        hasAnyPermission,
        hasAllPermissions,
        updateUserProfile,
        getUserAvatarProps,
    };
}, {
    persist: {
        key: 'user-permissions',
        storage: localStorage,
        pick: ['permDefs'],
    }
});

function fetchCases() {
    throw new Error('Function not implemented.');
}
