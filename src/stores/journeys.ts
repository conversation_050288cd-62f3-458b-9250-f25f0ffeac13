import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useAdminJourneysAPI, type Journey } from '@/composables/services/useAdminJourneysAPI';

export const useJourneysStore = defineStore('journeys', () => {
  const journeys = ref<Journey[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  async function fetchJourneys(partnersId: string = 'H3F') {
    loading.value = true;
    error.value = null;

    try {
      const adminJourneysAPI = useAdminJourneysAPI();
      journeys.value = await adminJourneysAPI.fetchJourneys(partnersId);
      console.log('Journeys Store: Journeys set to:', journeys.value);
    } catch (err) {
      console.error('Journeys Store: Error in fetchJourneys:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  function clearJourneys() {
    journeys.value = [];
    error.value = null;
  }

  return {
    journeys,
    loading,
    error,
    fetchJourneys,
    clearJourneys,
  };
}); 