import { defineStore } from 'pinia';
import type { CopilotSearchResult } from '@/types/copilot';

export const useCopilotStore = defineStore('copilot', {
    state: () => {
        return {};
    },
    getters: {},
    actions: {
        async submitSearch(search: string): Promise<CopilotSearchResult> {
            try {
                const data = {
                    message: search,
                };
                
                const url = 'https://genai.ovationcxm.ai/api/search';
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });
                
                const result = await response.json();

                console.log('result - ', result);

                return {
                    message: result.message,
                    sources: result.source ? result.source.map((s: string) => {
                        return {
                            url: s
                        }
                    }) : [],
                    relatedLinks: result.relatedLinks ? result.relatedLinks.map((r: string) => {
                        return {
                            url: r
                        }
                    }) : [],
                    triggers: result.triggers,
                }
            } catch (error) {
                console.log('Failed to submit search', error);
                throw error;
            }
        },
    },
}); 