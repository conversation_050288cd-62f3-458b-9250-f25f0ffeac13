import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUsersAPI, type User, type FetchUserListParams } from '../composables/services/useUsersAPI'

export const useUsersStore = defineStore('users', () => {
  const usersAPI = useUsersAPI()
  
  // State
  const users = ref<User[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const totalCount = ref(0)

  // Getters
  const getUsersByTeam = computed(() => {
    return (teamId: string) => users.value.filter(user => user.object_id === teamId)
  })

  const getActiveUsers = computed(() => {
    return users.value.filter(user => user.status === 1)
  })

  const getUsersForSelect = computed(() => {
    return users.value.map(user => ({
      label: `${user.first_name} ${user.last_name}`.trim() || user.email,
      value: user.id,
      teamId: user.object_id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name
    }))
  })

  // Actions
  async function fetchUsers(params: FetchUserListParams = {}) {
    try {
      loading.value = true
      error.value = null
      
      const response = await usersAPI.fetchUserList(params)
      
      if (response.success) {
        users.value = response.users.results
        totalCount.value = response.users.totalCount
      } else {
        throw new Error('Failed to fetch users')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch users'
      console.error('Error fetching users:', err)
      users.value = []
      totalCount.value = 0
    } finally {
      loading.value = false
    }
  }

  async function fetchUsersByTeam(teamId: string, organizationId?: string) {
    return fetchUsers({ teamId, organizationId })
  }

  function clearUsers() {
    users.value = []
    totalCount.value = 0
    error.value = null
  }

  function getUserById(id: string): User | undefined {
    return users.value.find(user => user.id === id)
  }

  function getUserFullName(id: string): string {
    const user = getUserById(id)
    if (!user) return ''
    return `${user.first_name} ${user.last_name}`.trim() || user.email
  }

  return {
    // State
    users,
    loading,
    error,
    totalCount,
    
    // Getters
    getUsersByTeam,
    getActiveUsers,
    getUsersForSelect,
    
    // Actions
    fetchUsers,
    fetchUsersByTeam,
    clearUsers,
    getUserById,
    getUserFullName
  }
}) 