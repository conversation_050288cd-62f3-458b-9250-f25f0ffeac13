import { defineStore } from 'pinia'
import { ref } from 'vue'
import { fetchConnectors } from '@/composables/services/useConnectorsApi'

export const useConnectorsStore = defineStore('connectors', () => {
  const connectors = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  async function loadConnectors(orgId: string) {
    loading.value = true
    error.value = null
    try {
      const data = await fetchConnectors(orgId)
      connectors.value = Array.isArray(data.data) ? data.data : []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load connectors'
    } finally {
      loading.value = false
    }
  }

  return {
    connectors,
    loading,
    error,
    loadConnectors
  }
}) 