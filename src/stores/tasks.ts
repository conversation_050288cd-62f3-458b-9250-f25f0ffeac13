import { defineStore } from 'pinia'
import { ref } from 'vue'
import { fetchUserTasks, fetchTeamTasks, fetchAllTasks } from '@/composables/services/useTasksApi'
import { useUserStore } from '@/stores/user'

export const useTasksStore = defineStore('tasks', () => {
  const userTasks = ref<any[]>([])
  const teamTasks = ref<any[]>([])
  const allTasks = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  async function loadUserTasks(userId: string) {
    loading.value = true
    error.value = null
    try {
      const data = await fetchUserTasks(userId)
      userTasks.value = Array.isArray(data) ? data : (data.tasks || [])
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load user tasks'
    } finally {
      loading.value = false
    }
  }

  async function loadTeamTasks() {
    loading.value = true
    error.value = null
    try {
      const userStore = useUserStore()
      const teamId = userStore.userTeams[0] // mock for now
      const data = await fetchTeamTasks(teamId)
      teamTasks.value = Array.isArray(data) ? data : (data.tasks || [])
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load team tasks'
    } finally {
      loading.value = false
    }
  }

  async function loadAllTasks() {
    loading.value = true
    error.value = null
    try {
      const userStore = useUserStore()
      const orgId = userStore.userData?.object_id || ''
      const data = await fetchAllTasks(orgId)
      allTasks.value = Array.isArray(data) ? data : (data.tasks || [])
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load all tasks'
    } finally {
      loading.value = false
    }
  }

  return {
    userTasks,
    teamTasks,
    allTasks,
    loading,
    error,
    loadUserTasks,
    loadTeamTasks,
    loadAllTasks
  }
}) 