<template>
  <div class="customers-view">
    <CustomersSidebar 
      @item-selected="handleItemSelected"
    />
    
    <div class="main-content">
      <RouterView />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import CustomersSidebar from '../components/CustomersSidebar.vue'

interface NavigationItem {
  id: string
  label: string
  icon: string
  badge?: string | number
}

const router = useRouter()

// Component state
const selectedView = ref<NavigationItem | null>(null)

// Event handlers
const handleItemSelected = (item: NavigationItem) => {
  selectedView.value = item
  console.log('Selected view:', item)
  
  // Handle routing based on selected item
  switch (item.id) {
    case 'customers':
      router.push('/customers')
      break
    case 'locations':
      router.push('/customers/locations')
      break
    case 'contacts':
      router.push('/customers/contacts') 
      break
    default:
      router.push('/customers')
  }
}
</script>

<style scoped>
.customers-view {
  height: 100%;
  display: flex;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: hidden;
  background: var(--surface-0);
}
</style>
