<template>
  <div class="contacts-list">
    <div class="header">
      <BravoTitlePage>Contacts</BravoTitlePage>
      <div class="button-group">
        <BravoButton 
          label="Add Contact" 
          icon="pi pi-plus" 
          @click="showAddContactModal" 
        />
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-row">
      <div class="search-filters">
        <BravoIconField>
          <BravoInputIcon class="pi pi-search" />
          <BravoInputText 
            v-model="searchQuery" 
            @input="handleSearchInput" 
            @keydown="handleSearchKeydown" 
            placeholder="Search contacts..." 
            :disabled="isLoading"
          />
        </BravoIconField>
      </div>
    </div>

    <!-- Contacts List Table -->
    <BravoDataTable 
      :value="filteredContacts"
      :columns="[]" 
      dataKey="id"
      :rowHover="true"
      :stripedRows="false"
      :showGridlines="false" 
      class="p-datatable-sm" 
      v-model:selection="selectedContacts"
      @selection-change="onSelectionChange"
      :sortField="sortField"
      :sortOrder="sortOrder"
      @sort="onSort"
      removableSort
      :loading="false"
      :rows="rowsPerPage"
      :totalRecords="totalContacts"
      :paginator="true"
      :lazy="false"
      @page="onPage"
      :first="(currentPage - 1) * rowsPerPage"
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      :rowsPerPageOptions="[10, 25, 50]"
      scrollable
      scrollHeight="calc(100vh - 230px)"
      paginatorPosition="bottom"
      paginatorClassName="fixed-paginator"
    >
      <template #header>
        <div>
          <span v-if="selectedContacts.length > 0">
            {{ selectedContacts.length }} of {{ filteredContacts.length }} contacts selected
          </span>
          <span v-else>
            Showing {{ filteredContacts.length }} of {{ totalContacts }} contacts
          </span>
        </div>
      </template>
      
      <template #empty>
        <div>No contacts found</div>
      </template>
      
      <Column selectionMode="multiple" headerStyle="width: 3rem" />
      
      <Column field="name" header="Contact Name" sortable style="min-width: 200px">
        <template #body="{ data }">
          <a 
            :href="`/customers/contacts/${data.id}`"
            class="contact-name-link" 
            @click="(event) => handleContactClick(data, event)"
          >
            {{ data.name }}
          </a>
        </template>
      </Column>
      
      <Column field="customer" header="Customer" sortable style="min-width: 200px">
        <template #body="{ data }">
          <span>{{ data.customer }}</span>
        </template>
      </Column>
      
      <Column field="role" header="Role" sortable style="min-width: 150px">
        <template #body="{ data }">
          <span>{{ data.role }}</span>
        </template>
      </Column>
      
      <Column field="email" header="Email" sortable style="min-width: 250px">
        <template #body="{ data }">
          <span>{{ data.email }}</span>
        </template>
      </Column>
      
      <Column field="phone" header="Phone" sortable style="min-width: 150px">
        <template #body="{ data }">
          <span>{{ data.phone }}</span>
        </template>
      </Column>
      
      <Column field="status" header="Status" sortable style="min-width: 100px">
        <template #body="{ data }">
          <BravoTag
            :value="data.status" 
            :state="data.status.toLowerCase()" 
          />
        </template>
      </Column>
      
      <Column field="lastActivity" header="Last Activity" sortable style="min-width: 150px">
        <template #body="{ data }">
          <BravoTimestamp 
            v-if="data.lastActivity" 
            :datetime="data.lastActivity" 
          />
          <span v-else>N/A</span>
        </template>
      </Column>
      
      <Column :exportable="false" style="width: 4rem">
        <template #body="{ data }">
          <div class="flex align-items-center justify-content-center">
            <BravoButton
              icon="pi pi-ellipsis-v"
              text
              severity="secondary"
              class="table-action-button"
              @click="(e) => toggleMenu(e, data)"
              aria-label="Contact actions"
              v-tooltip.bottom="{ value: 'Actions', showDelay: 400 }"
            />
          </div>
        </template>
      </Column>
    </BravoDataTable>

    <BravoMenu ref="menu" :model="menuItems" :popup="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import BravoDataTable from 'primevue/datatable'
import Column from 'primevue/column'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoIconField from '@services/ui-component-library/components/BravoIconField.vue'
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue'
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue'
import Tooltip from 'primevue/tooltip'
import type { MenuItem } from 'primevue/menuitem'

const router = useRouter()

// Component state
const selectedContacts = ref<any[]>([])
const sortField = ref<string>('')
const sortOrder = ref<number>(1)
const currentPage = ref(1)
const rowsPerPage = ref(25)
const menu = ref()
const selectedRecord = ref<any>(null)
const searchQuery = ref('')
const isLoading = ref(false)

// Register Tooltip directive
const vTooltip = Tooltip

// Mock contact data
const contacts = ref([
  {
    id: '101',
    name: 'Sarah Johnson',
    customer: 'Tech Solutions Inc',
    role: 'Project Manager',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    lastActivity: new Date('2024-04-01T10:30:00Z').toISOString()
  },
  {
    id: '102',
    name: 'Michael Chen',
    customer: 'Global Services Ltd',
    role: 'Operations Director',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    lastActivity: new Date('2024-03-28T14:15:00Z').toISOString()
  },
  {
    id: '103',
    name: 'Emma Williams',
    customer: 'Alan\'s Burger',
    role: 'Store Manager',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    lastActivity: new Date('2024-03-15T09:45:00Z').toISOString()
  },
  {
    id: '104',
    name: 'David Rodriguez',
    customer: 'Innovation Corp',
    role: 'Engineering Lead',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    lastActivity: new Date('2024-04-02T16:20:00Z').toISOString()
  },
  {
    id: '105',
    name: 'Lisa Thompson',
    customer: 'Digital Dynamics',
    role: 'Account Executive',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Inactive',
    lastActivity: new Date('2024-03-30T11:10:00Z').toISOString()
  }
])

// Computed properties
const filteredContacts = computed(() => {
  if (!searchQuery.value.trim()) {
    return contacts.value
  }
  
  const query = searchQuery.value.toLowerCase().trim()
  return contacts.value.filter(contact => 
    contact.name.toLowerCase().includes(query) ||
    contact.customer.toLowerCase().includes(query) ||
    contact.role.toLowerCase().includes(query) ||
    contact.email.toLowerCase().includes(query) ||
    contact.phone.toLowerCase().includes(query) ||
    contact.status.toLowerCase().includes(query)
  )
})

const totalContacts = computed(() => contacts.value.length)

// Search handlers
const handleSearchInput = () => {
  // Reset to first page when searching
  currentPage.value = 1
}

const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    // Perform search on Enter key
    handleSearchInput()
  }
}

// Event handlers
const handleContactClick = (contact: any, event?: MouseEvent) => {
  // Allow default behavior for middle mouse button and Ctrl+click
  if (event && (event.button === 1 || event.ctrlKey || event.metaKey)) {
    return;
  }
  
  // For regular left clicks, prevent default and use router navigation
  if (event) {
    event.preventDefault();
  }
  router.push(`/customers/contacts/${contact.id}`);
}

const onSelectionChange = (selection: any[]) => {
  selectedContacts.value = selection
}

const onSort = (event: any) => {
  sortField.value = event.sortField
  sortOrder.value = event.sortOrder
}

const onPage = (event: any) => {
  currentPage.value = event.page + 1
  rowsPerPage.value = event.rows
}

const showAddContactModal = () => {
  console.log('Add contact modal')
  // TODO: Implement add contact modal
}

// Menu functionality
const editRecord = (record: any) => {
  console.log('Edit contact:', record)
  // TODO: Implement edit contact functionality
}

const deleteRecord = (record: any) => {
  console.log('Delete contact:', record)
  // TODO: Implement delete contact functionality
}

const viewRecord = (record: any) => {
  router.push(`/customers/contacts/${record.id}`)
}

const menuItems = computed((): MenuItem[] => {
  if (!selectedRecord.value) return []
  
  return [
    {
      label: 'View',
      icon: 'pi pi-eye',
      command: () => {
        viewRecord(selectedRecord.value)
      }
    },
    {
      label: 'Edit',
      icon: 'pi pi-pencil',
      command: () => {
        editRecord(selectedRecord.value)
      }
    },
    {
      label: 'Delete',
      icon: 'pi pi-trash',
      command: () => {
        deleteRecord(selectedRecord.value)
      }
    }
  ]
})

const toggleMenu = (event: Event, record: any) => {
  selectedRecord.value = record
  nextTick(() => {
    menu.value.toggle(event)
  })
}
</script>

<style scoped>
.contacts-list {
  padding: 1rem 2rem 0rem 2rem;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  max-height: 100vh;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  align-items: center;
  flex: 0 0 auto;
}

.button-group {
  display: flex;
  gap: .75rem;
}

.search-row {
  margin-bottom: 1.5rem;
  flex: 0 0 auto;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

/* Make the data table take up available space and paginator fixed at bottom */
:deep(.p-datatable) {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: calc(100vh - 230px);
  position: relative;
}

:deep(.p-datatable-wrapper) {
  flex-grow: 1;
  overflow: auto;
  min-height: 200px;
  padding-bottom: 56px;
}

:deep(.p-paginator) {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--surface-a);
  z-index: 1;
}

.contact-name-link {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    color: var(--blue-650);
    text-decoration: underline;
  }
}

:deep(.p-datatable-table) {
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0 !important;
}

:deep(.p-datatable-tbody > tr.p-highlight) {
  background-color: var(--primary-50) !important;
  color: var(--primary-700) !important;
}

:deep(.p-datatable-tbody > tr.p-highlight:hover) {
  background-color: var(--primary-100) !important;
}

:deep(.p-checkbox .p-checkbox-box.p-highlight) {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

:deep(.p-datatable .p-datatable-header) {
  border: none !important;
}

.table-action-button {
  width: 2rem !important;
  height: 2rem !important;
  padding: 0.5rem !important;
}

:deep(.fixed-paginator) {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  margin: 0 !important;
}
</style>
