<template>
  <div class="contact-detail">
    <div class="contact-header">
      <div class="header-nav">
        <BravoButton
          icon="pi pi-arrow-left"
          text
          @click="handleBack"
          class="back-button"
          aria-label="Back to contacts"
        />
        <BravoTitlePage>Contact Detail</BravoTitlePage>
      </div>
      
      <div class="header-actions">
        <BravoButton
          icon="pi pi-pencil"
          label="Edit Contact"
          severity="primary"
          @click="handleEditContact"
        />
        <BravoButton
          icon="pi pi-ellipsis-h"
          severity="secondary"
          text
          @click="handleContactActions"
          aria-label="Contact actions"
        />
      </div>
    </div>

    <div class="contact-content">
      <div class="contact-info-section">
        <div class="contact-overview">
          <div class="contact-avatar">
            <BravoAvatar 
              :label="contactInitials"
              size="xlarge"
              shape="circle"
            />
          </div>
          
          <div class="contact-basic-info">
            <h1 class="contact-name">{{ contactName }}</h1>
            <p class="contact-id">Contact ID: {{ id }}</p>
            <div class="contact-status">
              <BravoBadge value="Active" severity="success" />
              <BravoBadge value="Primary Contact" severity="info" />
            </div>
          </div>
        </div>

        <div class="contact-details">
          <div class="detail-section">
            <h3>Contact Information</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>Full Name</label>
                <span>{{ contactName }}</span>
              </div>
              <div class="detail-item">
                <label>Job Title</label>
                <span>Account Manager</span>
              </div>
              <div class="detail-item">
                <label>Department</label>
                <span>Customer Success</span>
              </div>
              <div class="detail-item">
                <label>Email</label>
                <span>contact{{ id }}@company.com</span>
              </div>
              <div class="detail-item">
                <label>Phone</label>
                <span>+1 (555) 123-{{ id }}0</span>
              </div>
              <div class="detail-item">
                <label>Mobile</label>
                <span>+1 (555) 987-{{ id }}1</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3>Customer Association</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>Associated Customer</label>
                <span>Acme Corporation</span>
              </div>
              <div class="detail-item">
                <label>Location</label>
                <span>Main Office - Downtown</span>
              </div>
              <div class="detail-item">
                <label>Account Type</label>
                <span>Enterprise</span>
              </div>
              <div class="detail-item">
                <label>Relationship Start</label>
                <span>January 15, 2023</span>
              </div>
              <div class="detail-item">
                <label>Contact Preference</label>
                <span>Email & Phone</span>
              </div>
              <div class="detail-item">
                <label>Timezone</label>
                <span>Pacific Standard Time (PST)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="contact-activity-section">
        <h3>Contact Activity & Communications</h3>
        <div class="activity-placeholder">
          <i class="pi pi-comments" style="font-size: 2rem; color: var(--surface-400); margin-bottom: 1rem;"></i>
          <p>Contact communication history and activity timeline will be displayed here.</p>
          <p class="feature-note">
            <strong>Planned features:</strong><br>
            • Communication history (emails, calls, meetings)<br>
            • Interaction timeline and notes<br>
            • Task and follow-up management<br>
            • Document sharing history<br>
            • Relationship insights and analytics
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue'

// Props
const props = defineProps<{
  id: string
}>()

const router = useRouter()

// For now, generate a placeholder contact name based on the ID
const contactName = computed(() => {
  const names = ['Sarah Johnson', 'Michael Chen', 'Emma Williams', 'David Brown', 'Lisa Garcia']
  const index = parseInt(props.id) % names.length
  return names[index] || `Contact ${props.id}`
})

const contactInitials = computed(() => {
  return contactName.value
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
})

// Event handlers
const handleBack = () => {
  router.push('/customers/contacts')
}

const handleEditContact = () => {
  console.log('Edit contact:', props.id)
  // TODO: Implement edit contact functionality
}

const handleContactActions = () => {
  console.log('Contact actions for:', props.id)
  // TODO: Implement contact actions menu
}
</script>

<style scoped>
.contact-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

.contact-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--surface-200);
  background: white;
  height: 64px;
  flex-shrink: 0;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background-color: var(--surface-100) !important;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.contact-info-section {
  margin-bottom: 3rem;
}

.contact-overview {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: var(--surface-50);
  border-radius: 12px;
  border: 1px solid var(--surface-200);
}

.contact-avatar {
  flex-shrink: 0;
}

.contact-basic-info {
  flex: 1;
}

.contact-name {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
}

.contact-id {
  margin: 0 0 1rem 0;
  color: var(--text-color-secondary);
  font-size: 1rem;
}

.contact-status {
  display: flex;
  gap: 0.5rem;
}

.contact-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.detail-section {
  background: white;
  border: 1px solid var(--surface-200);
  border-radius: 8px;
  padding: 1.5rem;
}

.detail-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.detail-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item label {
  font-weight: 600;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  color: var(--text-color);
  font-size: 1rem;
}

.contact-activity-section {
  background: white;
  border: 1px solid var(--surface-200);
  border-radius: 8px;
  padding: 1.5rem;
}

.contact-activity-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.activity-placeholder {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-color-secondary);
}

.activity-placeholder p {
  margin: 0 0 1rem 0;
}

.feature-note {
  background: var(--surface-100);
  padding: 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  text-align: left;
  max-width: 400px;
  margin: 1.5rem auto 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .contact-details {
    grid-template-columns: 1fr;
  }
  
  .contact-overview {
    flex-direction: column;
    text-align: center;
  }
  
  .contact-content {
    padding: 1rem;
  }
  
  .contact-header {
    padding: 1rem;
  }
}
</style>
