<template>
  <div class="location-detail">
    <div class="location-header">
      <div class="header-nav">
        <BravoButton
          icon="pi pi-arrow-left"
          text
          @click="handleBack"
          class="back-button"
          aria-label="Back to locations"
        />
        <BravoTitlePage>Location Detail</BravoTitlePage>
      </div>
      
      <div class="header-actions">
        <BravoButton
          icon="pi pi-pencil"
          label="Edit Location"
          severity="primary"
          @click="handleEditLocation"
        />
        <BravoButton
          icon="pi pi-ellipsis-h"
          severity="secondary"
          text
          @click="handleLocationActions"
          aria-label="Location actions"
        />
      </div>
    </div>

    <div class="location-content">
      <div class="location-info-section">
        <div class="location-overview">
          <div class="location-avatar">
            <i class="pi pi-map-marker" style="font-size: 3rem; color: var(--primary-color);"></i>
          </div>
          
          <div class="location-basic-info">
            <h1 class="location-name">{{ locationName }}</h1>
            <p class="location-id">Location ID: {{ id }}</p>
            <div class="location-status">
              <BravoBadge value="Active" severity="success" />
            </div>
          </div>
        </div>

        <div class="location-details">
          <div class="detail-section">
            <h3>Address Information</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>Street Address</label>
                <span>123 Business District Ave</span>
              </div>
              <div class="detail-item">
                <label>City</label>
                <span>Downtown City</span>
              </div>
              <div class="detail-item">
                <label>State / Province</label>
                <span>CA</span>
              </div>
              <div class="detail-item">
                <label>Postal Code</label>
                <span>90210</span>
              </div>
              <div class="detail-item">
                <label>Country</label>
                <span>United States</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h3>Location Information</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>Location Type</label>
                <span>Main Office</span>
              </div>
              <div class="detail-item">
                <label>Timezone</label>
                <span>Pacific Standard Time (PST)</span>
              </div>
              <div class="detail-item">
                <label>Operating Hours</label>
                <span>Mon-Fri 9:00 AM - 6:00 PM</span>
              </div>
              <div class="detail-item">
                <label>Phone</label>
                <span>+****************</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="location-activity-section">
        <h3>Location Activity</h3>
        <div class="activity-placeholder">
          <i class="pi pi-building" style="font-size: 2rem; color: var(--surface-400); margin-bottom: 1rem;"></i>
          <p>Location activity and management tools will be displayed here.</p>
          <p class="feature-note">
            <strong>Planned features:</strong><br>
            • Interactive map integration<br>
            • Associated customers list<br>
            • Service area management<br>
            • Location-based analytics<br>
            • Equipment and assets tracking
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'

// Props
const props = defineProps<{
  id: string
}>()

const router = useRouter()

// For now, generate a placeholder location name based on the ID
const locationName = computed(() => {
  return `Location ${props.id}`
})

// Event handlers
const handleBack = () => {
  router.push('/customers/locations')
}

const handleEditLocation = () => {
  console.log('Edit location:', props.id)
  // TODO: Implement edit location functionality
}

const handleLocationActions = () => {
  console.log('Location actions for:', props.id)
  // TODO: Implement location actions menu
}
</script>

<style scoped>
.location-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

.location-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--surface-200);
  background: white;
  height: 64px;
  flex-shrink: 0;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background-color: var(--surface-100) !important;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.location-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.location-info-section {
  margin-bottom: 3rem;
}

.location-overview {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: var(--surface-50);
  border-radius: 12px;
  border: 1px solid var(--surface-200);
}

.location-avatar {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--surface-200);
  flex-shrink: 0;
}

.location-basic-info {
  flex: 1;
}

.location-name {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
}

.location-id {
  margin: 0 0 1rem 0;
  color: var(--text-color-secondary);
  font-size: 1rem;
}

.location-status {
  display: flex;
  gap: 0.5rem;
}

.location-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.detail-section {
  background: white;
  border: 1px solid var(--surface-200);
  border-radius: 8px;
  padding: 1.5rem;
}

.detail-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.detail-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item label {
  font-weight: 600;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  color: var(--text-color);
  font-size: 1rem;
}

.location-activity-section {
  background: white;
  border: 1px solid var(--surface-200);
  border-radius: 8px;
  padding: 1.5rem;
}

.location-activity-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.activity-placeholder {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-color-secondary);
}

.activity-placeholder p {
  margin: 0 0 1rem 0;
}

.feature-note {
  background: var(--surface-100);
  padding: 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  text-align: left;
  max-width: 400px;
  margin: 1.5rem auto 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .location-details {
    grid-template-columns: 1fr;
  }
  
  .location-overview {
    flex-direction: column;
    text-align: center;
  }
  
  .location-content {
    padding: 1rem;
  }
  
  .location-header {
    padding: 1rem;
  }
}
</style>
