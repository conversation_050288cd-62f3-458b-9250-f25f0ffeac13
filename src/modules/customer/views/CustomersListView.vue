<template>
  <div class="customers-list">
    <div class="header">
      <BravoTitlePage>Customers</BravoTitlePage>
      <div class="button-group">
        <BravoButton 
          label="Add Customer" 
          icon="pi pi-plus" 
          @click="showAddCustomerModal" 
        />
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-row">
      <div class="search-filters">
        <BravoIconField>
          <BravoInputIcon class="pi pi-search" />
          <BravoInputText 
            v-model="searchQuery" 
            @input="handleSearchInput" 
            @keydown="handleSearchKeydown" 
            placeholder="Search customers..." 
            :disabled="isLoading"
          />
        </BravoIconField>
      </div>
    </div>

    <!-- Customer List Table -->
    <BravoDataTable 
      :value="filteredCustomers"
      :columns="[]" 
      dataKey="id"
      :rowHover="true"
      :stripedRows="false"
      :showGridlines="false" 
      class="p-datatable-sm" 
      v-model:selection="selectedCustomers"
      @selection-change="onSelectionChange"
      :sortField="sortField"
      :sortOrder="sortOrder"
      @sort="onSort"
      removableSort
      :loading="false"
      :rows="rowsPerPage"
      :totalRecords="totalCustomers"
      :paginator="true"
      :lazy="false"
      @page="onPage"
      :first="(currentPage - 1) * rowsPerPage"
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      :rowsPerPageOptions="[10, 25, 50]"
      scrollable
      scrollHeight="calc(100vh - 230px)"
      paginatorPosition="bottom"
      paginatorClassName="fixed-paginator"
    >
      <template #header>
        <div>
          <span v-if="selectedCustomers.length > 0">
            {{ selectedCustomers.length }} of {{ filteredCustomers.length }} customers selected
          </span>
          <span v-else>
            Showing {{ filteredCustomers.length }} of {{ totalCustomers }} customers
          </span>
        </div>
      </template>
      
      <template #empty>
        <div>No customers found</div>
      </template>
      
      <Column selectionMode="multiple" headerStyle="width: 3rem" />
      
      <Column field="name" header="Customer Name" sortable style="min-width: 300px">
        <template #body="{ data }">
          <a 
            :href="`/customers/${data.id}`"
            class="customer-name-link" 
            @click="(event) => handleCustomerClick(data, event)"
          >
            {{ data.name }}
          </a>
        </template>
      </Column>
      
      <Column field="status" header="Status" sortable style="min-width: 100px">
        <template #body="{ data }">
          <BravoTag
            :value="data.status" 
            :state="data.status.toLowerCase()" 
          />
        </template>
      </Column>
      
      <Column field="type" header="Type" sortable style="min-width: 120px">
        <template #body="{ data }">
          <span>{{ data.type }}</span>
        </template>
      </Column>
      
      <Column field="locations" header="Locations" sortable style="min-width: 100px">
        <template #body="{ data }">
          <span>{{ data.locationCount }}</span>
        </template>
      </Column>
      
      <Column field="contacts" header="Contacts" sortable style="min-width: 100px">
        <template #body="{ data }">
          <span>{{ data.contactCount }}</span>
        </template>
      </Column>
      
      <Column field="lastActivity" header="Last Activity" sortable style="min-width: 150px">
        <template #body="{ data }">
          <BravoTimestamp 
            v-if="data.lastActivity" 
            :datetime="data.lastActivity" 
          />
          <span v-else>N/A</span>
        </template>
      </Column>
      
      <Column :exportable="false" style="width: 4rem">
        <template #body="{ data }">
          <div class="flex align-items-center justify-content-center">
            <BravoButton
              icon="pi pi-ellipsis-v"
              text
              severity="secondary"
              class="table-action-button"
              @click="(e) => toggleMenu(e, data)"
              aria-label="Customer actions"
              v-tooltip.bottom="{ value: 'Actions', showDelay: 400 }"
            />
          </div>
        </template>
      </Column>
    </BravoDataTable>

    <BravoMenu ref="menu" :model="menuItems" :popup="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import BravoDataTable from 'primevue/datatable'
import Column from 'primevue/column'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoIconField from '@services/ui-component-library/components/BravoIconField.vue'
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue'
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue'
import Tooltip from 'primevue/tooltip'
import type { MenuItem } from 'primevue/menuitem'

const router = useRouter()

// Component state
const selectedCustomers = ref<any[]>([])
const sortField = ref<string>('')
const sortOrder = ref<number>(1)
const currentPage = ref(1)
const rowsPerPage = ref(25)
const menu = ref()
const selectedRecord = ref<any>(null)
const searchQuery = ref('')
const isLoading = ref(false)

// Register Tooltip directive
const vTooltip = Tooltip

// Mock customer data
const customers = ref([
  {
    id: '123',
    name: 'Alan\'s Burger',
    status: 'Active',
    type: 'Restaurant',
    locationCount: 3,
    contactCount: 5,
    lastActivity: new Date('2024-04-01T10:30:00Z').toISOString()
  },
  {
    id: '456',
    name: 'Tech Solutions Inc',
    status: 'Active',
    type: 'Technology',
    locationCount: 1,
    contactCount: 8,
    lastActivity: new Date('2024-03-28T14:15:00Z').toISOString()
  },
  {
    id: '789',
    name: 'Global Services Ltd',
    status: 'Inactive',
    type: 'Services',
    locationCount: 5,
    contactCount: 12,
    lastActivity: new Date('2024-03-15T09:45:00Z').toISOString()
  },
  {
    id: '101',
    name: 'Innovation Corp',
    status: 'Active',
    type: 'Manufacturing',
    locationCount: 2,
    contactCount: 6,
    lastActivity: new Date('2024-04-02T16:20:00Z').toISOString()
  },
  {
    id: '102',
    name: 'Digital Dynamics',
    status: 'Active',
    type: 'Software',
    locationCount: 1,
    contactCount: 4,
    lastActivity: new Date('2024-03-30T11:10:00Z').toISOString()
  }
])

// Computed properties
const filteredCustomers = computed(() => {
  if (!searchQuery.value.trim()) {
    return customers.value
  }
  
  const query = searchQuery.value.toLowerCase().trim()
  return customers.value.filter(customer => 
    customer.name.toLowerCase().includes(query) ||
    customer.status.toLowerCase().includes(query) ||
    customer.type.toLowerCase().includes(query)
  )
})

const totalCustomers = computed(() => customers.value.length)

// Search handlers
const handleSearchInput = () => {
  // Reset to first page when searching
  currentPage.value = 1
}

const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    // Perform search on Enter key
    handleSearchInput()
  }
}

// Event handlers
const handleCustomerClick = (customer: any, event?: MouseEvent) => {
  // Allow default behavior for middle mouse button and Ctrl+click
  if (event && (event.button === 1 || event.ctrlKey || event.metaKey)) {
    return;
  }
  
  // For regular left clicks, prevent default and use router navigation
  if (event) {
    event.preventDefault();
  }
  router.push(`/customers/${customer.id}`);
}

const onSelectionChange = (selection: any[]) => {
  selectedCustomers.value = selection
}

const onSort = (event: any) => {
  sortField.value = event.sortField
  sortOrder.value = event.sortOrder
}

const onPage = (event: any) => {
  currentPage.value = event.page + 1
  rowsPerPage.value = event.rows
}

const showAddCustomerModal = () => {
  console.log('Add customer modal')
  // TODO: Implement add customer modal
}

// Menu functionality
const editRecord = (record: any) => {
  console.log('Edit customer:', record)
  // TODO: Implement edit customer functionality
}

const deleteRecord = (record: any) => {
  console.log('Delete customer:', record)
  // TODO: Implement delete customer functionality
}

const viewRecord = (record: any) => {
  router.push(`/customers/${record.id}`)
}

const menuItems = computed((): MenuItem[] => {
  if (!selectedRecord.value) return []
  
  return [
    {
      label: 'View',
      icon: 'pi pi-eye',
      command: () => {
        viewRecord(selectedRecord.value)
      }
    },
    {
      label: 'Edit',
      icon: 'pi pi-pencil',
      command: () => {
        editRecord(selectedRecord.value)
      }
    },
    {
      label: 'Delete',
      icon: 'pi pi-trash',
      command: () => {
        deleteRecord(selectedRecord.value)
      }
    }
  ]
})

const toggleMenu = (event: Event, record: any) => {
  selectedRecord.value = record
  nextTick(() => {
    menu.value.toggle(event)
  })
}
</script>

<style scoped>
.customers-list {
  padding: 1rem 2rem 0rem 2rem;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  max-height: 100vh;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  align-items: center;
  flex: 0 0 auto;
}

.button-group {
  display: flex;
  gap: .75rem;
}

.search-row {
  margin-bottom: 1.5rem;
  flex: 0 0 auto;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

/* Make the data table take up available space and paginator fixed at bottom */
:deep(.p-datatable) {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: calc(100vh - 230px);
  position: relative;
}

:deep(.p-datatable-wrapper) {
  flex-grow: 1;
  overflow: auto;
  min-height: 200px;
  padding-bottom: 56px;
}

:deep(.p-paginator) {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--surface-a);
  z-index: 1;
}

.customer-name-link {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    color: var(--blue-650);
    text-decoration: underline;
  }
}

:deep(.p-datatable-table) {
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0 !important;
}

:deep(.p-datatable-tbody > tr.p-highlight) {
  background-color: var(--primary-50) !important;
  color: var(--primary-700) !important;
}

:deep(.p-datatable-tbody > tr.p-highlight:hover) {
  background-color: var(--primary-100) !important;
}

:deep(.p-checkbox .p-checkbox-box.p-highlight) {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

:deep(.p-datatable .p-datatable-header) {
  border: none !important;
}

.table-action-button {
  width: 2rem !important;
  height: 2rem !important;
  padding: 0.5rem !important;
}

:deep(.fixed-paginator) {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  margin: 0 !important;
}
</style>
