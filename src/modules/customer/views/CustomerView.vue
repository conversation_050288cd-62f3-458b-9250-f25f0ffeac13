<template>
  <div class="customer-view">
    <div class="customer-header">
      <div class="header-nav">
        <BravoButton
          icon="pi pi-arrow-left"
          text
          @click="handleBack"
          class="back-button"
          aria-label="Back to customers"
        />
        <div class="customer-title">
          <h1>{{ customerName }}</h1>
          <span class="customer-subtitle">Customer Details</span>
        </div>
      </div>
    </div>

    <div class="customer-content">
      <div class="customer-layout">
        <!-- Left Column: Customer Profile -->
        <div class="profile-column">
          <CustomerProfilePanel :customerId="id" />
        </div>

        <!-- Middle Column: Customer Activity -->
        <div class="activity-column">
          <CustomerActivityPanel :customerId="id" />
        </div>

        <!-- Right Column: Customer Details -->
        <div class="details-column">
          <CustomerDetailPanel :customerId="id" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import CustomerProfilePanel from '../components/CustomerProfilePanel.vue'
import CustomerActivityPanel from '../components/CustomerActivityPanel.vue'
import CustomerDetailPanel from '../components/CustomerDetailPanel.vue'

// Props
const props = defineProps<{
  id: string
}>()

const router = useRouter()

// For now, generate a placeholder customer name based on the ID
const customerName = computed(() => {
  const names = ['Alan\'s Burger', 'Tech Solutions Inc', 'Global Services Ltd', 'Innovation Corp', 'Digital Dynamics']
  const index = parseInt(props.id) % names.length
  return names[index] || `Customer ${props.id}`
})

// Event handlers
const handleBack = () => {
  router.push('/customers')
}
</script>

<style scoped>
.customer-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--surface-0);
  overflow: hidden;
}

.customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--surface-200);
  background: white;
  height: 80px;
  flex-shrink: 0;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background-color: var(--surface-100) !important;
}

.customer-title h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
}

.customer-subtitle {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.customer-content {
  flex: 1;
  overflow: hidden;
  background: var(--surface-50);
}

.customer-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  height: 100%;
  gap: 0;
}

.profile-column {
  background: white;
  border-right: 1px solid var(--surface-200);
  overflow-y: auto;
}

.activity-column {
  background: white;
  overflow-y: auto;
}

.details-column {
  background: white;
  border-left: 1px solid var(--surface-200);
  overflow-y: auto;
}

/* Responsive design */
@media (max-width: 1200px) {
  .customer-layout {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media (max-width: 1024px) {
  .customer-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .profile-column,
  .details-column {
    border-right: none;
    border-left: none;
    border-bottom: 1px solid var(--surface-200);
  }
  
  .activity-column {
    order: 1;
  }
  
  .profile-column {
    order: 2;
  }
  
  .details-column {
    order: 3;
  }
}

@media (max-width: 768px) {
  .customer-header {
    padding: 1rem;
    height: 64px;
  }
  
  .customer-title h1 {
    font-size: 1.5rem;
  }
}
</style>
