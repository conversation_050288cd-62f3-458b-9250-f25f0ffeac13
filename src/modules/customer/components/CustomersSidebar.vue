<template>
  <div 
    class="customers-sidebar" 
    :class="{ minimized: isMinimized }"
    :style="!isMinimized ? { width: `${panelWidth}px` } : {}"
  >
    <div class="panel-header">
      <div class="header-content">
        <BravoTitlePage v-if="!isMinimized">Customers</BravoTitlePage>
        <div v-if="!isMinimized" class="header-actions">
          <BravoButton
            icon="pi pi-plus"
            severity="secondary"
            text
            @click="handleAddCustomer"
            aria-label="Add new customer"
          />
          <BravoButton
            icon="pi pi-search"
            severity="secondary"
            text
            @click="handleSearch"
            aria-label="Search customers"
          />
        </div>
      </div>
    </div>

    <div class="panel-content" v-if="!isMinimized">
      <div class="navigation-menu">
        <div 
          v-for="item in navigationItems" 
          :key="item.id"
          class="nav-item"
          :class="{ active: selectedItem?.id === item.id }"
          @click="selectItem(item)"
        >
          <i :class="item.icon"></i>
          <span class="nav-label">{{ item.label }}</span>
          <BravoBadge 
            v-if="item.badge" 
            :value="item.badge" 
            class="nav-badge"
          />
        </div>
      </div>
    </div>

    <!-- Minimized icons view -->
    <div class="panel-content minimized-icons" v-if="isMinimized">
      <div 
        v-for="item in navigationItems" 
        :key="item.id" 
        class="minimized-icon" 
        :class="{ active: selectedItem?.id === item.id }"
        @click="selectItem(item)"
        v-tooltip.right="{
          value: item.label,
          showDelay: 400
        }"
      >
        <i :class="item.icon"></i>
        <BravoBadge 
          v-if="item.badge" 
          :value="item.badge" 
          class="icon-badge"
        />
      </div>
    </div>

    <div class="panel-footer">
      <BravoButton
        :icon="isMinimized ? 'pi pi-chevron-right' : 'pi pi-chevron-left'"
        text
        severity="secondary"
        @click="toggleSidebar"
        :aria-label="isMinimized ? 'Expand sidebar' : 'Collapse sidebar'"
        v-tooltip.top="{
          value: isMinimized ? 'Expand sidebar' : 'Collapse sidebar',
          showDelay: 400
        }"
      />
    </div>

    <!-- Resize handle -->
    <div 
      v-if="!isMinimized" 
      class="resize-handle" 
      @mousedown="startResize"
      title="Drag to resize"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoTitle2 from '@services/ui-component-library/components/BravoTypography/BravoTitle2.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import Tooltip from 'primevue/tooltip'

// Register the tooltip directive
const vTooltip = Tooltip

// Define emits
const emit = defineEmits<{
  'item-selected': [item: NavigationItem]
}>()

interface NavigationItem {
  id: string
  label: string
  icon: string
  badge?: string | number
}

// Component state
const isMinimized = ref(false)
const panelWidth = ref(280)
const isResizing = ref(false)
const selectedItem = ref<NavigationItem | null>(null)

// Navigation items - placeholder data
const navigationItems = ref<NavigationItem[]>([
  {
    id: 'customers',
    label: 'Customers',
    icon: 'pi pi-users',
    badge: '1,234'
  },
  {
    id: 'locations',
    label: 'Locations',
    icon: 'pi pi-building',
    badge: '156'
  },
  {
    id: 'contacts',
    label: 'Contacts',
    icon: 'pi pi-address-book',
    badge: '2,891'
  }
])

// Initialize with first item selected
onMounted(() => {
  if (navigationItems.value.length > 0) {
    selectedItem.value = navigationItems.value[0]
    emit('item-selected', selectedItem.value)
  }
  
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
  document.removeEventListener('mousemove', resizePanel)
  document.removeEventListener('mouseup', stopResize)
})

const checkScreenSize = () => {
  isMinimized.value = window.innerWidth <= 800
}

const toggleSidebar = () => {
  isMinimized.value = !isMinimized.value
}

const selectItem = (item: NavigationItem) => {
  selectedItem.value = item
  emit('item-selected', item)
  
  // Auto-expand when selecting an item in minimized mode
  if (isMinimized.value) {
    isMinimized.value = false
  }
}

const handleAddCustomer = () => {
  console.log('Add customer clicked')
  // TODO: Implement add customer functionality
}

const handleSearch = () => {
  console.log('Search clicked')
  // TODO: Implement search functionality
}

// Resize functionality
const startResize = (event: MouseEvent) => {
  if (isMinimized.value) return
  
  isResizing.value = true
  document.addEventListener('mousemove', resizePanel)
  document.addEventListener('mouseup', stopResize)
  event.preventDefault()
}

const resizePanel = (event: MouseEvent) => {
  if (!isResizing.value) return
  
  const newWidth = Math.max(280, Math.min(500, event.clientX))
  panelWidth.value = newWidth
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', resizePanel)
  document.removeEventListener('mouseup', stopResize)
}
</script>

<style scoped>
.customers-sidebar {
  width: 280px;
  background-color: var(--surface-50);
  border-right: 1px solid var(--surface-300);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
}

.customers-sidebar.minimized {
  width: 60px;
}

.panel-header {
  padding: 1rem 1.5rem;
  height: 64px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--surface-200);
}

.customers-sidebar.minimized .panel-header {
  padding: 0.5rem;
  justify-content: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.customers-sidebar.minimized .header-content {
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.panel-content {
  padding: 1rem;
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.navigation-menu {
  margin-bottom: 2rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 0.25rem;
}

.nav-item:hover {
  background-color: var(--surface-100);
}

.nav-item.active {
  background-color: var(--primary-50);
  color: var(--primary-600);
  font-weight: 600;
}

.nav-item i {
  font-size: 1.1rem;
  color: var(--surface-600);
  width: 20px;
}

.nav-item.active i {
  color: var(--primary-600);
}

.nav-label {
  flex: 1;
}

.nav-badge {
  font-size: 0.75rem;
}

.minimized-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding-top: 0.5rem;
  overflow-y: auto;
}

.minimized-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--surface-100);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.2s;
}

.minimized-icon:hover {
  background-color: var(--surface-200);
}

.minimized-icon.active {
  background-color: var(--primary-100);
  border: 2px solid var(--primary-500);
}

.minimized-icon i {
  font-size: 1.2rem;
  color: var(--surface-600);
}

.minimized-icon.active i {
  color: var(--primary-600);
}

.icon-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 10;
}

.panel-footer {
  padding: 0.5rem;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--surface-200);
}

.customers-sidebar.minimized .panel-footer {
  justify-content: center;
}

.resize-handle {
  position: absolute;
  top: 0;
  right: -5px;
  width: 10px;
  height: 100%;
  cursor: ew-resize;
  z-index: 100;
}

.resize-handle:hover::after {
  content: '';
  position: absolute;
  top: 0;
  right: 5px;
  width: 2px;
  height: 100%;
  background-color: var(--surface-300);
  opacity: 0.5;
}

/* Media query for mobile */
@media (max-width: 800px) {
  .customers-sidebar:not(.minimized) {
    position: absolute;
    z-index: 10;
    height: 100%;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  }
}
</style>
