<template>
  <div class="customer-detail-panel">
    <!-- Pinned Fields Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">PINNED FIELDS</h3>
      </div>
      <div class="section-content">
        <div class="pinned-fields">
          <div class="field-item">FIELD 1</div>
          <div class="field-item">FIELD 2</div>
          <div class="field-item">FIELD 3</div>
          <div class="field-item">FIELD 4</div>
        </div>
      </div>
    </div>

    <!-- Customer Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">CUSTOMER</h3>
      </div>
      <div class="section-content">
        <div class="section-placeholder">
          <p>Customer information and details will be displayed here.</p>
        </div>
      </div>
    </div>

    <!-- Locations Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">LOCATIONS</h3>
      </div>
      <div class="section-content">
        <div class="section-placeholder">
          <p>Customer locations will be listed here.</p>
        </div>
      </div>
    </div>

    <!-- Contacts Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">CONTACTS</h3>
      </div>
      <div class="section-content">
        <div class="section-placeholder">
          <p>Customer contacts will be displayed here.</p>
        </div>
      </div>
    </div>

    <!-- Products Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">PRODUCTS</h3>
      </div>
      <div class="section-content">
        <div class="section-placeholder">
          <p>Customer products and services will be shown here.</p>
        </div>
      </div>
    </div>

    <!-- Cases Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">CASES</h3>
      </div>
      <div class="section-content">
        <div class="section-placeholder">
          <p>Customer support cases will be listed here.</p>
        </div>
      </div>
    </div>

    <!-- Journeys Section -->
    <div class="detail-section">
      <div class="section-header">
        <h3 class="section-title">JOURNEYS</h3>
      </div>
      <div class="section-content">
        <div class="section-placeholder">
          <p>Customer journeys and workflows will be displayed here.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
const props = defineProps<{
  customerId: string
}>()
</script>

<style scoped>
.customer-detail-panel {
  padding: 1.5rem;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-section {
  display: flex;
  flex-direction: column;
}

.section-header {
  margin-bottom: 0.75rem;
}

.section-title {
  margin: 0;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-content {
  flex: 1;
}

.pinned-fields {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-item {
  padding: 0.75rem;
  background: var(--surface-100);
  border-radius: 4px;
  border: 1px solid var(--surface-300);
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  text-align: center;
  font-weight: 500;
}

.section-placeholder {
  padding: 1rem;
  background: var(--surface-100);
  border-radius: 4px;
  border: 1px solid var(--surface-300);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-placeholder p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  text-align: center;
  line-height: 1.4;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .customer-detail-panel {
    padding: 1rem;
    gap: 1rem;
  }
  
  .pinned-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .pinned-fields {
    grid-template-columns: 1fr;
  }
}
</style>
