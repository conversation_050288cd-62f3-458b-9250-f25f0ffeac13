<template>
  <div class="customer-profile-panel">
    <!-- Customer Avatar and Basic Info -->
    <div class="profile-header">
      <div class="customer-avatar">
        <BravoAvatar 
          :label="customerInitials"
          size="xlarge"
          shape="circle"
          class="avatar"
        />
      </div>
      
      <div class="customer-basic-info">
        <h2 class="customer-name">{{ customerName }}</h2>
        <p class="last-interaction">{{ lastInteraction }}</p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <BravoButton
        icon="pi pi-file-edit"
        label="NOTE"
        severity="secondary"
        outlined
        size="small"
        class="action-btn"
      />
      <BravoButton
        icon="pi pi-check-square"
        label="TASK"
        severity="secondary"
        outlined
        size="small"
        class="action-btn"
      />
      <BravoButton
        icon="pi pi-briefcase"
        label="CASE"
        severity="secondary"
        outlined
        size="small"
        class="action-btn"
      />
      <BravoButton
        icon="pi pi-map-marker"
        label="JOURNEY"
        severity="secondary"
        outlined
        size="small"
        class="action-btn"
      />
    </div>

    <!-- Metrics Section -->
    <div class="metrics-section">
      <div class="metric-item">
        <span class="metric-label">SENTIMENT</span>
        <div class="metric-placeholder"></div>
      </div>
      <div class="metric-item">
        <span class="metric-label">CSAT</span>
        <div class="metric-placeholder"></div>
      </div>
      <div class="metric-item">
        <span class="metric-label">NPS</span>
        <div class="metric-placeholder"></div>
      </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
      <h3 class="section-title">Summary</h3>
      <div class="summary-placeholder">
        <p>Customer summary and key information will be displayed here. This could include recent interactions, preferences, and important notes.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'

// Props
const props = defineProps<{
  customerId: string
}>()

// For now, generate placeholder data based on the customer ID
const customerName = computed(() => {
  const names = ['Alan\'s Burger', 'Tech Solutions Inc', 'Global Services Ltd', 'Innovation Corp', 'Digital Dynamics']
  const index = parseInt(props.customerId) % names.length
  return names[index] || `Customer ${props.customerId}`
})

const customerInitials = computed(() => {
  return customerName.value
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .substring(0, 2)
})

const lastInteraction = computed(() => {
  return 'LAST INTERACTION: APR 1 (2D AGO)'
})
</script>

<style scoped>
.customer-profile-panel {
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.customer-avatar {
  display: flex;
  justify-content: center;
}

.avatar {
  width: 80px;
  height: 80px;
}

.customer-basic-info {
  width: 100%;
}

.customer-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.last-interaction {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.action-btn {
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 2.5rem;
}

.metrics-section {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.metric-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-placeholder {
  width: 100%;
  height: 60px;
  background: var(--surface-100);
  border-radius: 4px;
  border: 1px solid var(--surface-300);
}

.summary-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.summary-placeholder {
  flex: 1;
  padding: 1rem;
  background: var(--surface-100);
  border-radius: 4px;
  border: 1px solid var(--surface-300);
  min-height: 120px;
}

.summary-placeholder p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .customer-profile-panel {
    padding: 1rem;
  }
  
  .action-buttons {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
  
  .metrics-section {
    justify-content: space-around;
  }
}
</style>
