<template>
  <div class="customer-activity-panel">
    <!-- Header with Tabs -->
    <div class="activity-header">
      <div class="activity-tabs">
        <button 
          class="tab-button"
          :class="{ active: activeTab === 'events' }"
          @click="activeTab = 'events'"
        >
          Events
        </button>
        <button 
          class="tab-button"
          :class="{ active: activeTab === 'details' }"
          @click="activeTab = 'details'"
        >
          Details
        </button>
      </div>
    </div>

    <!-- Events Tab Content -->
    <div v-if="activeTab === 'events'" class="events-content">
      <!-- Events Header -->
      <div class="events-header">
        <h3 class="section-title">Events</h3>
        <div class="filter-section">
          <span class="filter-label">Filter by: Notes</span>
        </div>
      </div>

      <!-- Event Items -->
      <div class="events-list">
        <div 
          v-for="event in mockEvents" 
          :key="event.id"
          class="event-item"
        >
          <div class="event-placeholder">
            <!-- Event content will go here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Details Tab Content -->
    <div v-if="activeTab === 'details'" class="details-content">
      <div class="details-header">
        <h3 class="section-title">Details</h3>
      </div>
      
      <div class="details-placeholder">
        <p>Customer details and additional information will be displayed here.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Props
const props = defineProps<{
  customerId: string
}>()

// Component state
const activeTab = ref('events')

// Mock data for events
const mockEvents = ref([
  { id: 1 },
  { id: 2 },
  { id: 3 },
  { id: 4 },
  { id: 5 }
])
</script>

<style scoped>
.customer-activity-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activity-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid var(--surface-200);
}

.activity-tabs {
  display: flex;
  gap: 0;
  margin-bottom: -1px;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-color-secondary);
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  color: var(--text-color);
  background: var(--surface-50);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: white;
}

.events-content,
.details-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.events-header,
.details-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--surface-200);
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.filter-section {
  margin-top: 0.5rem;
}

.filter-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.events-list {
  flex: 1;
  padding: 1rem 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.event-item {
  width: 100%;
}

.event-placeholder {
  width: 100%;
  height: 80px;
  background: var(--surface-100);
  border-radius: 6px;
  border: 1px solid var(--surface-300);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.details-placeholder {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.details-placeholder p {
  margin: 0;
  color: var(--text-color-secondary);
  text-align: center;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .activity-header,
  .events-header,
  .details-header {
    padding: 1rem;
  }
  
  .events-list {
    padding: 1rem;
  }
  
  .details-placeholder {
    padding: 1rem;
  }
}
</style>
