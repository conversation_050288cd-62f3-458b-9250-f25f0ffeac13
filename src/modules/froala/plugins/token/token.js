/**
 * Token Plugin for Froala Editor
 * Allows inserting tokens (variables) into the editor content
 */
import "./token.css";
import FroalaEditor from "froala-editor";

const token = function (editor) {
  // Private variables
  let _editor = editor;

  // Add popup templates
  FroalaEditor.POPUP_TEMPLATES["token.edit"] = "[_BUTTONS_]";
  FroalaEditor.POPUP_TEMPLATES["token.insert"] =
    "[_BUTTONS_][_TOKEN_SELECTOR_][_BUTTON_LAYER_]";

  // Check if element is a token block
  function isTokenBlock(el) {
    return el && el.tagName === "SPAN" && el.dataset.btBlock === "bt-token";
  }

  // Get the current token element if it exists
  function get() {
    if (_editor.$wp) {
      let c_el = _editor.selection.ranges(0).commonAncestorContainer;

      try {
        if (
          c_el &&
          ((c_el.contains && c_el.contains(_editor.el)) ||
            !_editor.el.contains(c_el) ||
            _editor.el === c_el)
        ) {
          c_el = null;
        }
      } catch (ex) {
        c_el = null;
      }

      if (c_el && isTokenBlock(c_el)) {
        return c_el;
      }

      let s_el = _editor.selection.element();
      let e_el = _editor.selection.endElement();

      if (!isTokenBlock(s_el) && !_editor.node.isElement(s_el)) {
        s_el = _editor
          .$(s_el)
          .parentsUntil(_editor.$el, 'span[data-bt-block="bt-token"]')
          .get(0);
      }

      if (!isTokenBlock(e_el) && !_editor.node.isElement(e_el)) {
        e_el = _editor
          .$(e_el)
          .parentsUntil(_editor.$el, 'span[data-bt-block="bt-token"]')
          .get(0);
      }

      try {
        if (
          e_el &&
          ((e_el.contains && e_el.contains(_editor.el)) ||
            !_editor.el.contains(e_el) ||
            _editor.el === e_el)
        ) {
          e_el = null;
        }
      } catch (ex) {
        e_el = null;
      }

      try {
        if (
          s_el &&
          ((s_el.contains && s_el.contains(_editor.el)) ||
            !_editor.el.contains(s_el) ||
            _editor.el === s_el)
        ) {
          s_el = null;
        }
      } catch (ex) {
        s_el = null;
      }

      if (e_el && e_el === s_el && isTokenBlock(e_el)) {
        // We do not clicking at the end / input of links because in IE the selection is changing shortly after mouseup.
        if (
          (_editor.browser.msie || _editor.helpers.isMobile()) &&
          (_editor.selection.info(s_el).atEnd ||
            _editor.selection.info(s_el).atStart)
        ) {
          return null;
        }
        return s_el;
      }
    } else if (isTokenBlock(_editor.el)) {
      return _editor.el;
    }
    return null;
  }

  // Get all selected tokens
  function allSelected() {
    const selectedTokens = [];
    let range;
    let containerEl;
    let tokens;
    let tokenRange;

    if (_editor.win.getSelection) {
      const sel = _editor.win.getSelection();

      if (sel.getRangeAt && sel.rangeCount) {
        tokenRange = _editor.doc.createRange();

        for (let r = 0; r < sel.rangeCount; ++r) {
          range = sel.getRangeAt(r);
          containerEl = range.commonAncestorContainer;

          if (containerEl && containerEl.nodeType !== 1) {
            containerEl = containerEl.parentNode;
          }

          if (containerEl && isTokenBlock(containerEl)) {
            selectedTokens.push(containerEl);
          } else {
            tokens = containerEl.getElementsByTagName("span");
            for (let i = 0; i < tokens.length; ++i) {
              if (isTokenBlock(tokens[i])) {
                tokenRange.selectNodeContents(tokens[i]);
                if (
                  tokenRange.compareBoundaryPoints(range.END_TO_START, range) <
                    1 &&
                  tokenRange.compareBoundaryPoints(range.START_TO_END, range) >
                    -1
                ) {
                  selectedTokens.push(tokens[i]);
                }
              }
            }
          }
        }
      }
    } else if (
      _editor.doc.selection &&
      _editor.doc.selection.type !== "Control"
    ) {
      range = _editor.doc.selection.createRange();
      containerEl = range.parentElement();

      if (isTokenBlock(containerEl)) {
        selectedTokens.push(containerEl);
      } else {
        tokens = containerEl.getElementsByTagName("span");
        tokenRange = _editor.doc.body.createTextRange();

        for (let j = 0; j < tokens.length; ++j) {
          if (isTokenBlock(tokens[j])) {
            tokenRange.moveToElementText(tokens[j]);
            if (
              tokenRange.compareEndPoints("StartToEnd", range) > -1 &&
              tokenRange.compareEndPoints("EndToStart", range) < 1
            ) {
              selectedTokens.push(tokens[j]);
            }
          }
        }
      }
    }

    return selectedTokens;
  }

  // Edit token functionality
  function edit(e) {
    if (_editor.core.hasFocus()) {
      hideEditPopup();

      if (
        e &&
        e.type === "keyup" &&
        (e.altKey || e.which === FroalaEditor.KEYCODE.ALT)
      ) {
        return true;
      }

      setTimeout(
        () => {
          if (!e || (e && (e.which === 1 || e.type !== "mouseup"))) {
            const token = get();
            if (token) {
              if (_editor.image) {
                const contents = _editor.node.contents(token);
                if (contents.length === 1 && contents[0].tagName === "IMG") {
                  const range = _editor.selection.ranges(0);
                  if (range.startOffset === 0 && range.endOffset === 0) {
                    _editor.$(token).before(FroalaEditor.MARKERS);
                  } else {
                    _editor.$(token).after(FroalaEditor.MARKERS);
                  }
                  _editor.selection.restore();
                  return false;
                }
              }

              if (e) e.stopPropagation();
              showEditPopup(token);
            }
          }
        },
        _editor.helpers.isIOS() ? 100 : 0
      );
    }
  }

  // Show edit popup for token
  function showEditPopup(token) {
    let $popup = _editor.popups.get("token.edit");
    if (!$popup) {
      $popup = initEditPopup();
    }

    const $token = _editor.$(token);

    if (!_editor.popups.isVisible("token.edit")) {
      _editor.popups.refresh("token.edit");
    }

    _editor.popups.setContainer("token.edit", _editor.$sc);
    const left = $token.offset().left + $token.outerWidth() / 2;
    const top = $token.offset().top + $token.outerHeight();

    _editor.popups.show("token.edit", left, top, $token.outerHeight());
  }

  // Hide edit popup
  function hideEditPopup() {
    _editor.popups.hide("token.edit");
  }

  // Initialize edit popup
  function initEditPopup() {
    let buttonsHtml = "";
    const buttons = _editor.opts.tokenEditButtons || [
      "tokenEdit",
      "tokenRemove",
    ];

    if (isTokenBlock(_editor.el) && buttons.indexOf("tokenRemove") >= 0) {
      buttons.splice(buttons.indexOf("tokenRemove"), 1);
    }

    buttonsHtml =
      '<div class="fr-buttons">' + _editor.button.buildList(buttons) + "</div>";

    const template = {
      buttons: buttonsHtml,
    };

    // Create the popup
    const $popup = _editor.popups.create("token.edit", template);

    if (_editor.$wp) {
      _editor.events.$on(_editor.$wp, "scroll.token-edit", () => {
        const currentToken = get();
        if (currentToken && _editor.popups.isVisible("token.edit")) {
          showEditPopup(currentToken);
        }
      });
    }

    return $popup;
  }

  // Remove token
  function remove() {
    const token = get();
    if (_editor.events.trigger("token.beforeRemove", [token]) === false) {
      return false;
    }

    if (token) {
      _editor.selection.save();
      _editor.$(token).replaceWith("");
      _editor.selection.restore();
      hideEditPopup();
    }
  }

  // Split selection for token
  function splitSelection() {
    if (!_editor.selection.isCollapsed()) {
      _editor.selection.save();
      let markers = _editor.$el
        .find(".fr-marker")
        .addClass("fr-unprocessed")
        .toArray();

      while (markers.length) {
        const $marker = _editor.$(markers.pop());
        $marker.removeClass("fr-unprocessed");

        // Get deepest parent
        const deep_parent = _editor.node.deepestParent($marker.get(0));

        if (deep_parent) {
          let node = $marker.get(0);
          let close_str = "";
          let open_str = "";

          do {
            node = node.parentNode;
            if (!_editor.node.isBlock(node)) {
              close_str = close_str + _editor.node.closeTagString(node);
              open_str = _editor.node.openTagString(node) + open_str;
            }
          } while (node !== deep_parent);

          const marker_str =
            _editor.node.openTagString($marker.get(0)) +
            $marker.html() +
            _editor.node.closeTagString($marker.get(0));
          $marker.replaceWith('<span id="fr-break"></span>');
          let h = deep_parent.outerHTML;
          h = h.replace(
            /<span id="fr-break"><\/span>/g,
            close_str + marker_str + open_str
          );
          deep_parent.outerHTML = h;
        }

        markers = _editor.$el.find(".fr-marker.fr-unprocessed").toArray();
      }

      _editor.html.cleanEmptyTags();
      _editor.selection.restore();
    }
  }

  // Insert token HTML
  function insertTokenHtml(tokenId, title) {
    if (!tokenId) return;

    // Restore selection before insertion
    editor.selection.restore();

    const tokenSpan = document.createElement("span");

    // Generate token <span> HTML
    tokenSpan.appendChild(document.createTextNode(`{${title || ""}}`));
    tokenSpan.setAttribute("class", "bt-block-token fr-deletable");
    tokenSpan.setAttribute("data-bt-block", "bt-token");
    tokenSpan.setAttribute(
      "data-token-id",
      tokenId.replace(/[^a-zA-Z0-9-]+/g, "")
    );
    tokenSpan.setAttribute("data-token", title || "");
    tokenSpan.setAttribute("contenteditable", "false");

    _editor.selection.save();

    _editor.html.insert(tokenSpan.outerHTML + " ");

    // Move the cursor after the <span>
    const node = _editor.selection.get();
    if (
      node &&
      node.anchorNode &&
      node.anchorNode.nodeType === Node.TEXT_NODE
    ) {
      _editor.selection.setAfter(node.anchorNode.parentNode);
    }
  }

  // Insert token
  function insert(Token, tokenId, title, attrs = {}) {
    if (
      _editor.events.trigger("token.beforeInsert", [tokenId, title, attrs]) ===
      false
    ) {
      return false;
    }

    if (!isTokenBlock(_editor.el)) {
      _editor.selection.restore();
      _editor.popups.hide("token.insert");
    } else {
      _editor.$el.focus();
    }

    if (!tokenId) {
      _editor.events.trigger("token.error", ["Invalid token ID"]);
      return false;
    }

    // Format title
    title = title || "";

    // Check if we have selection only in one token
    // const token = get();
    let token;
    if (Token) {
      if (typeof Token === 'string') {
        // Find token element by ID
        token = _editor.$el.find(`[data-token-id="${Token}"]`).get(0);
      } else {
        // Token is an element
        token = Token;
      }
    } else {
      token = get();
    }

    let $token;

    if (token) {
      $token = _editor.$(token);
      $token.attr("data-token-id", tokenId);

      // Change title if it is different
      if (title.length > 0 && $token.text() !== `{${title}}`) {
        let child = $token.get(0);
        while (
          child.childNodes.length === 1 &&
          child.childNodes[0].nodeType === Node.ELEMENT_NODE
        ) {
          child = child.childNodes[0];
        }
        _editor.$(child).html(`{${title}}`);
      }

      // Set attributes
      $token.attr(attrs);
      _editor.selection.restore();
    } else {
      // Remove current Tokens
      _editor.format.remove("span");

      // Nothing is selected
      if (_editor.selection.isCollapsed()) {
        insertTokenHtml(tokenId, title);
        _editor.selection.restore();
      } else {
        if (
          title.length > 0 &&
          title !== _editor.selection.text().replace(/\n/g, "")
        ) {
          _editor.selection.remove();
          insertTokenHtml(tokenId, title);
          _editor.selection.restore();
        } else {
          splitSelection();

          // Add token
          _editor.format.apply("span", {
            "data-bt-block": "bt-token",
            "data-token-id": tokenId,
            "data-token": title || "",
          });
        }
      }

      // Set attributes
      const links = allSelected();
      for (let i = 0; i < links.length; i++) {
        $token = _editor.$(links[i]);
        $token.attr(attrs);
        $token.removeAttr("_moz_dirty");
      }

      // Show token edit if only one token
      if (links.length === 1 && _editor.$wp) {
        _editor
          .$(links[0])
          .prepend(FroalaEditor.START_MARKER)
          .append(FroalaEditor.END_MARKER);
        _editor.selection.restore();
      }
    }

    // Hide popup and try to edit
    edit();

    // Trigger inserted event
    _editor.events.trigger("token.inserted", [tokenId, title]);
  }

  /**
   * Initialize plugin events
   */
  function _init() {
    // Edit on keyup
    _editor.events.on("keyup", (e) => {
      if (e.which !== FroalaEditor.KEYCODE.ESC) {
        edit(e);
      }
    });

    _editor.events.on("window.mouseup", (e) => {
      edit(e);
    });

    if (_editor.helpers.isMobile()) {
      _editor.events.$on(_editor.$doc, "selectionchange", (e) => {
        edit(e);
      });
    }

    // Hit ESC when focus is in token edit popup
    _editor.events.on(
      "toolbar.esc",
      () => {
        if (_editor.popups.isVisible("token.edit")) {
          _editor.events.disableBlur();
          _editor.events.focus();
          return false;
        }
      },
      true
    );
  }

  /**
   * Handle insert token button click
   */

  function insertToken (token, editor, val, text){
    editor.token.insert(token, val, text);
  }

  /**
   * Public plugin methods
   */
  return {
    _init: _init,
    get: get,
    edit: edit,
    remove: remove,
    insert: insert,
    insertToken: insertToken,
  };
};

/**
 * Register as Froala Plugin
 */
FroalaEditor.PLUGINS.token = token;

/**
 * Define token plugin default options
 */
FroalaEditor.DEFAULTS.tokenEditButtons = ["tokenEdit", "tokenRemove"];
FroalaEditor.DEFAULTS.tokenInsertButtons = [];
FroalaEditor.DEFAULTS.tokenOptions = {
  predefinedTokens: [],
};

/**
 * Define icon for "insertToken" command
 */
FroalaEditor.DefineIcon("insertToken", {
  NAME: "tag",
  SVG_KEY: "tag",
});

/**
 * Register "insertToken" command
 */
FroalaEditor.RegisterCommand("insertToken", {
  title: "Insert Token",
  undo: true,
  focus: true,
  refreshOnCallback: false,
  popup: true,
  callback: function () {
    const emptyToken = '';
    this.selection.save();
    this.events.trigger("token.showPopup", [emptyToken, "insert"]);
  },
});

/**
 * Register "tokenInsert" command for insert button in popup
 */
FroalaEditor.RegisterCommand("tokenInsert", {
  focus: false,
  refreshAfterCallback: false,
  callback: function () {
    this.token.insert(
      this.popups.get("token.insert").find(".fr-token-selector").val(),
      this.popups.get("token.insert").find(".fr-token-selector")[0].selectedOptions[0].innerText
    );
    this.popups.hide("token.insert");
  },
  refresh: function ($btn) {
    const token = this.token.get();
    if (token) {
      $btn.text(this.language.translate("Update"));
    } else {
      $btn.text(this.language.translate("Insert"));
    }
  },
});

/**
 * Define icon for "tokenEdit" command
 */
FroalaEditor.DefineIconTemplate('tokenEdit_tpl', '<i style="color: #626363;" class="icon icon-16 icon-edit"></i>');
FroalaEditor.DefineIcon('tokenEdit', { template: 'tokenEdit_tpl' });

/**
 * Register "tokenEdit" command
 */
FroalaEditor.RegisterCommand("tokenEdit", {
  title: "Edit Token",
  undo: false,
  refreshAfterCallback: false,
  popup: true,
  callback: function () {
    const token = this.token.get();
    this.events.trigger("token.showPopup", [token.dataset.token, 'edit']);
    this.popups.hide("token.edit");
  },
  refresh: function ($btn) {
    const token = this.token.get();
    if (token) {
      $btn.removeClass("fr-hidden");
    } else {
      $btn.addClass("fr-hidden");
    }
  },
});

/**
 * Define icon for "tokenRemove" command
 */
FroalaEditor.DefineIconTemplate('tokenRemove_tpl', '<i style="color: #626363;" class="icon icon-16 icon-unlink"></i>');
FroalaEditor.DefineIcon('tokenRemove', { template: 'tokenRemove_tpl' });

/**
 * Register "tokenRemove" command
 */
FroalaEditor.RegisterCommand("tokenRemove", {
  title: "Remove",
  callback: function () {
    this.token.remove();
  },
  refresh: function ($btn) {
    const token = this.token.get();
    if (token) {
      $btn.removeClass("fr-hidden");
    } else {
      $btn.addClass("fr-hidden");
    }
  },
});

export default token;
