/**
 * Token Plugin Styles for Froala Editor
 */

/* Token appearance */
span[data-bt-block="bt-token"] {
    display: inline-block;
    background-color: #f1f8ff;
    border: 1px solid #c8e1ff;
    border-radius: 3px;
    padding: 2px 6px;
    color: #0366d6;
    margin: 0 2px;
    cursor: default;
    font-size: 0.95em;
    line-height: 1.4;
    white-space: nowrap;
    transition: background-color 0.2s, border-color 0.2s;
  }
  
  span[data-bt-block="bt-token"]:hover {
    background-color: #e1efff;
    border-color: #9ecbff;
  }
  
  /* Token selector in popup */
  .fr-token-selector {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 2px;
    font-size: 14px;
    margin-bottom: 10px;
  }
  
  .fr-token-insert-layer {
    width: 260px;
    padding: 15px;
  }
  
  .fr-token-insert-layer .fr-input-line {
    margin-bottom: 10px;
  }
  
  .fr-token-insert-layer label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #555;
  }
  