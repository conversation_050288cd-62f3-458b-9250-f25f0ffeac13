declare const kbArticle: {
  _init: () => void;
  get: () => HTMLElement | null;
  allSelected: () => boolean;
  remove: () => void;
  insert: (
    kbId: string, 
    kbUrl: string, 
    title: string, 
    subTitle?: string, 
    teams?: any[], 
    orgs?: any[], 
    visibility?: string, 
    updated?: string, 
    attrs?: Record<string, any>,
    editor?: any
  ) => boolean | void;
  showInsertPopup: () => void;
  insertKbArticle: () => void;
};

export default kbArticle;