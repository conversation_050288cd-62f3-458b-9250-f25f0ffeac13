.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  height: 23px;
  margin-top: 9px;
  margin-left: 5px;
  width: 50px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 15px;
  width: 15px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.tree-root,
.child-list {
list-style: none;
padding-left: 5px;
}

.child-list{
margin-left: 20px;
}

.tree-node {
padding-left: 16px; /* Adjust this value as needed */
}

div.bt-block-article,
span.bt-block-article {
border: solid 1px #dedede;
border-radius: 3px;
margin: 4px;
min-height: 45px;
}

div.bt-block-article.bt-block-link-only,
span.bt-block-article.bt-block-link-only {
border: none;
margin: 0;
display: inline;
}

div.bt-block-article.bt-block-link-only > .bt-block-title,
span.bt-block-article.bt-block-link-only > .bt-block-title {
padding: 0;
}

div.bt-block-article.bt-block-link-only.bt-block-substituted,
span.bt-block-article.bt-block-link-only.bt-block-substituted {
border: none;
border-radius: unset;
}

div.bt-block-article > .bt-block-title {
padding: 8px;
display: flex;
align-items: center;
}

div.bt-block-article > .bt-block-title > span,
span.bt-block-article > .bt-block-title > span {
font-weight: bold;
}

.bt-title-link{
  color: #007bff;
  font-weight: 600;
}

div.bt-block-article > .bt-block-title > span.bt-title-link,
span.bt-block-article > .bt-block-title > span.bt-title-link {
color: #007bff; /* Replace with actual value of $bt-color-hyperlink-static */
font-weight: 600;
}

div.bt-block-article > .bt-block-title > span.bt-title-link:hover,
span.bt-block-article > .bt-block-title > span.bt-title-link:hover {
text-decoration: underline;
}

div.bt-block-article > .bt-block-title > span.spacer,
span.bt-block-article > .bt-block-title > span.spacer {
flex: 1;
}

div.bt-block-article > .bt-block-inner,
span.bt-block-article > .bt-block-inner {
display: inline-block;
padding: 0 12px 12px 12px;
}

div.bt-block-article > .bt-block-inner > .summary,
span.bt-block-article > .bt-block-inner > .summary {
display: block;
}

div.bt-block-article > .bt-block-inner .bt-block-section-title,
span.bt-block-article > .bt-block-inner .bt-block-section-title {
font-weight: bold;
}

/* tags/chips */
div.bt-block-article span.chips span.chip,
div.bt-block-article .summary span.chip,
span.bt-block-article span.chips span.chip,
span.bt-block-article .summary span.chip {
display: inline-block;
background-color: #ddf5f5;
padding: 2px 4px;
border-radius: 4px;
white-space: nowrap;
margin: 0 2px;
}

div.bt-block-article span.chips span.chip.chip-light-red,
div.bt-block-article .summary span.chip.chip-light-red,
span.bt-block-article span.chips span.chip.chip-light-red,
span.bt-block-article .summary span.chip.chip-light-red {
background-color: #f7af86;
}

div.bt-block-article span.chips span.chip.chip-light-yellow,
div.bt-block-article .summary span.chip.chip-light-yellow,
span.bt-block-article span.chips span.chip.chip-light-yellow,
span.bt-block-article .summary span.chip.chip-light-yellow {
background-color: #fce8b2;
}

div.bt-block-article span.chips span.chip.chip-light-blue,
div.bt-block-article .summary span.chip.chip-light-blue,
span.bt-block-article span.chips span.chip.chip-light-blue,
span.bt-block-article .summary span.chip.chip-light-blue {
background-color: #ddf5f5;
}

div.bt-block-article span.chips span.chip.chip-light-purple,
div.bt-block-article .summary span.chip.chip-light-purple,
span.bt-block-article span.chips span.chip.chip-light-purple,
span.bt-block-article .summary span.chip.chip-light-purple {
background-color: #e9e5fb;
}

div.bt-block-article span.chips span.chip.chip-light-green,
div.bt-block-article .summary span.chip.chip-light-green,
span.bt-block-article span.chips span.chip.chip-light-green,
span.bt-block-article .summary span.chip.chip-light-green {
background-color: #b7eacc;
}

div.bt-block-article span.chips span.chip.chip-transparent,
div.bt-block-article .summary span.chip.chip-transparent,
span.bt-block-article span.chips span.chip.chip-transparent,
span.bt-block-article .summary span.chip.chip-transparent {
background-color: unset !important;
}

.open-article-btn {
  text-decoration: unset;
  color: unset;
}

.control {
  cursor: pointer;
}

.collapsable > .head > .control {
  cursor: pointer;
}
.collapsable > .head .control:before {
  content: "\e930";
}
.collapsable.collapsed > .head .control:before {
  content: "\e933";
}
.collapsable.collapsed > .content {
  display: none;
}

.center {
display: block;
margin-left: auto;
margin-right: auto;
padding-top: 50px;
padding-bottom: 50px;
}