/**
 * Div Utilities Plugin for Froala Editor
 * Provides utilities for managing divs like tabs and accordions
 * No Ext dependency
 */
import FroalaEditor from "froala-editor";

const divutils = function (editor) {
  // Initialize the plugin
  function _init() {
    // Add popup template for deletion confirmation
    FroalaEditor.POPUP_TEMPLATES["tabs.deletion"] = "[_BUTTONS_]";

    // Setup click handler for deleting divs
    editor.events.on("click", function (e) {
      const clickedEl = editor.selection.get().anchorNode;

      if (!clickedEl) {
        return;
      }

      const targetElement = clickedEl.parentElement;
      const getDiv = targetElement.closest(
        "div.tabs-panel, div.accordion-panel"
      );
      const $popup = editor.popups.get("tabs.deletion");

      // Toggle deletion popup state
      editor.deletePopupShown = !editor.deletePopupShown;

      if (getDiv) {
        const left = e.originalEvent.clientX + 30;
        const top = e.originalEvent.clientY + 30;

        $popup.css({
          top: `${top}px`,
          left: `${left}px`,
          height: "55px",
          width: "45px",
          padding: "5px",
          transform: "translate(-50%, -50%)",
        });

        editor.selection.save();

        if (!editor.deletePopupShown) {
          editor.popups.hide("tabs.deletion");
        } else {
          editor.popups.show("tabs.deletion");
        }

        editor.selection.restore();
      }
    });

    // Register the remove command if not already defined
    if (!FroalaEditor.COMMANDS.removeDiv) {
      // Define the icon if not already defined
      if (!FroalaEditor.ICONS.removeDiv) {
        FroalaEditor.DefineIcon("removeDiv", {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('editor-paragraph-more-16'),
        });
      }

      FroalaEditor.RegisterCommand("removeDiv", {
        title: "Delete",
        icon: "removeDiv",
        callback: function () {
          const targetElement = this.selection.get().anchorNode.parentElement;
          const getDiv = targetElement.closest(
            "div.tabs-panel, div.accordion-panel"
          );

          if (getDiv) {
            this.undo.saveStep();
            getDiv.remove();
          }

          this.popups.hide("tabs.deletion");
        },
      });
    }

    // Create deletion popup if it doesn't exist yet
    if (!editor.popups.get("tabs.deletion")) {
      editor.popups.create("tabs.deletion", {
        buttons:
          '<div class="fr-buttons"><button class="fr-command" data-cmd="removeDiv" title="Delete" type="button">&#xe020;</button></div>',
      });
    }
  }

  // Return public API
  return {
    _init: _init,
  };
};

export default divutils;
