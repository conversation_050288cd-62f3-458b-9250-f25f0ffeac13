import FroalaEditor from "froala-editor";
import { getHttpClient } from '../../../../services/httpClientProvider';
import { useAuthStore } from '../../../../stores/auth';
import { useKnowledgeAPI } from '../../../../composables/services/useKnowledgeAPI';

const fileManager = function (editor) {
    // Private variables
    let _editor = editor;
    
    // Initialize auth store and get HTTP client
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);
    // Initialize knowledge API
    const knowledgeAPI = useKnowledgeAPI();

    function insertFile(file, editor){
        // if (!selectionRestored) {
        //     editor.selection.restore();
        //     selectionRestored = true;
        // }
        let selectionRestored = false;

        editor.selection.save();

        if (!selectionRestored) {
            editor.selection.restore();
            selectionRestored = true;
        }

        if (file.type.includes("image")) {
            editor.image.insert(file.file, true, {alt: file.name}, null, {
                link: file.file,
                file_name: file.name,
                file_tag: file.tag,
            });
        }
        else if (file.type.includes("video")) {
            editor.video.insert('<video controls crossorigin="use-credentials" src="' + file.file + '"></video>');
        }
        else {
            editor.link.insert(file.file, file.name);
        }
    }

    return {
        insertFile: insertFile,
      };
};

FroalaEditor.RegisterCommand('manageFiles', {
    title: 'File Manager',
    undo: false,
    focus: true,
    refreshOnCallback: false,
    callback: function() {
        this.events.trigger("openfileManager");
    },
});

export default fileManager;