<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoToggleButton from '@services/ui-component-library/components/BravoToggleButton.vue';
import { VAceEditor } from 'vue3-ace-editor';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-python';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/mode-css';
import 'ace-builds/src-noconflict/mode-java';
import 'ace-builds/src-noconflict/mode-c_cpp';
import 'ace-builds/src-noconflict/mode-sql';
import 'ace-builds/src-noconflict/mode-typescript';
import 'ace-builds/src-noconflict/theme-github';
import 'ace-builds/src-noconflict/theme-monokai';

interface ICodeSnippetData {
  id?: string;
  language: string;
  code: string;
  theme: string;
}

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'insert', data: ICodeSnippetData): void;
  (e: 'update', data: ICodeSnippetData): void;
}>();

const isVisible = ref(props.visible);
const language = ref('javascript');
const code = ref('');
const isDarkTheme = ref(false);
const isEditMode = ref(false);
const editId = ref('');
const editorReady = ref(false);
const aceEditor = ref<any>(null);

const languages = [
  { label: 'JavaScript', value: 'javascript' },
  { label: 'Python', value: 'python' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'Java', value: 'java' },
  { label: 'C++', value: 'cpp' },
  { label: 'SQL', value: 'sql' },
  { label: 'TypeScript', value: 'typescript' }
];

watch(() => props.visible, (newValue) => {
  isVisible.value = newValue;
});

watch(() => isVisible.value, (newValue) => {
  emit('update:visible', newValue);
  if (!newValue) {
    resetForm();
  }
  setTimeout(() => {
  if (newValue && editorReady.value && aceEditor.value) {
      aceEditor.value?.focus();
    }
  }, 500);
});

const resetForm = () => {
  language.value = 'javascript';
  code.value = '';
  isDarkTheme.value = false;
  isEditMode.value = false;
  editId.value = '';
};

const onHide = () => {
  isVisible.value = false;
};

const onInsert = () => {
  if (!editorReady.value) {
    console.warn('Editor not ready yet');
    return;
  }

  const currentCode = code.value;
  if (!currentCode || !currentCode.trim()) {
    console.warn('No code content available');
    return;
  }

  const data: ICodeSnippetData = {
    language: language.value,
    code: currentCode,
    theme: isDarkTheme.value ? 'dark' : 'light'
  };

  if (isEditMode.value && editId.value) {
    data.id = editId.value;
    emit('update', data);
  } else {
    emit('insert', data);
  }

  onHide();
};

// Method to be called from parent when editing
const editCode = (data: ICodeSnippetData) => {
  isEditMode.value = true;
  editId.value = data.id || '';
  language.value = data.language;
  code.value = data.code;
  isDarkTheme.value = data.theme === 'dark';
  isVisible.value = true;
};

// Expose the editCode method to the parent
defineExpose({ editCode });
</script>

<template>
  <BravoDialog
    v-model:visible="isVisible"
    :header="isEditMode ? 'Update Code Snippet' : 'Insert Code Snippet'"
    :modal="true"
    :style="{ width: '800px', height: '80vh' }"
    :closable="true"
    :closeAriaLabel="'Close dialog'"
    :draggable="true"
    :resizable="true"
    :maximizable="true"
    :minimizable="false"
    :position="'center'"
    :autoFocus="false"
    @hide="onHide"
    class="code-snippet-dialog"
  >
    <div class="code-snippet-form">
      <div class="toolbar">
        <BravoSelect
          id="language-select"
          v-model="language"
          :options="languages"
          placeholder="Select language"
          class="language-select"
          dataTestId="language-select"
        />
        <div class="theme-toggle">
          <span>Theme</span>
          <BravoToggleButton
            v-model="isDarkTheme"
            onIcon="pi pi-moon"
            offIcon="pi pi-sun"
            onLabel="Dark"
            offLabel="Light"
          />
        </div>
      </div>

      <div class="code-editor">
        <VAceEditor
          ref="aceEditor"
          :value="code"
          @update:value="code = $event"
          :lang="language"
          :theme="isDarkTheme ? 'monokai' : 'github'"
          :options="{
            fontSize: '13px',
            showPrintMargin: false,
            showGutter: true,
            highlightActiveLine: true,
            tabSize: 2,
            useSoftTabs: true,
            wrap: true
          }"
          @init="editorReady = true"
          style="height: 100%; width: 100%;"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <BravoButton
          label="Close"
          severity="secondary"
          @click="onHide"
        />
        <BravoButton
          :label="isEditMode ? 'Update' : 'Insert'"
          @click="onInsert"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
.code-snippet-dialog .p-dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 0;
}

.code-snippet-form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.5rem;
  background-color: var(--surface-ground);
  border-bottom: 1px solid var(--surface-border);
  position: sticky;
  top: 0;
  z-index: 1;
  flex-shrink: 0;
}

.language-select {
  flex: 1;
}

.theme-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.code-editor {
  flex: 1;
  min-height: 0;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--surface-border);
  border-radius: 4px;
  margin: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: var(--surface-ground);
  border-top: 1px solid var(--surface-border);
  flex-shrink: 0;
}
</style>
