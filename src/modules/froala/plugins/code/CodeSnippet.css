.code-snippet-dialog .p-dialog-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    padding: 0;
  }
.code-snippet-block {
    margin: 1rem 0;
    border: 1px solid var(--surface-border);
    border-radius: 4px;
    overflow: hidden;
}

.code-snippet-block.light {
    background-color: var(--surface-ground);
    color: var(--text-color);
}

.code-snippet-block.dark {
    background-color: var(--surface-900);
    color: var(--surface-0);
}

.code-snippet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--surface-section);
    border-bottom: 1px solid var(--surface-border);
}

.code-snippet-language-name {
    font-weight: 500;
    font-size: 0.875rem;
}

.code-snippet-actions {
    display: flex;
    gap: 0.5rem;
}

.code-snippet-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    color: var(--text-color-secondary);
    transition: all 0.2s ease;
}

.code-snippet-actions button:hover {
    background-color: var(--surface-hover);
    color: var(--text-color);
}

.code-snippet-block pre {
    margin: 0;
    padding: 1rem;
    overflow-x: auto;
}

.code-snippet-block code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Theme-specific code styles */
.code-snippet-block.light pre {
    background-color: var(--surface-ground);
}


.code-snippet-block {
    min-height: 45px;
    position: relative;
    margin: 1em 0;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .code-snippet-block .code-snippet-header {
    display: flex;
    padding: 7px 10px;
    align-items: center;
  }
  .code-snippet-block .code-snippet-header .code-snippet-language-name {
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
    flex: 1;
    font-family: "Inter", sans-serif;
  }
  .code-snippet-block .code-snippet-header ul {
    display: flex;
    padding: 0;
    margin: 0;
    gap: 20px;
    align-items: center;
  }
  .code-snippet-block .code-snippet-header li {
    list-style: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
  }
  .code-snippet-block .code-snippet-header .copy-snippet-btn {
    font-size: 25px;
    height: 27px;
    display: flex;
    align-items: center;
  }
  .code-snippet-block code[class*=language-], .code-snippet-block pre[class*=language-] {
    word-wrap: break-word;
    overflow: hidden;
    width: 100%;
    display: block;
    white-space: pre-wrap !important;
    word-break: break-word;
  }
  .code-snippet-block code[class*=language-], .code-snippet-block pre[class*=language-] {
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace !important;
  }
  .code-snippet-block.light {
    border: 1px solid #dedede;
    background: #f5f5f5;
    color: #000;
  }
  .code-snippet-block.light .code-snippet-header {
    background: #dedede;
  }
  .code-snippet-block.light code[class*=language-], .code-snippet-block.light pre[class*=language-] {
    color: #444;
  }
  .code-snippet-block.dark {
    border: 1px solid #000;
    background: #272822;
    color: #fff;
  }
  .code-snippet-block.dark .code-snippet-header {
    background: #1e1f1c;
  }
  .code-snippet-block.dark pre {
    background: #272822 !important;
  }
  .code-snippet-block.dark code[class*=language-], .code-snippet-block.dark pre[class*=language-] {
    color: #fff;
    text-shadow: none;
  }
  .code-snippet-block.dark .code-snippet-language-name, .code-snippet-block.dark .copy-snippet-btn {
    color: #fff;
  }
  .code-snippet-block .token.boolean, .code-snippet-block .token.number {
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow: hidden;
    word-break: break-all;
  }
  .code-snippet-block .language-css .token.string, .code-snippet-block .style .token.string, .code-snippet-block .token.entity, .code-snippet-block .token.operator, .code-snippet-block .token.url {
    background: none;
  }

  .article-content .code-snippet-block .pi-pencil {
    display: none;
  }

  .article-content .code-snippet-block .pi-trash {
    display: none;
  }
