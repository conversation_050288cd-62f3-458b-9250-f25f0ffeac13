import FroalaEditor from 'froala-editor';
import { createApp, ref } from 'vue';
import CodeSnippetDialog from './CodeSnippetDialog.vue';
import PrimeVue from 'primevue/config';
import './CodeSnippet.css';

const codeEditor = function (editor: FroalaEditor) {
    let app: any = null;
    let dialogContainer: HTMLElement | null = null;
    let dialogInstance: any = null;
    const isDialogVisible = ref(false);
    let cursorPosition: any = null;

    async function _init() {
        try {
            // Create dialog container if it doesn't exist
            if (!dialogContainer) {
                dialogContainer = document.createElement('div');
                dialogContainer.id = 'code-snippet-dialog-container';
                document.body.appendChild(dialogContainer);
            }

            // Create Vue app for dialog if it doesn't exist
            if (!app) {
                app = createApp(CodeSnippetDialog, {
                    visible: isDialogVisible,
                    onInsert: (data: { language: string; code: string; theme: string }) => {
                        const uniqueId = 'code-' + Date.now();
                        const html = `
                            <div class="code-snippet-block ${data.theme}" id="${uniqueId}" contenteditable="false">
                                <div class="code-snippet-header">
                                    <span class="code-snippet-language-name">${data.language}</span>
                                    <ul class="code-snippet-actions">
                                        <li role="button" id="copyCode_${uniqueId}" class="copy-snippet-btn" title="Copy to clipboard">&#x2398;</li>
                                        <li role="button" id="editCode_${uniqueId}" class="edit-snippet-btn pi pi-pencil" title="Edit"></li>
                                        <li role="button" id="deleteCode_${uniqueId}" class="delete-snippet-btn pi pi-trash" title="Delete"></li>
                                    </ul>
                                </div>
                                <pre><code class="language-${data.language.toLowerCase()}">${escapeHtml(data.code)}</code></pre>
                            </div>
                            <br/>
                        `;

                        if (cursorPosition) {
                            editor.selection.restore();
                        }
                        editor.html.insert(html);
                        editor.events.trigger('contentChanged', [], true);
                        isDialogVisible.value = false;

                        setTimeout(() => {
                            const codeElement = document.getElementById(uniqueId)?.querySelector('code');
                            if (codeElement && (window as any).Prism) {
                                codeElement.innerHTML = codeElement.innerHTML.replace(/[<]br[/]?[>]/gi, '\n');
                                (window as any).Prism.highlightElement(codeElement);
                            }
                        }, 0);
                    },
                    onUpdate: (data: { id: string; language: string; code: string; theme: string }) => {
                        const codeBlock = document.getElementById(data.id);
                        if (codeBlock) {
                            const headerSpan = codeBlock.querySelector('.code-snippet-language-name');
                            const codeElement = codeBlock.querySelector('code');
                            if (headerSpan && codeElement) {
                                headerSpan.textContent = data.language;
                                codeElement.className = `language-${data.language.toLowerCase()}`;
                                codeElement.textContent = data.code;
                                // Highlight the updated code block
                                if ((window as any).Prism) {
                                    codeElement.innerHTML = codeElement.innerHTML.replace(/[<]br[/]?[>]/gi, '\n');
                                    (window as any).Prism.highlightElement(codeElement);
                                }

                                // Update theme
                                codeBlock.className = `code-snippet-block ${data.theme}`;
                            }
                        }
                        isDialogVisible.value = false;
                    }
                });

                // Initialize PrimeVue with default configuration
                app.use(PrimeVue, {
                    ripple: true,
                    locale: {
                        aria: {
                            close: 'Close'
                        }
                    }
                });

                // Mount the app and store reference
                dialogInstance = app.mount(dialogContainer);

                // Setup event handlers for code block actions
                editor.events.on('click', handleCodeBlockActions);
            }
        } catch (error) {
            console.error('Error initializing code snippet dialog:', error);
        }
    }

    async function showInsertPopup() {
        try {
            if (!app) {
                await _init();
            }
            cursorPosition = editor.selection.save();
            isDialogVisible.value = true;
        } catch (error) {
            console.error('Error showing code snippet dialog:', error);
        }
    }

    function handleCodeBlockActions(e: MouseEvent) {
        const target = e.target as HTMLElement;
        if (!target) return;

        const button = target.closest('li');
        if (!button) return;

        const [action, id] = (button.id || '').split('_');
        if (!action || !id) return;

        switch (action) {
            case 'copyCode':
                handleCopyCode(id);
                break;
            case 'editCode':
                handleEditCode(id);
                break;
            case 'deleteCode':
                handleDeleteCode(id);
                break;
        }
    }

    function handleCopyCode(id: string) {
        const codeBlock = document.getElementById(id);
        if (codeBlock) {
            const codeElement = codeBlock.querySelector('code');
            if (codeElement) {
                navigator.clipboard.writeText(codeElement.innerText)
                    .catch(err => console.error('Failed to copy code:', err));
            }
        }
    }

    function handleEditCode(id: string) {
        const codeBlock = document.getElementById(id);
        if (!codeBlock) return;

        const codeElement = codeBlock.querySelector('code');
        const languageElement = codeBlock.querySelector('.code-snippet-language-name');
        
        if (codeElement && languageElement && dialogInstance) {
            dialogInstance.editCode({
                id,
                code: codeElement.innerText,
                language: languageElement.textContent || '',
                theme: codeBlock.classList.contains('dark') ? 'dark' : 'light'
            });
        }
    }

    function handleDeleteCode(id: string) {
        const codeBlock = document.getElementById(id);
        if (codeBlock) {
            codeBlock.remove();
            editor.events.trigger('contentChanged', [], true);
        }
    }

    function _destroy() {
        try {
            if (app) {
                // Remove click event listener
                editor.events.on('click', handleCodeBlockActions);
                app.unmount();
                app = null;
            }
            if (dialogContainer) {
                dialogContainer.remove();
                dialogContainer = null;
            }
            dialogInstance = null;
            cursorPosition = null;
        } catch (error) {
            console.error('Error destroying code snippet dialog:', error);
        }
    }

    // Register destroy event
    editor.events.on('destroy', _destroy);

    return {
        _init: _init,
        showInsertPopup: showInsertPopup,
        _destroy: _destroy
    };
};

function escapeHtml(text: string): string {
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

// Register Froala command
FroalaEditor.DefineIcon('insertCodeBtn', { NAME: 'code', SVG_KEY: 'codeView' });
FroalaEditor.RegisterCommand('insertCodeBtn', {
    title: 'Insert Code Snippet',
    icon: 'insertCodeBtn',
    focus: false,
    undo: true,
    refreshAfterCallback: true,
    callback: function() {
        this.codeEditor.showInsertPopup();
    }
});

export { codeEditor };
