/* Tabs */
.fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel div.tabs p:not(.tab-title) {
    list-style-type: none;
  }
  .fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }
  .custom-theme.fr-popup .fr-colors-tabs {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab {
    color: #222222;
    padding: 8px 0;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:hover,
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:focus {
    color: #1e88e5;
  }
  .custom-theme.fr-popup
    .fr-colors-tabs
    .fr-colors-tab[data-param1="background"]::after {
    bottom: 0;
    left: 0;
    background: #1e88e5;
    -webkit-transition: transform none;
    -moz-transition: transform none;
    -ms-transition: transform none;
    -o-transition: transform none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab.fr-selected-tab {
    color: #1e88e5;
  }
  .fr-element.fr-view .tabs-panel .tabs .tab-title {
    list-style-type: circle;
    color: #0067c7;
    margin-bottom: unset;
    padding-bottom: 10px;
    padding-top: 10px;
  }
  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }

  .removeBtn{
    border: none;
    background: white;
    padding: 9px 4px 4px 4px;
    border-radius: 3px;
  }
  