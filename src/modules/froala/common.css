.fr-view .callout-container,
.fr-view .alert-info,
.fr-view .alert-warning,
.fr-view .alert-danger,
.fr-view .alert-success {
    margin: 15px 0px;
    padding: 10px 10px 10px 50px;
    border: 1px solid #efefef;
    border-radius: 6px;
    font-size: 14px;
    line-height: 21px;
    position: relative;
}

.fr-view .callout-container i,
.fr-view .alert-info i,
.fr-view .alert-warning i,
.fr-view .alert-danger i,
.fr-view .alert-success i {
    font-size: 24px;
    position: absolute;
    top: 9px;
    left: 9px;
}

.fr-view .callout-container.warning i,
.fr-view .alert-danger i {
    left: 13px;
}

.fr-view .callout-container.notes,
.fr-view .alert-info {
    background-color: #fcfcfc;
    border-color: #aab8c6;
}

.fr-view .callout-container.warning,
.fr-view .alert-danger {
    background-color: #fff8f7;
    border-color: #d04437;
}

.fr-view .callout-container.attention,
.fr-view .alert-warning {
    background: #fffdf6;
    border-color: #ffeaae;
}

.fr-view .callout-container.success,
.fr-view .alert-success {
    background: #dff0d8;
    border-color: #468847;
}

.fr-view #borderemphasis,
.fr-view .borderemphasis {
    border: 2px solid rgba(48, 48, 48, 0.5);
    border-radius: 16px;
}

.fr-view .alert-info::before,
.fr-view .alert-warning::before,
.fr-view .alert-danger::before,
.fr-view .alert-success::before {
    display: block;
    font-family: "Font Awesome 5 Pro";
    font-size: 20px;
    font-style: normal;
    font-weight: 300;
    position: absolute;
    left: 10px;
    padding: 0 10px;
}

.fr-view .alert-info::before {
    content: "\f05a";
}

.fr-view .alert-warning::before {
    content: "\f071";
}

.fr-view .alert-danger::before {
    content: "\f00d";
}

.fr-view .alert-success::before {
    content: "\f058";
}

.fr-popup .fr-submit.fr-btn {
    float: none !important;
}