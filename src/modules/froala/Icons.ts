import FroalaEditor from 'froala-editor';
import { Glyphs } from './Glyphs';


FroalaEditor.DefineIconTemplate('relay_glyphs', '<i style="color: #626363;" class="icon icon-16 [NAME]"></i>');
FroalaEditor.DefineIconTemplate('teamviewer_tpl', '<i class="svg-glyph team-viewer-dark team-viewer-html-btn"></i>');

// Update "More ..." Icons
FroalaEditor.DefineIcon('moreText', {
    template: 'relay_glyphs',
    //NAME: Glyphs.getIconName(this.getUseEmailCommsMenu() ? 'editor-text-16' : 'editor-text-more-16'),
    NAME: Glyphs.getIconName(false ? 'editor-text-16' : 'editor-text-more-16'), // TODO
});
FroalaEditor.DefineIcon('moreParagraph', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-paragraph-more-16'),
});
FroalaEditor.DefineIcon('moreRich', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-layout-16'),
});
FroalaEditor.DefineIcon('moreMisc', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-more-16'),
});

// Fix buttons for "track changes" menu
FroalaEditor.DefineIcon('showChanges', {
    template: 'svg',
    PATH: FroalaEditor.SVG.showTrackChanges,
});
FroalaEditor.DefineIcon('applyAll', {
    template: 'svg',
    PATH: FroalaEditor.SVG.acceptAllChanges,
});
FroalaEditor.DefineIcon('removeAll', {
    template: 'svg',
    PATH: FroalaEditor.SVG.rejectAllChanges,
});
FroalaEditor.DefineIcon('applyLast', {
    template: 'svg',
    PATH: FroalaEditor.SVG.acceptSingleChange,
});
FroalaEditor.DefineIcon('removeLast', {
    template: 'svg',
    PATH: FroalaEditor.SVG.rejectSingleChange,
});
FroalaEditor.DefineIcon('removeDiv', {
    NAME: 'external-link',
    FA5NAME: 'chevron-right',
});

FroalaEditor.DefineIcon('imageDisplay', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('group'),
});
FroalaEditor.DefineIcon('kbRemove', {NAME: 'unlink'});
FroalaEditor.DefineIcon('kbEdit', {
    NAME: 'edit',
    FA5NAME: 'pencil',
});
FroalaEditor.DefineIcon('kbOpen', {
    NAME: 'external-link',
    FA5NAME: 'chevron-right',
});
FroalaEditor.DefineIcon('tokenRemove', {NAME: 'unlink'});

FroalaEditor.DefineIcon('tokenEdit', {NAME: 'edit'});
FroalaEditor.DefineIcon('tokenOpen', {
    NAME: 'external-link',
    FA5NAME: 'chevron-right',
});
FroalaEditor.DefineIcon('inlineImgAlign', {NAME: 'paint-brush'});

FroalaEditor.DefineIcon('teamviewer', {template: 'teamviewer_tpl'});
FroalaEditor.DefineIcon('figureCaption', {
    NAME: 'commenting',
    FA5NAME: 'comment-alt',
    // template: 'svg',
    // PATH: FroalaEditor.SVG.imageCaption,
});

FroalaEditor.DefineIcon('figureAlign', {
    NAME: 'align-justify',
    // template: 'svg',
    // PATH: 'alignJustify'
});
FroalaEditor.DefineIcon('compress', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('compress'),
})
FroalaEditor.DefineIcon('panel-divs', {
    NAME: 'window',
});
FroalaEditor.DefineIcon('align-left', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-align-left-16'),
});
FroalaEditor.DefineIcon('align-right', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-align-right-16'),
});
FroalaEditor.DefineIcon('align-center', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-align-center-16'),
});
FroalaEditor.DefineIcon('align-justify', {
    template: 'relay_glyphs',
    NAME: Glyphs.getIconName('editor-align-justify-16'),
});
// FroalaEditor.DefineIcon(b.name, {
//     template: 'relay_glyphs',
//     NAME: Glyphs.getIconName(b.icon),
// });
// FroalaEditor.DefineIcon(b.name, {
//     template: 'svg',
//     PATH: FroalaEditor.SVG[b.name],
// });
FroalaEditor.DefineIcon('showChanges', {
    template: 'svg',
    PATH: FroalaEditor.SVG.showTrackChanges,
});
FroalaEditor.DefineIcon('applyAll', {
    template: 'svg',
    PATH: FroalaEditor.SVG.acceptAllChanges,
});
FroalaEditor.DefineIcon('removeAll', {
    template: 'svg',
    PATH: FroalaEditor.SVG.rejectAllChanges,
});
FroalaEditor.DefineIcon('applyLast', {
    template: 'svg',
    PATH: FroalaEditor.SVG.acceptSingleChange,
});
FroalaEditor.DefineIcon('removeLast', {
    template: 'svg',
    PATH: FroalaEditor.SVG.rejectSingleChange,
});
FroalaEditor.DefineIcon('removeDiv', {
    NAME: 'external-link',
    FA5NAME: 'chevron-right',
});