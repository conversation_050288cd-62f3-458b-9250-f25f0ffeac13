import FroalaEditor from 'froala-editor';

// Create a global editor instance with port
export const globalEditor = {
    instance: null as FroalaEditor | null,
    port: null as MessagePort | null,
    setInstance: (editor: FroalaEditor) => {
        globalEditor.instance = editor;
    },
    getInstance: () => {
        return globalEditor.instance;
    },
    setPort: (port: MessagePort) => {
        globalEditor.port = port;
    },
    getPort: () => {
        return globalEditor.port;
    }
};
