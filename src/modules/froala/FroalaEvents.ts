import FroalaEditor from 'froala-editor';

// Events written here are for improving froala wrapper, do not include events that are used for app
export function setupFroalaEvents(config: any) {
    config.events = config.events || {};

    config.events['buttons.refresh'] = function () {
        // const editor = this;
        const buttons = this.$tb.find(
            '.fr-command[data-cmd="bold"], .fr-command[data-cmd="italic"], ' +
            '.fr-command[data-cmd="underline"], .fr-command[data-cmd="strikeThrough"], ' +
            '.fr-command[data-cmd="subscript"], .fr-command[data-cmd="superscript"]'
        );

        buttons.each(function (this: HTMLElement) {
            const isPressed = this.getAttribute('aria-pressed') === 'true';

            if (isPressed) {
                this.classList.add('fr-btn-active-popup');
            } else {
                this.classList.remove('fr-btn-active-popup');
            }
        });
    };

    function handleInsertPopup(this: any, type: 'video' | 'image') {
        const $popup = this.popups.get(`${type}.insert`);
        const $inputField = $popup.find('input[type="text"]');
        const $submitButton = $popup.find('.fr-submit');

        // Initial state
        if ($inputField.val().trim() === '') {
            $submitButton.addClass('fr-disabled fr-btn');
        } else {
            $submitButton.removeClass('fr-disabled');
        }

        // Toggle disable/enable on input
        $inputField.on('input', function (this: HTMLInputElement) {
            if (this.value.trim() === '') {
                $submitButton.addClass('fr-disabled fr-btn');
            } else {
                $submitButton.removeClass('fr-disabled');
            }
        });
    }

    config.events['popups.show.video.insert'] = function (this: any) {
        handleInsertPopup.call(this, 'video');
    };

    config.events['popups.show.image.insert'] = function (this: any) {
        handleInsertPopup.call(this, 'image');
    };

    config.events['popups.show.link.insert'] = function (editor: any) {
        const $popup = this.popups.get('link.insert');

        const $cntCheckbox = $popup.find('.fr-checkbox-line');
        const $inputCheckbox = $cntCheckbox.find('input[type="checkbox"][name="target"]');
        const $labelCheckbox = $cntCheckbox.find('.fr-checkbox-line label');

        // Get specific input fields
        const $urlInput = $popup.find('input.fr-link-attr[id^="fr-link-insert-layer-url"]');
        const $submitButton = $popup.find('.fr-submit');

        if ($inputCheckbox) {
            $inputCheckbox.attr('checked', true);
            if ($labelCheckbox) {
                $labelCheckbox.attr('for', $inputCheckbox.attr('id'));
            }
        }

        $inputCheckbox.on('change', function () {
            const isChecked = $inputCheckbox[0].checked;
            editor.opts.linkNoOpener = isChecked;
            editor.opts.linkNoReferrer = isChecked;
        });

        // Disable/Enable insert button based on URL input
        const toggleInsertButton = () => {
            if ($urlInput.val().trim() === '') {
                $submitButton.addClass('fr-disabled fr-btn');
            } else {
                $submitButton.removeClass('fr-disabled');
            }
        };

        toggleInsertButton(); // run on popup open
        $urlInput.on('input', toggleInsertButton); // listen for typing
    };

    config.events['initialized'] = function (this: any) {
        const el = this.el;

        const keydownHandler = (e: KeyboardEvent) => {
            if (e.key === 'Backspace') {
                const range = this.selection.ranges();
                const commonContainer = range[0].commonAncestorContainer;
                const containerEl =
                    commonContainer.nodeType === 3
                        ? (commonContainer as Text).parentElement
                        : (commonContainer as HTMLElement);

                if (containerEl?.closest('.accordion, .accordion-panel, .tabs, .tabs-panel')) {
                    const removable = containerEl.closest('.accordion-panel, .tabs-panel');
                    if (removable) {
                        this.undo.saveStep();
                        removable.remove();
                        this.undo.saveStep();
                        e.preventDefault();
                    }
                }
            }

            if (e.which == FroalaEditor.KEYCODE.ENTER) {
                const targetElement = this.selection.element();

                if (targetElement?.closest('.accordion, .accordion-panel, .tabs, .tabs-panel, .tab-title, .accordion-header')) {
                    e.preventDefault();
                    this.cursor.enter(true);
                    return false;
                }
            }
        };

        this._customKeydownHandler = keydownHandler;
        this.events.on('keydown', keydownHandler, true);
    };

    config.events['commands.after'] = function (cmd: any) {
        if (cmd !== 'insertToken' && cmd !== 'insertKbArticle') {
            this.events.trigger('token.hidePopup');
        }

        if (cmd === 'insertToken' || cmd === 'insertKbArticle') {
            this.popups.hideAll();
        }

        if (cmd === 'unindent') {
            this.commands.outdent();
        }

        if (cmd === 'fixCodeView') {
            const fixCodeViewBtn = this.$tb.find('.fr-command[data-cmd="fixCodeView"]');
            const isCodeViewActive = this.codeView && this.codeView.isActive && this.codeView.isActive();

            if (fixCodeViewBtn.length) {
                fixCodeViewBtn.toggleClass('fr-btn-active-popup', isCodeViewActive);
            }
        }

    };
}