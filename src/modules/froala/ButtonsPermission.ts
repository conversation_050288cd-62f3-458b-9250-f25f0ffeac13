import { type ButtonPermission } from "@/types/froala/ButtonPermission";

export const defaultFroalaConfig: ButtonPermission = {
    enableBrEnter: false,
    enableBasicTextStyle: true,
    enableAdvancedTextStyle: true,
    enableColors: true,
    enableFontStyles: false,
    enableLineHeight: false,
    enableParagraphFormat: true,
    enableParagraphStyle: true,
    enableParagraphAlign: true,
    enableSelectAll: true,
    enableClearFormatting: true,
    enableAdvanced: true,
    enableLists: true,
    enableUndo: true,
    enableHR: true,
    enableLinks: true,
    enableImage: true,
    enableTeamviewer: false,
    enableCannedResponses: false,
    enableUpload: true,
    enableVideo: true,
    enableKbInsert: false,
    enableKbInsertBlock: true,
    enableKbInsertBlockEmbedded: true,
    enableTables: true,
    enableTabs: true,
    enableAccordions: true,
    enableDivPanels: false,
    enableSourceEdit: true,
    enableFontAwesome: true,
    enableHelp: false,
    enableTrackChanges: false,
    enableTrackChangesSingle: false,
    enableTokenInsert: true,
};

export const createFroalaConfig = (customConfig: Partial<ButtonPermission> = {}): ButtonPermission => {
    return {
        ...defaultFroalaConfig,
        ...customConfig
    };
};
