/**
 * Formatting helper functions for knowledge module
 */

/**
 * Formats a date safely to localized date string
 * @param dateStr The date string to format
 * @returns Formatted date string or 'Unknown' if invalid
 */
export const formatDate = (dateStr?: string): string => {
    if (!dateStr) return 'Unknown';
    try {
        return new Date(dateStr).toLocaleDateString();
    } catch (err) {
        console.error('Error formatting date:', err);
        return 'Invalid date';
    }
};

/**
 * Formats a date as a relative time (e.g. "2 hours ago")
 * @param dateStr The date string to format
 * @returns Formatted relative time string
 */
export const formatRelativeTime = (dateStr: string | number): string => {
    if (!dateStr) return 'Unknown';

    try {
        const date = new Date(dateStr);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);

        if (diffSec < 60) return 'Just now';
        if (diffMin < 60) return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
        if (diffHour < 24) return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
        if (diffDay < 1000) return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;

        return formatDate(dateStr.toString());
    } catch (e) {
        console.error('Error formatting relative time:', e);
        return 'Unknown date';
    }
};

/**
 * Extracts pathname from a URL
 * @param url The URL to extract pathname from
 * @returns The pathname portion of the URL
 */
export const extractPathname = (url: string): string => {
    try {
        // Use a simple regex to extract pathname
        const match = url.match(/^https?:\/\/[^\/]+(\/[^?#]*)/);
        return match ? match[1] : url;
    } catch (e) {
        return url;
    }
};
