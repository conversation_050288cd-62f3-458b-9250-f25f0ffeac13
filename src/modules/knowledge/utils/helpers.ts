/**
 * Knowledge module utility helper functions
 */

/**
 * Strips HTML tags and decodes HTML entities from a text string
 * @param text The HTML text to strip and decode
 * @returns Clean text without HTML tags or entities
 */
export const stripHtmlAndDecodeEntities = (text: string | null | undefined): string => {
  if (!text) return 'Untitled';
  
  try {
    // Create a new DOMParser
    const parser = new DOMParser();
    // Parse the text as HTML
    const dom = parser.parseFromString(
      '<!doctype html><body>' + text + '</body>', 
      'text/html'
    );
    // Get the text content which will have both HTML entities decoded and tags removed
    return dom.body.textContent || 'Untitled';
  } catch (error) {
    console.error('Error parsing HTML:', error);
    // Fallback method
    return text
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&#039;/g, "'")
      .replace(/<[^>]*>/g, '');
  }
};

/**
 * Converts status codes and strings to standardized status state
 * @param status The status value as string or number
 * @returns Standardized status state or undefined if not recognized
 */
export const getStatusState = (status: string | number): 'published' | 'draft' | 'archived' | 'new' | 'ready' | 'waiting' | 'resolved' | 'closed' | undefined => {
  // Convert to string for string comparison
  const statusStr = String(status).toLowerCase();
  
  // Handle string representations
  if (statusStr === 'published' || statusStr === '0' || statusStr === '0') {
    return 'published';
  } else if (statusStr === 'draft' || statusStr === '1' || statusStr === '1') {
    return 'draft';
  } else if (statusStr === 'archived' || statusStr === '98' || statusStr === '98') {
    return 'archived';
  } else if (statusStr === 'new') {
    return 'new';
  } else if (statusStr === 'ready') {
    return 'ready';
  } else if (statusStr === 'waiting') {
    return 'waiting';
  } else if (statusStr === 'resolved') {
    return 'resolved';
  } else if (statusStr === 'closed') {
    return 'closed';
  }
  
  // Handle numeric status codes directly
  if (typeof status === 'number') {
    switch (status) {
      case 0: return 'published';
      case 1: return 'draft';
      case 98: return 'archived';
      default: return undefined;
    }
  }
  
  return undefined;
};

/**
 * Converts a PST datetime string to ISO format
 * @param dateStr The date string in PST timezone
 * @returns ISO formatted date string
 */
export const toISOString = (dateStr: string): string => {
  try {
    // Add PST timezone indicator to the date string
    // Convert "2025-03-22 13:23:25" to "2025-03-22T13:23:25-07:00" (PST)
    const pstDate = dateStr.replace(' ', 'T') + '-07:00';
    
    // Create a date object and convert to ISO string
    // This will automatically handle the timezone conversion
    const date = new Date(pstDate);
    return date.toISOString();
  } catch (error) {
    console.error('Error converting date to ISO:', error);
    return dateStr; // Return original string instead of null to satisfy type check
  }
};

/**
 * Helper function to find an ID in a nested object returned from an API
 * @param obj The object to search for an ID
 * @returns The found ID or null
 */
export const findIdInObject = (obj: any): string | null => {
  if (!obj || typeof obj !== 'object') return null;
  
  // Check if the current object has an id property
  if (obj.id && typeof obj.id === 'string') {
    return obj.id;
  }
  
  // Recursively search through object properties
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      
      // Check if this is an ID key
      if ((key === 'id' || key.endsWith('_id')) && typeof value === 'string') {
        return value;
      }
      
      // Recursively search nested objects
      if (typeof value === 'object' && value !== null) {
        const result = findIdInObject(value);
        if (result) return result;
      }
      
      // Check array items
      if (Array.isArray(value)) {
        for (const item of value) {
          if (typeof item === 'object' && item !== null) {
            const result = findIdInObject(item);
            if (result) return result;
          }
        }
      }
    }
  }
  
  return null;
}; 