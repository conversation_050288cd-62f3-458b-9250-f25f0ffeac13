<script setup lang="ts">
import { useKnowledgeStore } from '../stores/knowledge.ts';
import { useUserStore } from '../../../stores/user.ts';
import { useRouter, useRoute } from 'vue-router';
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoPaginator from '@services/ui-component-library/components/BravoPaginator.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoBlock from '@services/ui-component-library/components/BravoBlock.vue';
import BravoAccordionPanel from '@services/ui-component-library/components/BravoAccordionPanel.vue';
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue';
import BravoAccordionContent from '@services/ui-component-library/components/BravoAccordionContent.vue';

import ToggleSwitch from '@services/ui-component-library/components/BravoToggleSwitch.vue';

import Popover from 'primevue/popover';
import Tree from 'primevue/tree';
import FroalaEditor from 'froala-editor';

import Tabs from 'primevue/tabs';
import TabList from 'primevue/tablist';
import Tab from 'primevue/tab';
import TabPanels from 'primevue/tabpanels';
import TabPanel from 'primevue/tabpanel';

import Timeline from 'primevue/timeline';
import Select from 'primevue/select';
import Menu from 'primevue/menu';
import buildFroalaConfig from '../../../froala/froala-config.ts';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue';
import Tooltip from 'primevue/tooltip';
import { useI18n } from 'vue-i18n';
import { useKnowledgeAPI } from '../../../composables/services/useKnowledgeAPI';
import type { ArticleRevision, RelatedArticle } from '@/composables/services/types/knowledge.ts';
import { ArticleStatus } from '../enums/ArticleStatus.ts';
import ArticleSidebar from '../components/ArticleSidebar.vue';
import DeleteArticleModal from '../components/DeleteArticleModal.vue';
import DiscardDraftModal from '../components/dialogs/DiscardDraftModal.vue';
import ArticlePreview from '../views/ArticlePreview.vue';
import { formatDate, formatRelativeTime } from '../utils/formatHelpers.ts';
import html2pdf from 'html2pdf.js';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import '@/assets/boomtown-icons/sass/src/all.scss';
import '@/assets/froala-custom.css';
import { parseButtons } from '@/modules/froala/Froala.ts';
import { globalEditor } from '@/modules/froala/EditorRef.ts';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import Skeleton from 'primevue/skeleton';
import BravoConfirmDialog from '@services/ui-component-library/components/BravoConfirmDialog.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';

import '@/modules/froala/plugins/code/CodeSnippet.css';
import '@/modules/froala/plugins/code/prism/prism.css';
import '@/modules/froala/plugins/code/prism/prism.js';
import FileManager from '@/components/FileManager';
import TokenPopup from '@/components/TokenPopup';

// Import the file manager composable
import { useFileManager } from '@/composables/useFileManager';
import { useAuthStore } from '../../../stores/auth.ts';

// Add import for the permission helpers
import { usePermissions } from '@/composables/usePermissions';

// Initialize the KnowledgeAPI composable
const knowledgeAPI = useKnowledgeAPI();

const handleFileSelected = (file:File) => {
  FroalaEditor.PLUGINS.fileManager().insertFile(file, editor.value);
  fileManager.showFileManager.value = false;
};

// Initialize the file manager composable with the article ID
const articleId = computed(() => (article.value as any)?.root_parent_id);
const fileManager = useFileManager(articleId, 'kb_library');

/**
 * Handle file upload when a file is selected from the file input
 * @param file The file to upload
 */
const handleFileSelect = async (file: File) => {
    await fileManager.uploadFile(
        file,
        () => {
            // Success callback
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'File uploaded successfully',
                life: 3000,
            });
        },
        (error) => {
            // Error callback
            console.error('Upload failed:', error);
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to upload file: ' + error.message,
                life: 5000,
            });
        }
    );
};

/**
 * Handle file deletion when a file is deleted from the file manager
 * @param event The file delete event containing the file and removeFromArticles flag
 */
const handleFileDelete = async (event: { file: any, removeFromArticles: boolean }) => {
    await fileManager.deleteFile(
        event.file,
        event.removeFromArticles,
        () => {
            // Success callback
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'File deleted successfully',
                life: 3000,
            });
        },
        (error) => {
            // Error callback
            console.error('Delete failed:', error);
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete file: ' + error.message,
                life: 5000,
            });
        }
    );
};

const { t } = useI18n();
const toast = useToast();
const confirm = useConfirm();
const isPanelMinimized = ref(false);
const isEditing = ref(false);
const editedContent = ref('');
const articleMenu = ref();
const isSavingRevision = ref(false);
const isDraftRevision = ref(false);
const froalaEditor = ref(null);
const editor = ref<any>(null);
const libraryCss = ref('');
const screensUrl = ref('https://f.goboomtown.com/screens');
const articleTheme = ref('modern');
const originalContent = ref('');
const isNewRevision = ref(false);
// Menu items will be dynamically set based on revision status
const articleMenuItems = ref([
    // Draft Revision Menu Items
    {
        label: t('knowledge.discard_draft'),
        icon: 'pi pi-trash',
        visible: false, // Will be controlled by setMenuItemsVisibility
        command: () => {
            discardDraft();
        },
        id: 'discard_draft'
    },
    // Published Revision Menu Items
    {
        label: t('knowledge.unpublish'),
        icon: 'pi pi-times-circle',
        visible: false, // Will be controlled by setMenuItemsVisibility
        command: () => {
            unpublishArticle();
        },
        id: 'unpublish',
    },
    // Common Menu Items (always visible)
    {
        label: t('knowledge.export_to_pdf'),
        icon: 'pi pi-file-pdf',
        visible: true, // Always visible
        command: () => {
            exportToPdf();
        },
        id: 'export_pdf',
    },
    {
        label: t('knowledge.delete_article'),
        icon: 'pi pi-trash',
        class: 'text-red-600',
        visible: () => can.deleteArticle(),
        command: () => {
            deleteArticle();
        },
        id: 'delete_article',
    },
]);
const showTokenPopup = ref(false);
const tokenPopup = ref<any>(null);
const tokenList = ref<Token[]>([]);
const initialToken  = ref<HTMLElement | null>(null);

interface Token {
  id: any;
  label: any;
}

async function fetchTokens(partnerId: any) {
  try {
    const response = await knowledgeAPI.getTokens(partnerId);
    tokenList.value = response.map((token: any) => ({
      id: token.id,
      label: token.label,
    }));
  } catch (err) {
    console.error('Failed to load tokens', err);
  }
}



// Create froalaConfig instance by calling the function
const froalaConfig = computed(() => {
    const config = buildFroalaConfig({
        object: 'kb',
        object_id: article.value?.id || '',
        authStore: useAuthStore(),
    });

    // Add the token.RequireOptions event to the froalaConfig
    config.events = {
        ...config.events,
        // 'token.RequireOptions': async function ($selector: any): Promise<void> {
        //     // Clear existing options completely
        //     $selector.empty();

        //     // Add default option
        //     $selector.append('<option value="" selected>Select a token...</option>');

        //     // Get current article's partner ID
        //     const partnerId = (article.value as any)?.owner_partner_id;

        //     if (!partnerId) {
        //         console.warn('Cannot fetch tokens: No partner ID available');
        //         return;
        //     }

        //     try {
        //         // Get tokens from your application
        //         const tokens = await knowledgeAPI.getTokens(partnerId);

        //         // Add tokens to the selector
        //         tokens.forEach((token) => {
        //             $selector.append(`<option value="${token.id}">${token.label}</option>`);
        //         });
        //     } catch (error) {
        //         console.error('Error fetching tokens:', error);
        //     }
        // },
        openInsertKbArticle: async function (editor: any): Promise<void> {
            insertKbTarget.value = document.querySelector('.fr-toolbar'); // or any fixed element
            if (insertKbPopover.value) {
                // PrimeVue Popover doesn't need an event parameter for toggle
                insertKbPopover.value.toggle(event);
            } else {
                console.error('insertKbPopover is not available');
            }
        },
        openfileManager: function (_editor: any): void {
            // First show the file manager with loading state
            fileManager.isLoadingFiles.value = true;
            fileManager.uploadProgress.value = 0;
            fileManager.showFileManager.value = true;

            // Then fetch the files asynchronously
            setTimeout(() => {
                fileManager.fetchFilesForTab(
                    fileManager.activeTab.value,
                    fileManager.currentPage.value,
                    12,
                    fileManager.searchQuery.value
                );
            }, 0);
        },
        'token.showPopup': function (token: any, mode: string): undefined {
            initialToken.value = token || null;
            fetchTokens(article.value.owner_partner_id);
            // Just set the visible state - no event needed
            // showTokenPopup.value = true;
            tokenPopup.value.show(mode, token);
        },
        'token.hidePopup': function (): undefined {
            // Just set the visible state - no event needed
            // showTokenPopup.value = false;
            tokenPopup.value.hide();
        },
    };

    // Update toolbar buttons
    parseButtons(config);

    return config;
});

const insertKbTarget = ref<HTMLElement | null>(null);
const insertKbPopover = ref<any>(null);
const isEmbedded = ref(false);
const nodes = ref([]);
const loading2 = ref(false);
const props = defineProps<{
    id: string;
}>();

const knowledgeStore = useKnowledgeStore();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const error = ref<string | null>(null);
const articleRevisions = ref<ArticleRevision[]>([]);
const loadingRevisions = ref(false);

// Set up ref for related articles
const relatedArticles = ref<RelatedArticle[]>([]);
const loadingRelatedArticles = ref(false);

/**
 * Transforms flat data into a hierarchical tree structure based on parent_id relationships
 * @param {Array} flatData - Flat array of objects with id and parent_id fields
 * @returns {Array} - Hierarchical tree structure
 */
function buildTreeFromFlatData(flatData: any[]) {
    // Create a map for quick lookups by id
    const nodeMap = new Map();

    // First pass: create nodes and add to map
    flatData.forEach((item) => {
        nodeMap.set(item.id, {
            key: item.id,
            id: item.id,
            label: item.lbl,
            icon: item.iconCls || 'pi pi-folder',
            data: item.description || item.lbl,
            leaf: item.leaf === true,
            url: item.url || '',
            children: item.leaf ? undefined : [], // Only include children array for non-leaf nodes
        });
    });

    // Result array to hold only top-level nodes
    const treeData: any = [];

    // Second pass: establish parent-child relationships
    flatData.forEach((item) => {
        const node = nodeMap.get(item.id);

        if (item.parent_id && nodeMap.has(item.parent_id)) {
            // This is a child node - add it to its parent's children array
            const parentNode = nodeMap.get(item.parent_id);

            // Make sure parent has a children array
            if (!parentNode.children) {
                parentNode.children = [];
            }

            parentNode.children.push(node);
        } else {
            // This is a top-level node (no parent or parent not in the dataset)
            treeData.push(node);
        }
    });

    return treeData;
}

const loadRootNodes = async () => {
    try {
        const data = await knowledgeAPI.getKBsTree();
        nodes.value = buildTreeFromFlatData(data);
    } catch (error) {
        console.error('Error loading root nodes:', error);
    }
};

const onNodeExpand = async (node: any) => {
    if (!node.leaf) {
        loading2.value = true;

        try {
            const childNodes = await knowledgeAPI.fetchKnowledgeTree({ node: node.key });
            node.children = buildTreeFromFlatData(childNodes);

            // Vue reactivity fix: force update
            nodes.value = [...nodes.value];
        } catch (error) {
            console.error('Error loading child nodes:', error);
        } finally {
            loading2.value = false;
        }
    }
};

const onNodeSelect = (node: any) => {
    if (node.leaf) {
        // Handle click only on non-expanding (leaf) nodes
        node.isEmbedded = isEmbedded.value;
        FroalaEditor.PLUGINS.kbArticle().insertKbArticle(node, editor.value);
        // You can call your insertCallback or any logic here
    }
};

const setMenuItemsVisibility = (status: string) => {
    // Find menu items by ID
    const discardDraftItem = articleMenuItems.value.find((i) => i.id === 'discard_draft');
    const unpublishItem = articleMenuItems.value.find((i) => i.id === 'unpublish');

    // Set visibility based on revision status
    if (discardDraftItem) {
        discardDraftItem.visible = status === ArticleStatus.DRAFT;
    }

    if (unpublishItem) {
        unpublishItem.visible = status === ArticleStatus.PUBLISHED;
    }
};

const unpublishArticle = async () => {
    if (article.value) {
        try {
            const updatedArticle = {
                id: article.value.id,
                _unpublish: true,
            };

            const response = await knowledgeAPI.unpublishArticles([updatedArticle]);

            if (response.success) {
                toast.add({
                    severity: 'success',
                    summary: t('knowledge.unpublish'),
                    detail: t('common.success'),
                    life: 3000,
                });

                // Refresh revisions to show the updated status
                await fetchArticleRevisions();
            } else {
                toast.add({
                    severity: 'error',
                    summary: t('knowledge.unpublish'),
                    detail: response.message || t('common.error'),
                    life: 5000,
                });
            }
        } catch (error) {
            console.error('Error unpublishing article:', error);
            toast.add({
                severity: 'error',
                summary: t('knowledge.unpublish'),
                detail: t('common.error'),
                life: 5000,
            });
        }
    }
};

// Function to check screen width and set sidebar state
const checkScreenWidth = () => {
    isPanelMinimized.value = window.innerWidth <= 1000;
};

const isContentDirty = () => {
    return editedContent.value !== originalContent.value;
};

const resetEditing = () => {
    isEditing.value = false;
    editedContent.value = '';
    originalContent.value = '';
    isNewRevision.value = false;
};

const cancelEdit = async () => {
    if (isContentDirty()) {
        confirm.require({
            message: 'You have unsaved changes. What would you like to do?',
            header: 'Unsaved Changes',
            icon: 'pi pi-exclamation-triangle',
            acceptClass: 'p-button-success',
            rejectClass: 'p-button-danger',
            acceptLabel: 'Save',
            rejectLabel: 'Discard',
            accept: async () => {
                // Save changes by toggling edit mode which handles saving
                await toggleEditMode();
            },
            reject: () => {
                resetEditing();
            },
        });
    } else if (isNewRevision.value) {
        showLoading(true);
        await knowledgeAPI.putRevisions([{ status: 99, id: selectedRevision.value.code }]);
        await fetchArticleRevisions();
        resetEditing();
        showLoading(false);
    } else {
        resetEditing();
    }
};

const togglePanel = () => {
    isPanelMinimized.value = !isPanelMinimized.value;
};

const isArticlePublished = () => {
    return selectedRevision.value?.status === ArticleStatus.PUBLISHED;
};

const isArticleDraft = () => {
    return selectedRevision.value?.status === ArticleStatus.DRAFT;
};

const isArticlePreviouslyPublished = () => {
    return selectedRevision.value?.status === ArticleStatus.PREVIOUSLY_PUBLISHED;
};

const getActiveDraftId = () => {
    return (
        (selectedRevision.value?.revision?.status !== 99 && selectedRevision.value?.revision?.kb_active_draft_id) ||
        null
    );
};

const createAndSwitchToNewDraft = async (selectedRevision: any) => {
    const data = {
        _copy_revision: true,
        id: selectedRevision?.value?.code,
    };

    const response = await knowledgeAPI.putRevisions([data]);

    return response;
};

const assignContentToEditor = () => {
    const articleContent = (article.value as any).body || article.value.content || '';

    originalContent.value = articleContent;
    editedContent.value = articleContent;
};

const onEditArticle = async () => {
    const isPublished = isArticlePublished();
    const isRevision = isArticlePreviouslyPublished();
    const latestDraft = getActiveDraftId();

    if (!latestDraft && (isPublished || isRevision)) {
        await createAndSwitchToNewDraft(selectedRevision);

        await fetchArticleRevisions();
        assignContentToEditor();
        isNewRevision.value = true;
    } else if (latestDraft) {
        if (isPublished) {
            // Change to the latest draft revision
            const newRevision = revisions.value.find((r) => r.code === latestDraft) || selectedRevision;

            handleRevisionChange({ value: newRevision });
        } else if (isRevision) {
            // Show confirmation dialog for restoring draft
            restoreArticle();
        } else {
            assignContentToEditor();
        }
    }

    // assignContentToEditor();
    isEditing.value = !isEditing.value;
};

const toggleEditMode = async () => {
    if (isEditing.value) {
        // Save changes
        if (article.value) {
            isSavingRevision.value = true;
            let isSaved = false;

            // Get the revision ID if we're working with a specific revision
            const revisionId =
                selectedRevision.value?.code && selectedRevision.value.code !== 'current'
                    ? selectedRevision.value.code
                    : undefined;
            if (!revisionId) {
                console.log('No revision ID. Unable to save content.');
                return;
            }
            const revision = selectedRevision.value;
            // Update the item with content and revision ID if available
            if (revision.status === ArticleStatus.PUBLISHED) {
                await restoreArticle();
            } else if (revision.status === ArticleStatus.DRAFT) {
                const result = await knowledgeStore.updateItem(
                    article.value.id,
                    {
                        content: '<bt/>' + editedContent.value,
                        title: article.value.title,
                        sub_title: article.value.sub_title || (article.value as any).c__sub_title || '',
                    },
                    revisionId,
                    true
                );

                if (result) {
                    isSaved = true;
                }
            }
            await fetchArticleRevisions();

            // Show saving indicator for a short time
            setTimeout(() => {
                isSavingRevision.value = false;
                if (isSaved) {
                    void fetchArticleRevisions();
                }
            }, 1000);
        }

        isEditing.value = !isEditing.value;
    } else {
        // Enter edit mode
        onEditArticle();
    }
};

// Function to fetch related articles
const fetchRelatedArticles = async () => {
    if (!selectedRevision.value || !selectedRevision.value.code) return;

    loadingRelatedArticles.value = true;
    try {
        // Use the selected revision's ID to fetch related articles
        const result = await knowledgeAPI.fetchRelatedArticles(
            selectedRevision.value.code,
            selectedRevision.value.revision?.updated
        );
        relatedArticles.value = result.items;
    } catch (err) {
        console.error('Error loading related articles:', err);
    } finally {
        loadingRelatedArticles.value = false;
    }
};

// If we're navigating directly to the article or refreshing the page,
// fetch the article data using the ID
onMounted(async () => {
    loading.value = true;
    try {
        // Check if article is already available in the store's items
        const existingArticle = knowledgeStore.getItemById(props.id);

        if (!existingArticle && !knowledgeStore.currentArticle) {
            await knowledgeStore.fetchArticleById(props.id);
        }

        // After article is loaded, fetch the revisions
        await fetchArticleRevisions();

        // After revisions are loaded, fetch related articles
        await fetchRelatedArticles();

        // Fetch article library CSS for styling the preview
        await fetchArticleLibraryCss();
    } catch (err) {
        console.error('Error loading article:', err);
        error.value = err instanceof Error ? err.message : 'Failed to load article';
    } finally {
        loading.value = false;
    }

    // Check initial screen width and set sidebar state
    checkScreenWidth();

    // Add event listener for window resize
    window.addEventListener('resize', checkScreenWidth);

    // Add event listener for code block copying
    document.addEventListener('click', handleCopyCode);
    loadRootNodes();
});

/**
 * Fetches the article's library CSS for styling the preview
 */
const fetchArticleLibraryCss = async () => {
    try {
        const response = await knowledgeAPI.fetchKnowledgeListingArticle({
            filter: [
                { property: 'id', value: props.id }
            ]
        });

        if (response.items && response.items.length > 0) {
            libraryCss.value = (response.items[0] as any).library_css || '';
        }
    } catch (error) {
        console.error('Error fetching article library CSS:', error);
    }
};

onUnmounted(() => {
    // Clear the current article when leaving the view
    knowledgeStore.clearCurrentArticle();

    // Remove event listener when component is unmounted
    window.removeEventListener('resize', checkScreenWidth);

    // Remove event listener for code block copying
    document.removeEventListener('click', handleCopyCode);
});

// Get article either from the items array or from currentArticle
const article = computed((): any => {
    const fromItems = knowledgeStore.getItemById(props.id);
    const baseArticle = fromItems || knowledgeStore.currentArticle;

    if (!baseArticle) {
        return null; // Handle the case where baseArticle is null
    }

    if (articleRevisions.value.length > 0 && selectedRevision.value?.revision) {
        return {
            ...baseArticle,
            body: selectedRevision.value.revision.body || baseArticle.body,
            body_rendered: selectedRevision.value.revision.body_rendered || (baseArticle as any).body_rendered,
            title: selectedRevision.value.revision.title || baseArticle.title,
        };
    } else {
        return baseArticle;
    }
});

// Get article either from the items array or from currentArticle
const revision = computed((): any => {
    const fromItems = knowledgeStore.getItemById(props.id);

    if (articleRevisions.value.length > 0) {
        const baseArticle = fromItems || knowledgeStore.currentArticle;
        return { ...baseArticle, ...articleRevisions.value.find((rev) => rev.id === selectedRevision.value?.code) };
    } else {
        return fromItems || knowledgeStore.currentArticle;
    }
});

// const currentRevision = computed(() => {
//     console.log('articleRevisions.value', articleRevisions.value)
//     return articleRevisions.value.find(rev => rev.id === selectedRevision.value?.code);
// });

// console.log('currentRevision', currentRevision.value)

// When initializing the editor, set the global instance
const onEditorInitialized = (editorInstance: any) => {
    editor.value = editorInstance;
    globalEditor.setInstance(editorInstance);
};

watch(froalaEditor, (newFroalaEditor: any) => {
    editor.value = newFroalaEditor?.getEditor();
});
// Update document title when article changes
watch(
    article,
    (newArticle) => {
        if (newArticle && newArticle.title) {
            // Set document title with the article title
            document.title = `Knowledge Article: ${newArticle.title} - CXME UI`;
        } else {
            // Default article page title
            document.title = 'Knowledge Article - CXME UI';
        }
    },
    { immediate: true }
);

// Update the watch effect to handle article content properly
watch(
    article,
    (newArticle) => {
        if (newArticle) {
            // TODO: Refactor this to use proper typing instead of type assertions
            // Consider extending the Article interface to include body and other properties
            const content = (newArticle as any).body || newArticle.content || '';
            editedContent.value = content;
            originalContent.value = content;

            // Fetch library CSS when article changes
            fetchArticleLibraryCss();
        }
    },
    { immediate: true }
);

const isEditingTitle = ref(false);
const isEditingSubtitle = ref(false);
const editedTitle = ref('');
const editedSubtitle = ref('');

// Create computed properties for article metadata
const articleStatus = computed(() => {
    if (!article.value) return 'unknown';
    if ((article.value as any).isDraft) return 'draft';
    if ((article.value as any).isPublished) return 'published';
    if ((article.value as any).isArchived) return 'archived';
    return (article.value as any).c__d_status?.toLowerCase() || 'draft';
});

const articleVisibility = computed(() => {
    if (!article.value) return 'unknown';
    return (article.value as any).c__d_visibility || ((article.value as any).isPrivate ? 'Internal' : 'Public');
});

const articleTags = computed(() => {
    if (!article.value) return [];
    // Combine all possible tag sources
    return [
        ...((article.value as any).bc__tags_object_kb || []),
        ...((article.value as any).bc__tags_linked_kb || []),
        ...((article.value as any).bc__tags_support || []),
    ];
});

// Fetch article revisions from the API
const fetchArticleRevisions = async () => {
    if (!props.id) return;

    loadingRevisions.value = true;
    try {
        const result = await knowledgeAPI.fetchArticleRevisions(props.id);
        articleRevisions.value = result.items;

        // If we're looking at a specific revision, refresh its content
        if (selectedRevision.value && selectedRevision.value.code) {
            const currentRevisionId = selectedRevision.value.code;
            const updatedRevision = result.items.find(rev => rev.id === currentRevisionId);

            if (updatedRevision && article.value) {
                // Update the article with fresh content
                const updatedArticle = {
                    ...article.value,
                    body: updatedRevision.body || article.value.body,
                    body_rendered: updatedRevision.body_rendered || (article.value as any).body_rendered
                };

                // Update the store with fresh content
                knowledgeStore.setCurrentArticle(updatedArticle);

                // Update editor content references
                editedContent.value = updatedRevision.body || '';
                originalContent.value = updatedRevision.body || '';
            }
        }

        console.log('Loaded article revisions:', articleRevisions.value);
    } catch (err) {
        console.error('Error loading article revisions:', err);
    } finally {
        loadingRevisions.value = false;
    }
};

// Add a type definition for the revision item used in the dropdown
interface RevisionItem {
    name: string;
    code: string;
    date: Date;
    status: string;
    revision?: ArticleRevision;
    uiAccess?: any;
}

// Create revision data from article information
const revisions = computed<RevisionItem[]>(() => {
    // If we have revisions from the API, use those
    if (articleRevisions.value && articleRevisions.value.length > 0) {
        return articleRevisions.value.map((revision) => {
            let status = ArticleStatus.DRAFT; // Default to DRAFT
            let name = 'Revision';

            if (revision.isDraft) {
                status = ArticleStatus.DRAFT;
                name = t('knowledge.draft');
            } else if (revision.isPublished && revision.isActive) {
                status = ArticleStatus.PUBLISHED;
                name = t('knowledge.published');
            } else if (!revision.isDraft && !revision.isActive) {
                status = ArticleStatus.PREVIOUSLY_PUBLISHED;
                name = t('knowledge.previously_published');
            }

            const dateValue = revision.updated || revision.created || Date.now();
            const date = new Date(revision.updated || revision.created || Date.now());
            const dateString = date.toLocaleDateString() + ' (' + formatRelativeTime(dateValue) + ')';
            return {
                name,
                code: revision.id,
                date: new Date(revision.updated || revision.created || Date.now()),
                dateString: dateString,
                status,
                revision,
                uiAccess: revision._uiAccess,
            };
        });
    }

    // Fallback to the original logic if no revisions are available
    if (!article.value)
        return [
            {
                name: 'Current',
                code: 'current',
                date: new Date(),
                status: ArticleStatus.DRAFT,
            },
        ];

    const revisionsList = [];

    if ((article.value as any)._activeRevisionId) {
        revisionsList.push({
            name: (article.value as any).isDraft
                ? 'Current (Draft)'
                : (article.value as any).isPublished
                  ? 'Current (Published)'
                  : (article.value as any).isArchived
                    ? 'Current (Archived)'
                    : 'Current',
            code: (article.value as any)._activeRevisionId,
            date: new Date((article.value as any).updated || (article.value as any).created),
            status: (article.value as any).isDraft
                ? ArticleStatus.DRAFT
                : (article.value as any).isPublished
                  ? ArticleStatus.PUBLISHED
                  : (article.value as any).isArchived
                    ? ArticleStatus.ARCHIVED
                    : ArticleStatus.DRAFT,
        });
    }

    if (
        (article.value as any)._latestDraftId &&
        (article.value as any)._latestDraftId !== (article.value as any)._activeRevisionId
    ) {
        revisionsList.push({
            name: 'Latest Draft',
            code: (article.value as any)._latestDraftId,
            date: new Date((article.value as any).c__last_edited || (article.value as any).updated),
            status: ArticleStatus.DRAFT,
        });
    }

    // If we don't have any revisions yet, add a default one
    if (revisionsList.length === 0) {
        revisionsList.push({
            name: t('knowledge.draft'),
            code: 'current',
            date: new Date((article.value as any).updated || (article.value as any).created),
            status: ArticleStatus.DRAFT,
        });
    }

    return revisionsList;
});

const selectedRevision = ref(revisions.value[0]);

// Update the selectedRevision when revisions change
watch(
    revisions,
    (newRevisions) => {
        if (newRevisions.length > 0) {
            selectedRevision.value = newRevisions[0];
        }
    },
    { immediate: true }
);

// Watch for changes in the selected revision and fetch related articles
watch(
    selectedRevision,
    async (newRevision: RevisionItem) => {
        if (!newRevision || !newRevision.code || newRevision.code === 'current') return;

        // Update menu items visibility based on revision status
        setMenuItemsVisibility(newRevision.status);

        // If the revision has a full revision object, update the article and editor content
        if (newRevision.revision && (newRevision.revision.body || newRevision.revision.body_rendered)) {
            if (article.value) {
                const updatedArticle = {
                    ...article.value,
                    body: newRevision.revision.body || article.value.body,
                    body_rendered: newRevision.revision.body_rendered || (article.value as any).body_rendered,
                    title: newRevision.revision.title || article.value.title,
                    c__revision_owner_id: article.value.c__revision_owner_id,
                    owner_id: article.value.owner_id,
                    _activeRevisionId: article.value._activeRevisionId,
                };

                knowledgeStore.setCurrentArticle(updatedArticle);
                editedContent.value = newRevision.revision.body || '';
                originalContent.value = newRevision.revision.body || '';
            }

            await fetchRelatedArticles();
            return;
        }

        // Otherwise, fetch the revision content
        try {
            loadingRevisions.value = true;
            const revisionData = await knowledgeAPI.fetchRevisionById(newRevision.code);

            if (article.value && revisionData) {
                const updatedArticle = {
                    ...article.value,
                    body: revisionData.body || article.value.body,
                    body_rendered: revisionData.body_rendered || (article.value as any).body_rendered,
                    title: revisionData.title || article.value.title,
                    c__revision_owner_id: article.value.c__revision_owner_id,
                    owner_id: article.value.owner_id,
                    _activeRevisionId: article.value._activeRevisionId,
                };

                knowledgeStore.setCurrentArticle(updatedArticle);
                editedContent.value = revisionData.body || '';
                originalContent.value = revisionData.body || '';

                selectedRevision.value = {
                    ...selectedRevision.value,
                    revision: revisionData,
                };

                await fetchRelatedArticles();
            }
        } catch (err) {
            console.error('Error loading revision content:', err);
        } finally {
            loadingRevisions.value = false;
        }
    },
    { immediate: false }
);

const startEditingTitle = () => {
    editedTitle.value = article.value?.title || '';
    isEditingTitle.value = true;
};

const startEditingSubtitle = () => {
    // Handle subtitle safely since it might not exist in the KnowledgeItem type
    editedSubtitle.value = (article.value as any)?.subtitle || '';
    isEditingSubtitle.value = true;
};

const saveTitle = () => {
    if (article.value && editedTitle.value.trim()) {
        knowledgeStore.updateItem(article.value.id, { title: editedTitle.value.trim() }, selectedRevision.value?.code);
        isEditingTitle.value = false;
    }
};

const saveSubtitle = () => {
    if (article.value && editedSubtitle.value.trim()) {
        // Use type assertion to avoid TypeScript errors
        knowledgeStore.updateItem(
            article.value.id,
            {
                metadata: {
                    ...article.value.metadata,
                    subtitle: editedSubtitle.value.trim(),
                },
            } as any,
            selectedRevision.value?.code
        );
        isEditingSubtitle.value = false;
    }
};

const handleBack = () => {
    router.push('/knowledge');
};

// Set initial edit state based on query parameter
isEditing.value = route.query.mode === 'edit';

// Watch for changes to the query parameter
watch(
    () => route.query.mode,
    (newMode) => {
        isEditing.value = newMode === 'edit';
    }
);

// Add this helper function to extract pathname from URL
const extractPathname = (url: string): string => {
    try {
        // Use a simple regex to extract pathname
        const match = url.match(/^https?:\/\/[^\/]+(\/[^?#]*)/);
        return match ? match[1] : url;
    } catch (e) {
        return url;
    }
};

// Add the export to PDF function
const exportToPdf = async () => {
    if (!article.value) return;

    // Show loading toast
    toast.add({
        severity: 'info',
        summary: t('knowledge.export_to_pdf'),
        detail: t('common.processing'),
        life: 3000,
    });

    // Create a temporary div to style and organize the content
    const element = document.createElement('div');
    element.style.padding = '20px';
    element.style.fontFamily = 'Arial, Helvetica, sans-serif';

    // Add title
    const titleEl = document.createElement('h1');
    titleEl.textContent = article.value.title || 'Untitled Article';
    titleEl.style.marginBottom = '5px';
    titleEl.style.color = '#1e293b';
    titleEl.style.fontSize = '24px';
    element.appendChild(titleEl);

    // Add subtitle if available
    if ((article.value as any).sub_title || (article.value as any).c__sub_title) {
        const subtitleEl = document.createElement('h2');
        subtitleEl.textContent = (article.value as any).sub_title || (article.value as any).c__sub_title;
        subtitleEl.style.marginBottom = '20px';
        subtitleEl.style.color = '#64748b';
        subtitleEl.style.fontWeight = 'normal';
        subtitleEl.style.fontSize = '18px';
        element.appendChild(subtitleEl);
    }

    // Add content (use the rendered HTML)
    const contentEl = document.createElement('div');
    contentEl.innerHTML =
        (article.value as any).body_rendered || (article.value as any).body || article.value.content || '';

    // Add some CSS to fix code blocks and other formatting in the PDF
    const contentStyle = document.createElement('style');
    contentStyle.textContent = `
        pre, code {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        table, th, td {
            border: 1px solid #e2e8f0;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f8fafc;
        }

        a {
            color: #3182ce;
            text-decoration: underline;
        }
    `;

    contentEl.appendChild(contentStyle);
    element.appendChild(contentEl);

    // Configure PDF options
    const options = {
        margin: [15, 15, 15, 15],
        filename: `${article.value.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'article'}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
            scale: 2,
            useCORS: true,
            logging: false,
            letterRendering: true,
        },
        jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait' as const,
            compress: true,
        },
        pagebreak: { mode: ['avoid-all', 'css', 'legacy'] },
        footer: {
            height: '10mm',
            contents: {
                default:
                    '<div style="text-align: center; font-size: 10px; color: #64748b; width: 100%;">Page {{page}} of {{pages}}</div>',
            },
        },
    };

    try {
        // Generate the PDF
        await html2pdf().from(element).set(options).save();

        // Show success toast when complete
        toast.add({
            severity: 'success',
            summary: t('knowledge.export_to_pdf'),
            detail: t('common.success'),
            life: 3000,
        });
    } catch (error) {
        console.error('Error generating PDF:', error);

        // Show error toast
        toast.add({
            severity: 'error',
            summary: t('knowledge.export_to_pdf'),
            detail: t('common.error'),
            life: 5000,
        });
    }
};
const showLoading = (show: boolean) => {
    loadingRevisions.value = show;
};

const publishArticle = async () => {
    if (article.value) {
        showLoading(true);
        await knowledgeStore.publishRevision(
            article.value.id,
            { content: '<bt/>' + (article.value as any).body },
            selectedRevision.value.code
        );
        await fetchArticleRevisions();
        showLoading(false);
    }
};

const restoreArticle = () => {
    if (article.value) {
        confirm.require({
            message: 'Your current draft will be deleted. Are you sure?',
            header: 'Restore Draft',
            icon: 'pi pi-exclamation-triangle',
            acceptClass: 'p-button-primary',
            rejectClass: 'p-button-secondary',
            acceptLabel: 'Yes',
            rejectLabel: 'Cancel',
            accept: async () => {
                await knowledgeStore.updateRevisionToDraft(
                    article.value.id,
                    { content: (article.value as any).body },
                    selectedRevision.value.code
                );
                await fetchArticleRevisions();
            },
            reject: () => {
                // Do nothing on reject
            },
        });
    }
};

const getTagStateFromStatus = (status: string) => {
    return loadingRevisions.value
        ? 'waiting'
        : status === ArticleStatus.PUBLISHED
          ? 'published'
          : status === ArticleStatus.DRAFT
            ? 'draft'
            : status === ArticleStatus.PREVIOUSLY_PUBLISHED
              ? 'previously-published'
              : 'draft';
};

const getTagSeverityFromStatus = (status: string) => {
    return status === ArticleStatus.PUBLISHED ? 'success' : status === ArticleStatus.DRAFT ? 'warning' : 'info';
};

const getTagIconFromStatus = (status: string) => {
    return status === ArticleStatus.PUBLISHED
        ? 'pi pi-check-circle'
        : status === ArticleStatus.DRAFT
          ? 'pi pi-pencil'
          : 'pi pi-info-circle';
};

const deleteArticle = () => {
    if (!can.deleteArticle()) {
        return;
    }
    // Use the DeleteArticleModal component instead of the confirm dialog
    if (article.value) {
        deleteArticleModal.value.showConfirmation();
    }
};

// Add a handler for successful deletion
const handleArticleDeleted = (articleId: string) => {
    // Navigate back to knowledge article list after successful deletion
    router.push('/knowledge');
};

// Add reference to DeleteArticleModal component
const deleteArticleModal = ref();
const discardDraftModal = ref();

// Add a handler for successful draft discard
const handleDraftDiscarded = (draftId: string) => {
    // Refresh article revisions
    fetchArticleRevisions();
};

// Add a function to discard draft
const discardDraft = () => {
    if (selectedRevision.value && selectedRevision.value.code) {
        discardDraftModal.value.showConfirmation();
    }
};

// Add a handler to re-fetch the article and revisions
const handleSidebarUpdate = async () => {
    // await store.fetchArticleById(props.id);
    await fetchArticleRevisions();
};
// Add this function after the other handler functions
const handleCopyCode = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (!target) return;

    const button = target.closest('li');
    if (!button || !button.id) return;

    const [action, id] = button.id.split('_');
    if (action !== 'copyCode' || !id) return;

    const codeBlock = document.getElementById(id);
    if (codeBlock) {
        const codeElement = codeBlock.querySelector('code');
        if (codeElement) {
            navigator.clipboard
                .writeText(codeElement.innerText)
                .catch((err) => console.error('Failed to copy code:', err));
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Code copied to clipboard',
                life: 3000,
            });
        }
    }
};

const handleRevisionChange = (event: any) => {
    if (!event.value) return;

    // Update the selected revision
    selectedRevision.value = event.value;
};

// Add in the setup section, near other composable initializations
const { can } = usePermissions();
</script>

<template>
    <!-- Add BravoConfirmDialog component for delete confirmation -->
    <BravoConfirmDialog />

    <div v-if="loading" class="article-view" data-testid="article-loading">
        <div class="article-layout">
            <div class="article-main" data-testid="article-main">
                <div class="top-nav" data-testid="article-top-nav">
                    <BravoButton
                        icon="pi pi-arrow-left"
                        text
                        @click="handleBack"
                        class="back-button mr-4"
                        style="--icon-color: var(--surface-600)"
                        data-testid="back-button"
                    />
                    <BravoTitlePage data-testid="article-page-title">{{ article?.type === 1 ? t('knowledge.template') : article?.type === 0 ? t('knowledge.article') : '' }}</BravoTitlePage>
                    <div class="revision-selector" data-testid="revision-selector">
                        <BravoBody class="viewing-label" data-testid="viewing-label">{{
                            t('knowledge.viewing')
                        }}</BravoBody>
                        <div class="revision-option">
                            <Skeleton width="150px" height="30px" />
                        </div>
                    </div>
                </div>

                <div class="article-container">
                    <div class="article-header">
                        <div class="article-title-area">
                            <Skeleton width="80%" height="3rem" class="mb-3" />
                            <Skeleton width="60%" height="1.5rem" class="mb-4" />
                        </div>

                        <div class="article-meta-info">
                            <div class="meta-row">
                                <div>
                                    <Skeleton
                                        width="120px"
                                        height="1.5rem"
                                        class="mb-2 bg-blue-200 rounded-md shadow-sm"
                                    />
                                    <Skeleton width="180px" height="1.5rem" class="bg-gray-200 rounded-md" />
                                </div>
                                <div>
                                    <Skeleton width="100px" height="1.5rem" class="mb-2" />
                                    <Skeleton width="120px" height="1.5rem" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="article-content" style="display: flex; flex-direction: column; gap: 2rem">
                        <!-- Title skeleton - larger -->
                        <Skeleton width="70%" height="3rem" />

                        <!-- Metadata line -->
                        <Skeleton width="30%" height="1.25rem" style="margin-bottom: 1.5rem" />

                        <!-- Heading 1 -->
                        <Skeleton width="25%" height="2.25rem" style="margin-top: 2rem; margin-bottom: 1rem" />

                        <!-- Short paragraph -->
                        <Skeleton width="25%" height="1.5rem" style="margin-bottom: 2rem" />

                        <!-- Longer paragraph -->
                        <Skeleton width="100%" height="1.5rem" />
                        <Skeleton width="100%" height="1.5rem" />
                        <Skeleton width="95%" height="1.5rem" />
                        <Skeleton width="85%" height="1.5rem" style="margin-bottom: 2rem" />

                        <!-- Table/image representation -->
                        <div style="margin: 2.5rem 0">
                            <Skeleton width="50%" height="2rem" style="margin-bottom: 1rem" />
                            <!-- Table header -->
                            <Skeleton width="100%" height="150px" />
                            <!-- Table body -->
                        </div>

                        <!-- List items -->
                        <div style="display: flex; gap: 0.5rem; margin-left: 2rem; margin-bottom: 0.5rem">
                            <Skeleton width="10px" height="10px" style="margin-top: 0.5rem" />
                            <!-- Bullet -->
                            <Skeleton width="15%" height="1.5rem" />
                            <!-- List item text -->
                        </div>
                        <div style="display: flex; gap: 0.5rem; margin-left: 2rem; margin-bottom: 3rem">
                            <Skeleton width="10px" height="10px" style="margin-top: 0.5rem" />
                            <!-- Bullet -->
                            <Skeleton width="10%" height="1.5rem" />
                            <!-- List item text -->
                        </div>

                        <!-- Heading 2 -->
                        <Skeleton width="25%" height="2.25rem" style="margin-top: 2rem" />
                    </div>
                </div>
            </div>

            <!-- Replace the sidebar skeleton with the actual ArticleSidebar component -->
            <ArticleSidebar
                :article="{ id: props.id, title: '' }"
                :isLoading="loading"
                :relatedArticles="[]"
                :loadingRelatedArticles="true"
                :isPanelMinimized="isPanelMinimized"
                :isEditing="false"
                @toggle-panel="togglePanel"
            />
        </div>
    </div>

    <div v-else-if="error" class="article-error" data-testid="article-error">
        <p>{{ error }}</p>
        <BravoButton label="Go Back" icon="pi pi-arrow-left" @click="handleBack" data-testid="error-back-button" />
    </div>

    <div class="article-view" v-else-if="article" data-testid="article-view" :class="{ 'not-editing': !isEditing, 'is-editing': isEditing }">
        <div class="article-layout">
            <div class="article-main" data-testid="article-main">
                <div class="top-nav" data-testid="article-top-nav">
                    <div class="top-nav-left">
                        <BravoButton
                            icon="pi pi-arrow-left"
                            text
                            @click="handleBack"
                            class="back-button mr-3"
                            style="--icon-color: var(--surface-600)"
                            data-testid="back-button"
                        />
                    <BravoTitlePage data-testid="article-page-title">{{ article?.type === 1 ? t('knowledge.template') : article?.type === 0 ? t('knowledge.article') : '' }}</BravoTitlePage>

                    </div>

                    <div class="revision-selector" data-testid="revision-selector">
                        <div class="viewing-label" data-testid="viewing-label">{{
                            t('knowledge.viewing')
                        }}</div>
                        <Select
                            v-model="selectedRevision"
                            :options="revisions"
                            optionLabel="dateString"
                            class="revision-dropdown"
                            :disabled="loadingRevisions || isEditing"
                            data-testid="revision-dropdown"
                            @change="handleRevisionChange"
                        >
                            <template #value="slotProps">
                                <div class="revision-option" data-testid="selected-revision">
                                    <BravoTag
                                        :value="loadingRevisions ? 'Loading...' : slotProps.value.name"
                                        :state="getTagStateFromStatus(slotProps.value.status)"
                                    />
                                    <span class="revision-date" data-testid="revision-date">
                                        {{ loadingRevisions ? 'Loading revision...' : slotProps.value.dateString }}
                                    </span>
                                    <i
                                        v-if="loadingRevisions"
                                        class="pi pi-spin pi-spinner ml-2"
                                        data-testid="revision-loading-spinner"
                                    ></i>
                                </div>
                            </template>
                            <template #option="slotProps">
                                <div class="revision-option" :data-testid="`revision-option-${slotProps.option.code}`">
                                    <BravoTag
                                        :value="slotProps.option.name"
                                        :state="getTagStateFromStatus(slotProps.option.status)"
                                    />
                                    <div class="revision-details">
                                        <div class="revision-date-small">{{ slotProps.option.dateString }}</div>
                                    </div>
                                </div>
                            </template>
                        </Select>
                    </div>
                    <div class="ml-auto edit-buttons" data-testid="edit-buttons">
                        <template v-if="isEditing">
                            <BravoButton
                                :label="t('common.cancel')"
                                icon="pi pi-times"
                                severity="secondary"
                                outlined
                                @click="cancelEdit"
                                class="mr-3"
                                data-testid="cancel-edit-button"
                            />
                            <BravoButton
                                :label="isSavingRevision ? t('common.saving') : t('common.save')"
                                :icon="isSavingRevision ? 'pi pi-spin pi-spinner' : 'pi pi-check'"
                                severity="primary"
                                @click="toggleEditMode"
                                :disabled="isSavingRevision"
                                data-testid="save-edit-button"
                            />
                        </template>
                        <template v-else>
                            <!-- Menu Button (always shown) -->
                            <template v-if="selectedRevision.uiAccess?.edit">
                                <BravoButton
                                    icon="pi pi-ellipsis-h"
                                    severity="secondary"
                                    text
                                    @click="(event) => articleMenu.toggle(event)"
                                    class="ml-2 mr-3 menu-button"
                                    aria-label="Article actions"
                                    data-testid="article-menu-button"
                                    v-tooltip.bottom="{ value: 'Actions', showDelay: 400 }"
                                />
                            </template>
                            <!-- Draft Revision Buttons -->
                            <template v-if="selectedRevision?.status === ArticleStatus.DRAFT && !isEditing && can.publishArticle()">
                                <BravoButton
                                    data-testid="publish-article-button"
                                    :label="t('knowledge.publish')"
                                    icon="pi pi-check-circle"
                                    severity="success"
                                    class="mr-3"
                                    @click="publishArticle"
                                />
                            </template>

                            <template v-if="selectedRevision?.status === ArticleStatus.DRAFT && !isEditing && can.editArticle()">
                                <BravoButton
                                    data-testid="edit-article-button"
                                    :label="t('knowledge.edit')"
                                    icon="pi pi-pencil"
                                    severity="primary"
                                    @click="toggleEditMode"
                                />
                            </template>

                            <!-- Published Revision Buttons -->
                            <template v-if="selectedRevision?.status === ArticleStatus.PUBLISHED && !isEditing && can.editArticle()">
                                <BravoButton
                                    :label="t('knowledge.edit_as_draft')"
                                    icon="pi pi-pencil"
                                    severity="primary"
                                    @click="toggleEditMode"
                                    data-testid="edit-as-draft-button"
                                />
                            </template>

                            <!-- Previously Published Revision Buttons -->
                            <template v-if="selectedRevision?.status === ArticleStatus.PREVIOUSLY_PUBLISHED && !isEditing && can.editArticle()">
                                <BravoButton
                                    :label="t('knowledge.restore_as_draft')"
                                    icon="pi pi-check-circle"
                                    severity="primary"
                                    @click="restoreArticle"
                                    data-testid="restore-article-button"
                                />
                            </template>
                        </template>
                    </div>
                </div>
                <div class="article-content-scrollable" data-testid="article-content-scrollable">
                    <div class="article-content-container" data-testid="article-content-container">
                        <Popover ref="insertKbPopover" :target="insertKbTarget" dismissable position="right"
                            class="max-h-[280px] overflow-y-auto p-4">
                            <div class="flex flex-col space-y-4">
                                <div class="flex items-center" style="padding-left:15px">
                                <ToggleSwitch inputId="switch1" v-model="isEmbedded" class="mr-2" />
                                <label for="switch1" class="text-sm font-medium text-gray-900">Embedded</label>
                                </div>

                                <div class="w-full">
                                <Tree
                                    :value="nodes"
                                    :filter="true"
                                    filterMode="lenient"
                                    selectionMode="single"
                                    @node-expand="onNodeExpand"
                                    @node-select="onNodeSelect"
                                    :loading="loading2"
                                    class="w-full rounded-md" />
                                </div>
                            </div>
                        </Popover>
                        <div class="article-header" data-testid="article-header">
                            <FileManager
                                :visible="fileManager.showFileManager.value"
                                @update:visible="(val) => { fileManager.showFileManager.value = val }"
                                @fileUploadSelected="handleFileSelect"
                                @fileSelected="handleFileSelected"
                                @fileDelete="handleFileDelete"
                                @tabChanged="(tab) => {
                                    fileManager.isLoadingFiles.value = true;
                                    fileManager.fetchFilesForTab(tab);
                                }"
                                @pageChange="fileManager.handleFileManagerPageChange"
                                :items="fileManager.fileManagerItems.value"
                                :totalItems="fileManager.totalFileCount.value"
                                :currentPageProp="fileManager.currentPage.value"
                                :pageSize="12"
                                :isLoading="fileManager.isLoadingFiles.value"
                                :selectedTab="fileManager.activeTab.value"
                            />
                            <TokenPopup 
                                ref="tokenPopup"
                                :visible="showTokenPopup" 
                                :editor="editor" 
                                :tokens="tokenList"
                                :initialtokenId="initialToken"
                            />
                            <div class="title-section" data-testid="title-section">
                                <div class="title-field" data-testid="title-field">
                                    <BravoInputText
                                        v-if="isEditing"
                                        v-model="article.title"
                                        class="title-input"
                                        :placeholder="t('knowledge.add_title')"
                                        data-testid="title-input"
                                    />
                                    <BravoTitlePage v-else data-testid="article-title">{{
                                        article.title
                                    }}</BravoTitlePage>
                                </div>

                                <div class="subtitle-field" data-testid="subtitle-field">
                                    <BravoInputText
                                        v-if="isEditing"
                                        v-model="(article as any).sub_title"
                                        class="subtitle-input"
                                        placeholder="Add a subtitle..."
                                        data-testid="subtitle-input"
                                    />
                                    <h2 v-else data-testid="article-subtitle">
                                        {{ (article as any).sub_title || (article as any).c__sub_title || '' }}
                                    </h2>
                                </div>
                                <div class="article-meta" data-testid="article-meta">
                                    <span class="updated" data-testid="article-updated">
                                        {{ t('knowledge.updated') }}
                                        {{
                                            (article as any).updated
                                                ? formatRelativeTime((article as any).updated)
                                                : 'Recently'
                                        }}
                                    </span>
                                    <span
                                        v-if="articleVisibility"
                                        class="visibility ml-2"
                                        data-testid="article-visibility"
                                    >
                                        <i
                                            class="pi"
                                            :class="articleVisibility === 'Internal' ? 'pi-lock' : 'pi-globe'"
                                        ></i>
                                        {{ articleVisibility }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <ArticlePreview
                            v-if="!isEditing"
                            :content="article && (article as any).body_rendered || (article && (article as any).body) || article?.content"
                            :libraryCss="libraryCss"
                            :screensUrl="screensUrl"
                            :articleTheme="articleTheme"
                            data-testid="article-content"
                        />
                        <div v-else class="article-editor" data-testid="article-editor">
                            <froala
                                id="edit"
                                :tag="'textarea'"
                                :config="froalaConfig"
                                v-model:value="editedContent"
                                data-testid="article-editor-froala"
                                ref="froalaEditor"
                            ></froala>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Article Sidebar -->
            <ArticleSidebar
                :article="revision"
                :isLoading="false"
                :relatedArticles="relatedArticles"
                :loadingRelatedArticles="loadingRelatedArticles"
                :isPanelMinimized="isPanelMinimized"
                :isEditing="isEditing"
                @toggle-panel="togglePanel"
                @update="handleSidebarUpdate"
            />
            <Menu ref="articleMenu" :model="articleMenuItems" :popup="true" data-testid="article-menu" />
            <DeleteArticleModal
                v-if="can.deleteArticle()"
                ref="deleteArticleModal"
                :articleId="article.id"
                :articleTitle="article.title"
                @deleted="handleArticleDeleted"
            />
            <DiscardDraftModal
                v-if="can.editArticle()"
                ref="discardDraftModal"
                :draftId="selectedRevision?.code"
                :draftTitle="article?.title"
                @discarded="handleDraftDiscarded"
            />
        </div>
    </div>

    <div v-else class="article-not-found" data-testid="article-not-found">
        <p>Article not found. It may have been moved or deleted.</p>
        <BravoButton label="Go Back" icon="pi pi-arrow-left" @click="handleBack" data-testid="not-found-back-button" />
    </div>
</template>

<style scoped>
.article-view {
    height: 100vh;
    overflow: hidden;
}

.article-loading,
.article-error,
.article-not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    gap: 1rem;
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--border-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.top-nav {
    height: 64px;
    padding: 0 1.5rem;
    background-color: white;
    z-index: 10;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    min-height: 64px;
    max-height: 64px;
    flex-shrink: 0;
    overflow: hidden;
    gap: 1rem;
}

.top-nav-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.top-nav .back-button.p-button.p-component {
    flex-shrink: 0;
    z-index: 5;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--surface-600) !important;
}

.top-nav .back-button.p-button.p-component:hover {
    background-color: var(--surface-100) !important;
}

/* Target all possible icon elements */
.top-nav .back-button.p-button.p-component .p-button-icon,
.top-nav .back-button.p-button.p-component i,
.top-nav .back-button.p-button.p-component span,
.top-nav .back-button.p-button.p-component:deep(.p-button-icon),
.top-nav .back-button:deep(i),
.top-nav .back-button :deep(svg),
.top-nav .back-button :deep(path) {
    color: var(--surface-600) !important;
    fill: var(--surface-600) !important;
    stroke: var(--surface-600) !important;
}

.revision-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    overflow: hidden;
}

.viewing-label {
    color: var(--text-color-secondary);
}

.revision-dropdown {
    min-width: 240px;
}

.revision-dropdown :deep(.p-dropdown) {
    border-radius: 8px;
    border: 1px solid var(--surface-300);
}

.revision-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.revision-date {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    margin-left: 0.25rem;
}

.revision-date-small {
    color: var(--text-color-secondary);
    font-size: 0.75rem;
}

.revision-details {
    display: flex;
    flex-direction: column;
}

.ml-auto {
    margin-left: auto;
}

.mr-4 {
    margin-right: 1rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.edit-buttons {
    display: flex;
    align-items: center;
}

.edit-buttons .p-button {
    border-radius: 6px;
}

.menu-button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-button:hover {
    background-color: var(--surface-100) !important;
}

.article-meta {
    display: flex;
    align-items: center;
    color: var(--text-color-secondary);
    padding: 0 0 1rem 0;
}

.visibility {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.article-layout {
    display: flex;
    height: 100%;
    overflow: hidden;
    width: 100%;
}

.article-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
}

.article-content-scrollable {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    margin: 0;
    padding: 0;
}

.article-content-container {
    max-width: 1100px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem 4rem 2rem 4rem;
    overflow-x: hidden;
    height: 80vh;
}

.not-editing .article-content-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
}

.not-editing .article-header {
    max-width: 1100px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem 4rem 2rem 4rem;
    overflow-x: hidden;
}

.article-content {
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.article-main > *:not(.top-nav) {
    margin: 0 auto;
    padding: 2rem;
}

.not-editing .article-main > *:not(.top-nav) {
    margin: 0;
    padding: 2rem 0 0 2rem;
}

.title-field,
.subtitle-field {
    margin-bottom: 1rem;
}

.title-field h1 {
    margin: 0;
    font-size: 2rem;
    color: var(--text-color-primary);
    line-height: 1.5;
}

.subtitle-field h2 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--text-color-secondary);
    font-weight: normal;
}

.title-input {
    font-size: 2rem;
    font-weight: 600;
    width: 100%;
    margin: -0.3rem 0 -1rem -0.8rem;
    border-color: white;
}

.subtitle-input {
    font-size: 1.25rem;
    width: 100%;
    margin: -0.1rem 0 -1rem -0.8rem;
    border-color: white;
    color: var(--text-color-secondary);
}

.article-content :deep(pre) {
    margin: 0;
    padding: 1rem;
    background: #f8fafc;
    overflow-x: auto;
}

.article-content :deep(code) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 0.875rem;
}

.article-title-area {
    margin-bottom: 2rem;
}

.article-meta-info {
    margin-bottom: 2rem;
}

.meta-row {
    display: flex;
    gap: 3rem;
}

.article-container {
    margin-top: 5rem !important;
    width: 76% !important;
}

.is-editing .article-content-scrollable {
    margin-right: 0;
}

.is-editing .article-content-scrollable, .is-editing .article-content-container {
    padding-top: 0px;
}
.is-editing .article-content-container {
    overflow: initial;
}
.is-editing .article-header {
    margin-top: 4rem;
}

.is-editing .fr-box.fr-basic .fr-element {
    overflow: hidden;
}
</style>
