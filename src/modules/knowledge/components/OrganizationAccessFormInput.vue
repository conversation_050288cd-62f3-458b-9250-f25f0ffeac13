<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoForm<PERSON>ield from './BravoFormField.vue';
import { useMetaStore } from '@/stores/meta';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get meta store for organization data
const metaStore = useMetaStore();

// Define interface for organization data
interface Organization {
  val: string;
  id: string;
  lbl: string;
  avatar: string;
  url_avatar: string;
  defaultTeamId: string;
  rootKbs: any;
  canRelate: boolean;
  canSponsor: boolean;
  canProvider: boolean;
  canRoute: boolean;
  hasEcosystem: boolean;
  defaultPartner: boolean;
}

// Get organizations from the meta store
const organizations = computed<Organization[]>(() => {
  return metaStore.partnerMetaData?.pl__all_partners || [];
});

// Helper to get organization name from ID
const getOrgNameFromId = (orgId: string | number): string => {
  const org = organizations.value.find((o: Organization) => o.val === orgId || o.id === orgId);
  return org ? org.lbl : `Organization ${orgId}`;
};

// Compute the display values for the organizations
const organizationDisplayValues = computed(() => {
  const orgIds = props.article.partner_ids || [];
  if (orgIds.length === 0) {
    return 'No organization restrictions';
  }
  return orgIds.map((id: string | number) => getOrgNameFromId(id));
});

// Compute if there are any organization restrictions
const hasOrganizationRestrictions = computed(() => {
  return props.article.partner_ids && props.article.partner_ids.length > 0;
});

// Transform organizations for the dropdown - use val as the value
const transformedOrganizationOptions = computed(() => {
  return organizations.value.map(org => ({
    ...org,
    // Keep original structure but ensure we have the right mapping
    value: org.val,
    label: org.lbl
  }));
});

// Track the edited value locally
const editedOrganizations = ref<(string | number)[]>([]);

// Initialize the edited organizations when the article changes
watch(() => props.article.partner_ids, (newOrgIds) => {
  editedOrganizations.value = newOrgIds ? [...newOrgIds] : [];
}, { immediate: true });

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
  editedOrganizations.value = value;
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Check if the arrays are different
    const currentOrgIds = props.article.partner_ids || [];
    const isChanged = 
      value.length !== currentOrgIds.length ||
      value.some((id: string | number) => !currentOrgIds.includes(id)) ||
      currentOrgIds.some((id: string | number) => !value.includes(id));
      
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit('partner_ids', value);
      formInputRef.value?.handleSaveComplete(success);
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving organization access:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedOrganizations.value = props.article.partner_ids ? [...props.article.partner_ids] : [];
};

// Only show the component if organization access is relevant (based on visibility)
const showComponent = computed(() => {
  // Use the numeric visibility field if present, otherwise fallback to mapping
  const getVisibilityValueFromArticle = (article: any) => {
    if (article.c__d_visibility === 'Internal' || article.isPrivate) {
      return 1;
    } else if (article.c__d_visibility === 'Partner Ecosystem') {
      return 2;
    }
    return 0; // Public by default
  };

  const vis = typeof props.article.visibility === 'number' 
    ? props.article.visibility 
    : getVisibilityValueFromArticle(props.article);
  return vis === 2; // Only show for Partner Ecosystem
});
</script>

<template>
  <BravoFormField
    ref="formInputRef"
    v-if="showComponent"
    label="Organization Access"
    fieldName="organization-access"
    :value="editedOrganizations"
    :displayValue="organizationDisplayValues"
    inputType="multiselect"
    displayType="chips"
    :options="transformedOrganizationOptions"
    optionLabel="lbl"
    optionValue="val"
    :isLoading="isLoading || metaStore.isLoading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-building"
    noValueText="No organization restrictions"
    dataTestId="article-organization-access"
    showFilter
    filterPlaceholder="Search organizations..."
    enforceSubmitButton
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  >
    <!-- Custom option template with avatars -->
    <template #option="{ option }">
      <div class="org-option">
        <img
          :src="option.avatar"
          :alt="option.lbl"
          class="org-avatar"
        />
        <span>{{ option.lbl }}</span>
      </div>
    </template>
  </BravoFormField>
</template>

<style scoped>
.org-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
}

.org-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
</style> 