<!-- KnowledgeListZeroState.vue -->
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useKnowledgeStore } from '../stores/knowledge';
import { computed } from 'vue';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import contentZeroStateImage from '@/assets/content-zero-state.png';

const props = defineProps<{
  onAddArticle: () => void;
  selectedContentType: string;
}>();

const { t } = useI18n();
const store = useKnowledgeStore();

// Determine if a library is selected and get its name
const selectedLibraryName = computed(() => {
  // Check if we have a current node with title
  if (store.currentNode && store.currentNode.title) {
    return store.currentNode.title;
  }
  
  // If we have a currentRootId but no node info, use a generic term
  if (store.currentRootId) {
    return "selected";
  }
  
  return null;
});

// Add a computed property for the content type label (singular)
const contentTypeLabelSingular = computed(() => {
  if (props.selectedContentType === 'templates') {
    return t('knowledge.template') || 'Template';
  }
  return t('knowledge.article') || 'Article';
});

// Create the message with the library name if available
const zeroStateMessage = computed(() => {
  if (selectedLibraryName.value) {
    return `You haven't created any ${contentTypeLabelSingular.value}s in the ${selectedLibraryName.value} Library yet! Click the button below to start your first ${contentTypeLabelSingular.value}.`;
  }
  return `You haven't created any ${contentTypeLabelSingular.value}s yet. Click the button below to start your first ${contentTypeLabelSingular.value}.`;
});
</script>

<template>
  <BravoZeroStateScreen
    :title="`Create your first ${contentTypeLabelSingular}`"
    :message="zeroStateMessage"
    :buttonLabel="t('knowledge.new_article')"
    buttonIcon="pi pi-plus"
    :imageSrc="contentZeroStateImage"
    imageAlt="Content zero state"
    :actionHandler="props.onAddArticle"
  />
</template>

<style scoped>
</style> 