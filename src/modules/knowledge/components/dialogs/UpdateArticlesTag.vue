<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import { useMetaStore } from '@/stores/meta';
import { useToast } from 'primevue/usetoast';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';

const knowledgeAPI = useKnowledgeAPI();
const toast = useToast();
const isLoading = ref(false);
const isLoadingTags = ref(false);

const props = defineProps<{
    visible: boolean;
    selectedTags?: string[];
    selectedArticles?: any[];
}>();

const metaStore = useMetaStore();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'save', tags: string[]): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);
const selectedTags = ref<string[]>(props.selectedTags || []);
const tags = ref<any[]>([]);

// Watch for changes in props.visible
watch(
    () => props.visible,
    (newValue) => {
        isVisible.value = newValue;
        if (newValue) {
            isLoadingTags.value = true;
            try {
                /**
                 * certification - 30
                 * support - 3
                 * equipment - 4
                 * category - 5
                 * snooze_reason - 50
                 * escalation_reason - 51
                 */
                tags.value =
                    (metaStore.metaData?.pl__tags
                        .filter((tag: any) => tag.category === 3)
                        .map((tag: any) => ({
                            name: tag.lbl,
                            code: tag.val,
                        })) || []
                    );
                // Set selectedTags based on selection
                if (props.selectedArticles && props.selectedArticles.length === 1) {
                    // selectedTags.value = props.selectedArticles[0].bc__tags_support || [];
                } else {
                    selectedTags.value = [];
                }
            } finally {
                isLoadingTags.value = false;
            }
        } else {
            tags.value = [];
            selectedTags.value = [];
        }
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        emit('update:visible', newValue);
    }
);

const onHide = () => {
    isVisible.value = false;
    selectedTags.value = [];
};

const onSave = async () => {
    isLoading.value = true;
    const tagsIds = selectedTags.value.map((tag: any) => tag.code);
    const updatedArticles = props.selectedArticles?.map((article) => ({
        id: article.id,
        _bulkActionSave: true,
        bc__tags_support: tagsIds,
    }));

    if (!updatedArticles || updatedArticles.length === 0) {
        onHide();
        return;
    }

    try {
        const response = await knowledgeAPI.tagArticles(updatedArticles);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Articles tagged successfully',
                life: 3000,
            });
            emit('save', selectedTags.value);
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to tag articles',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error tagging articles:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to tag articles',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};
</script>

<template>
    <BravoDialog v-model:visible="isVisible" header="Tags" :modal="true" class="knowledge-dialog" @hide="onHide">
        <div class="dialog-content">
            <p class="description">
                The tags you submit will override all data in the Tags field for all selected articles. Submitting with a blank field will remove all Tags data from the selected articles.
            </p>
            <div class="form-field">
                <BravoMultiSelect
                    v-model="selectedTags"
                    :options="tags"
                    placeholder="Select tags"
                    optionLabel="name"
                    class="w-full"
                    :filter="true"
                    display="chip"
                    :loading="isLoadingTags"
                />
            </div>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('knowledge.actionsMenu.update')" :loading="isLoading" :disabled="isLoadingTags" @click="onSave" />
        </template>
    </BravoDialog>
</template>

<style>
@import './dialogs.css';
</style>
