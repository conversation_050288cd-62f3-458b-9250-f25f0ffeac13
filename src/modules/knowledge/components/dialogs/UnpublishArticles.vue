<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useToast } from 'primevue/usetoast';

// Get the knowledge API composable
const knowledgeAPI = useKnowledgeAPI();

const toast = useToast();
const isLoading = ref(false);

const props = defineProps<{
    visible: boolean;
    selectedArticlesCount?: number;
    selectedArticles?: any[];
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'unpublish'): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);

// Dialog header with article count
const dialogHeader = computed(() => {
    const count = props.selectedArticlesCount || 0;
    return `Unpublish ${count} ${count === 1 ? 'article' : 'articles'}`;
});

// Watch for changes in props.visible
watch(
    () => props.visible,
    (newValue) => {
        isVisible.value = newValue;
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        emit('update:visible', newValue);
    }
);

const onHide = () => {
    isVisible.value = false;
};

const onUnpublish = async () => {
    const updatedArticles = props.selectedArticles?.map((article) => ({
        id: article.id,
        _unpublish: true,
    }));

    if (!updatedArticles || updatedArticles.length === 0) {
        onHide();
        return;
    }

    isLoading.value = true;
    try {
        const response = await knowledgeAPI.unpublishArticles(updatedArticles);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Articles unpublished successfully',
                life: 3000,
            });
            emit('unpublish');
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to unpublish articles',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error unpublishing articles:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to unpublish articles',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};
</script>

<template>
    <BravoDialog
        v-model:visible="isVisible"
        :header="dialogHeader"
        :modal="true"
        class="knowledge-dialog"
        @hide="onHide"
    >
        <div class="dialog-content">
            <p class="description">Are you sure you want to unpublish the selected articles?</p>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('common.unpublish')" :loading="isLoading" @click="onUnpublish" />
        </template>
    </BravoDialog>
</template>
