<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import { useToast } from 'primevue/usetoast';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useMetaStore } from '@/stores/meta';

const knowledgeAPI = useKnowledgeAPI();
const metaStore = useMetaStore();
const toast = useToast();
const isLoadingTeams = ref(false);

const props = defineProps<{
    visible: boolean;
    selectedAccessLevel?: string;
    teamAccessTo?: string[];
    selectedArticles?: any[];
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'save', data: { accessLevel: string; teamAccessTo: string[] }): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);
const selectedAccessLevel = ref(props.selectedAccessLevel || '');
const teamAccessTo = ref(props.teamAccessTo || []);
const teams = ref<any[]>([]);

// Access level options
const accessLevelOptions = ref<any[]>([]);

// Watch for changes in props.visible
watch(
    () => props.visible,
    async (newValue) => {
        isVisible.value = newValue;

        if (newValue) {
            isLoadingTeams.value = true;
            try {
                const response = await knowledgeAPI.loadPartnersTeams({ sAction: 'metaPartnersTeams' });
                teams.value = response.pl__org_partners_teams || [];
            } finally {
                isLoadingTeams.value = false;
            }
        }
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        if (metaStore.metaData?.pl__kb_visibility) {
            accessLevelOptions.value = metaStore.metaData?.pl__kb_visibility.map((item: any) => ({
                label: item.lbl,
                value: item.val,
            })) || [];
        }
        emit('update:visible', newValue);
    }
);

const onHide = () => {
    isVisible.value = false;
};

const onSave = async () => {
    isLoadingTeams.value = true;
    try {
        // Build the payload for all selected articles
        const updatedArticles = props.selectedArticles?.map((article) => ({
            _bulkActionSave: true,
            internal_team_ids: teamAccessTo.value,
            id: article.id,
            visibility: selectedAccessLevel.value,
        }));

        if (!updatedArticles || updatedArticles.length === 0) {
            onHide();
            return;
        }

        const response = await knowledgeAPI.updateProducts(updatedArticles);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Access controls updated successfully',
                life: 3000,
            });
            emit('save', {
                accessLevel: selectedAccessLevel.value,
                teamAccessTo: teamAccessTo.value,
            });
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to update access controls',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error updating access controls:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update access controls',
            life: 3000,
        });
    } finally {
        isLoadingTeams.value = false;
    }
};
</script>

<template>
    <BravoDialog
        v-model:visible="isVisible"
        :header="t('knowledge.actionsMenu.update_access_controls')"
        :modal="true"
        class="knowledge-dialog"
        @hide="onHide"
    >
        <div class="dialog-content">
            <div class="form-section">
                <div class="form-field">
                    <label>
                        Access Level
                        <span class="required">*</span>
                    </label>
                    <BravoSelect
                        id="access-level-select"
                        v-model="selectedAccessLevel"
                        :options="accessLevelOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select access level"
                        class="w-full"
                        dataTestId="access-level-select"
                    />
                </div>
                <div v-if="selectedAccessLevel === '1'" class="form-field">
                    <label>Restrict Team access to</label>
                    <BravoMultiSelect
                        v-model="teamAccessTo"
                        :options="teams"
                        optionLabel="lbl"
                        optionValue="val"
                        display="chip"
                        placeholder="Select Teams"
                        class="w-full"
                        :loading="isLoadingTeams"
                    />
                    <p class="text-sm text-gray-500">By adding Teams here, access is restricted to those teams. If left blank, all teams will have access.</p>
                </div>
            </div>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('knowledge.actionsMenu.update')" :loading="isLoadingTeams" :disabled="isLoadingTeams" @click="onSave" />
        </template>
    </BravoDialog>
</template>

<style>
@import './dialogs.css';
</style>
