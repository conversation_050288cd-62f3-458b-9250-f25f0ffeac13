<!-- DiscardDraftModal.vue -->
<script setup lang="ts">
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useKnowledgeStore } from '../../stores/knowledge';
import { stripHtmlAndDecodeEntities } from '../../utils/helpers';

const { t } = useI18n();

const props = defineProps<{
    draftId?: string;
    draftTitle?: string;
}>();

const emit = defineEmits<{
    (e: 'discarded', draftId: string): void;
    (e: 'error', error: any): void;
}>();

const store = useKnowledgeStore();
const toast = useToast();
const confirm = useConfirm();
const isDiscarding = ref(false);

// Method to show the discard confirmation dialog
const showConfirmation = () => {
    if (!props.draftId || !props.draftTitle) {
        console.error('Missing draft ID or title for discard operation');
        return;
    }

    // Clean the draft title if it contains HTML
    const cleanTitle = stripHtmlAndDecodeEntities(props.draftTitle);

    confirm.require({
        message: t('knowledge.discard_draft_confirmation_message', { title: cleanTitle }),
        header: t('knowledge.discard_draft_confirmation_title', { defaultValue: 'Discard draft' }),
        icon: 'pi pi-exclamation-triangle',
        acceptClass: 'p-button-danger',
        rejectClass: 'p-button-secondary',
        accept: () => discardDraft(),
        reject: () => {
            // User rejected the confirmation, do nothing
        },
    });
};

// Method to handle draft discarding
const discardDraft = async () => {
    if (!props.draftId) return;

    isDiscarding.value = true;

    try {
        const response = await store.deleteDraft(props.draftId);
        console.log('Discard response:', response);

        // Show success toast
        toast.add({
            severity: 'success',
            summary: t('knowledge.discard_draft_success_title', { defaultValue: 'Draft discarded' }),
            detail: t('knowledge.discard_draft_success_message', { title: props.draftTitle, defaultValue: 'The draft was successfully discarded.' }),
            life: 5000,
        });

        // Emit discarded event with draft ID
        emit('discarded', props.draftId);
    } catch (error: any) {
        console.error('Error discarding draft:', error);

        // Show error toast
        toast.add({
            severity: 'error',
            summary: t('knowledge.discard_draft_error_title', { defaultValue: 'Error discarding draft' }),
            detail: t('knowledge.discard_draft_error_message', { error: error.message || 'Unknown error', defaultValue: 'Failed to discard draft: {error}' }),
            life: 5000,
        });

        // Emit error event
        emit('error', error);
    } finally {
        isDiscarding.value = false;
    }
};

// Method to expose for external components to call
defineExpose({
    showConfirmation,
});
</script>

<template>
    <!-- Empty component - dialog is managed by the parent's BravoConfirmDialog -->
    <div class="discard-draft-modal-container"></div>
</template> 