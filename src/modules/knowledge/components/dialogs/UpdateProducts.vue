<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import { useToast } from 'primevue/usetoast';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';

const knowledgeAPI = useKnowledgeAPI();
const toast = useToast();
const isLoading = ref(false);

const props = defineProps<{
    visible: boolean;
    selectedProducts?: string[];
    selectedArticles?: any[];
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'save', products: string[]): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);
const selectedProducts = ref<string[]>(props.selectedProducts || []);
const products = ref<any[]>([]);
const searchQuery = ref('');

const loadProducts = async (ownerPartnerId: string) => {
    try {
        isLoading.value = true;
        const query = [
            { property: 'current_dict_id', value: [], operator: 'in_set' },
            { property: 'owner_partner_id', value: ownerPartnerId },
        ];

        const response = (await knowledgeAPI.loadProducts(query))?.partners_product_list?.results;
        if (response && Array.isArray(response)) {
            products.value = response.map((item: any) => ({
                name: item.c__lbl,
                code: item.dict_id,
            }));
        }
    } catch (error) {
        console.error('Error loading products:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load products',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};

// Watch for changes in props.visible
watch(
    () => props.visible,
    (newValue) => {
        isVisible.value = newValue;
        if (newValue && props.selectedArticles?.length) {
            const ownerPartnerId = props.selectedArticles[0].owner_partner_id;

            if (ownerPartnerId) {
                loadProducts(ownerPartnerId);
            }
        } else {
            products.value = [];
        }
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        emit('update:visible', newValue);
    }
);

const onHide = () => {
    isVisible.value = false;
};

const onSave = async () => {
    const updatedArticles = props.selectedArticles?.map((article) => ({
        id: article.id,
        _bulkActionSave: true,
        bc__tags_object_members_devices_dict: selectedProducts.value?.map((product: any) => product.code),
    }));

    if (!updatedArticles || updatedArticles.length === 0) {
        onHide();
        return;
    }

    isLoading.value = true;
    try {
        const response = await knowledgeAPI.updateProducts(updatedArticles);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Products updated successfully',
                life: 3000,
            });
            emit('save', selectedProducts.value);
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to update products',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error updating products:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update products',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};

// Handle search input
const handleSearchInput = (event: Event) => {
    // TODO: Implement product search logic
    console.log('Searching for:', searchQuery.value);
};
</script>

<template>
    <BravoDialog v-model:visible="isVisible" header="Products" :modal="true" class="knowledge-dialog" @hide="onHide">
        <div class="dialog-content">
            <p class="description">
            The products you submit will override all data in the Products field for all selected articles. Submitting with a blank field will remove all products data from the selected articles.
            </p>
            <div class="products-section">
                <BravoMultiSelect
                    v-model="selectedProducts"
                    :options="products"
                    optionLabel="name"
                    placeholder="Select products"
                    class="w-full"
                    :filter="true"
                    :filterPlaceholder="'Search products'"
                    display="chip"
                    :loading="isLoading"
                />
            </div>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('knowledge.actionsMenu.update')" :loading="isLoading" @click="onSave" />
        </template>
    </BravoDialog>
</template>

<style scoped>
.update-products-dialog {
    min-width: 400px;
}

.dialog-content {
    padding: 1rem 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.search-section {
    width: 100%;
}

.products-section {
    width: 100%;
}

:deep(.w-full) {
    width: 100%;
}

:deep(.p-multiselect-token) {
    background: var(--primary-color);
    color: var(--primary-color-text);
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    margin: 0.25rem;
}
</style>
