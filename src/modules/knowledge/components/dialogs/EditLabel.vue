<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoTreeSelect from '@services/ui-component-library/components/BravoTreeSelect.vue';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useUserStore } from '@/stores/user';
import { useToast } from 'primevue/usetoast';
import { fetchClientInstanceId } from '@/utils/clientInstance';

const toast = useToast();
const { t } = useI18n();
const userStore = useUserStore();

const props = defineProps<{
  visible: boolean;
  node: any;
  nodes: any[];
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh'): void;
}>();

const editLabelName = ref(props.node?.label || '');
const selectedNode = ref<{ [key: string]: boolean }>({});
const editIcon = ref('folder');
const editIconUrl = ref('');
const editAltText = ref('');
const fileInput = ref<HTMLInputElement | null>(null);
const isLoading = ref(false);
const treeData = ref<any[]>([]);
const expandedKeys = ref<Record<string, boolean>>({});
const isLoadingTree = ref(false);

// Create a knowledgeAPI instance
const knowledgeAPI = useKnowledgeAPI();

interface LibraryResponse {
  success: boolean;
  pl__kb_libraries: Array<{
    id: string;
    val: string;
    lbl: string;
    sub_title: string | null;
    short_name: string;
    description: string | null;
    owner_partner_id: string;
    url: string;
  }>;
  current_server_time: string;
}

interface LabelResponse {
  success: boolean;
  pl__kb_labels: Array<{
    id: string;
    val: string;
    lbl: string;
    path: string;
    actual_path: string;
    indent_lbl: string;
    root_kb_id: string;
    iconCls: string;
    icon: string | null;
    icon_url: string;
    icon_alt_text: string | null;
    icon_visible: boolean;
    url: string;
  }>;
  current_server_time: string;
}

// Watch for dialog visibility to load labels
watch(() => props.visible, (newVal) => {
  if (newVal) {
    setDialogComponentValues();
    loadLabels();
  }
  else {
    treeData.value = [];
    editLabelName.value = '';
    selectedNode.value = {};
    expandedKeys.value = {};
  }
});

const setDialogComponentValues = () => {
  const newNode = props.node;

  editLabelName.value = newNode.label || '';
  editIcon.value = newNode.icon || 'folder';
  editIconUrl.value = newNode.data?.icon_url || '';
  editAltText.value = newNode.data?.icon_alt_text || '';
}

const loadLabels = async () => {
  if (!props.node) return;

  try {
    isLoadingTree.value = true;
    // Call the libraries API
    let query = {
      sAction: 'metaKBLibraries',
      query: JSON.stringify([{"property":"current_library_id","value":"_no_filter_"},""]),
    };
    const librariesResponse = await knowledgeAPI.loadLabels(query) as LibraryResponse;

    query = {
      sAction: 'metaKBLabels',
      query: JSON.stringify([{
        property: 'root_kb_id',
        value: props.node.data.rootParentId
      },{
        property: 'excludeId',
        value: props.node.data.id,
      },{
        property: 'excludeChildrenId',
        value: true,
      }]),
    };

    const labelsResponse = await knowledgeAPI.loadLabels(query) as LabelResponse;
    const parsedTreeData = createLibraryTree(librariesResponse, labelsResponse);

    const data = [];
    for (const node of parsedTreeData) {
      if (node.children && node.children.length) {
        data.push(...node.children);
      }
    }

    cleanChildren(data);
    treeData.value = data;

    // Set all nodes as expanded
    const expandedNodes: Record<string, boolean> = {};
    const setExpandedKeys = (nodes: any[]) => {
      nodes.forEach(node => {
        expandedNodes[node.key] = true;
        if (node.children) {
          setExpandedKeys(node.children);
        }
      });
    };
    setExpandedKeys(data);
    expandedKeys.value = expandedNodes;

    // Set the selected node after tree data is loaded
    if (props.node?.data?.parentId && props.node?.data?.parentId !== props.node?.data?.root_kb_id) {
      selectedNode.value = { [props.node.data.parentId]: true };
    }

  } catch (error) {
    console.error('Error loading labels:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load labels',
      life: 3000,
    });
  } finally {
    isLoadingTree.value = false;
  }
};

function createLibraryTree(librariesData: LibraryResponse, labelsData: LabelResponse) {
  const libraries = librariesData?.pl__kb_libraries || [];
  const labels = labelsData?.pl__kb_labels || [];

  // Group labels by their root_kb_id
  const labelGroups: Record<string, any[]> = {};
  for (const label of labels) {
    const libraryId = label.root_kb_id;
    if (!labelGroups[libraryId]) {
      labelGroups[libraryId] = [];
    }
    labelGroups[libraryId].push(label);
  }

  function buildLabelTree(flatLabels: any[], rootParentId: string) {
    const root: any[] = [];

    for (const label of flatLabels) {
      const parts = label.path.split('/');
      let currentLevel = root;
      let fullPath = '';
      let parentId = rootParentId;

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        fullPath = fullPath ? `${fullPath}/${part}` : part;

        let existing = currentLevel.find(item => item.label === part);
        if (!existing) {
          const isLeaf = i === parts.length - 1;
          const labelData = isLeaf ? label : { id: crypto.randomUUID() }; // Dummy ID for intermediate nodes

          existing = {
            key: labelData.id,
            label: part,
            data: {
              id: labelData.id,
              parentId,
              rootParentId,
              ...(label.root_kb_id ? { root_kb_id: label.root_kb_id } : {})
            },
            leaf: isLeaf && (label.article_cnt === 0),
            icon: "pi pi-tag",
            children: [],
            expanded: true
          };

          currentLevel.push(existing);
        }

        parentId = existing.key;
        currentLevel = existing.children;
      }
    }
    
    return root;
  }

  const tree = libraries.map((library: LibraryResponse['pl__kb_libraries'][0]) => {
    const labelTree = buildLabelTree(labelGroups[library.id] || [], library.id);

    const data = {
      key: library.id,
      label: library.lbl,
      data: {
        id: library.id,
        parentId: null,
        rootParentId: library.id,
        isLibrary: true,
      },
      leaf: false,
      icon: "pi pi-book",
      children: labelTree.length ? labelTree : null,
      expanded: true
    };

    return data;
  });

  return tree;
}

const cleanChildren = (data: any) => {
  for (const node of data) {
    if (node.children && node.children.length) {
      cleanChildren(node.children);
    } else {
      delete node.children;
      delete node.icon;
      node.leaf = true;
    }
  }
  return data;
};

const handleSaveEdit = async () => {
  if (!editLabelName.value) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Label name is required',
      life: 3000,
    });
    return;
  }

  isLoading.value = true;
  try {
    const newParentId = Object.keys(selectedNode.value)[0] || null;
    const isMovingToDifferentParent = newParentId !== props.node.data.parentId;

    // Find the new root parent ID if moving to a different parent
    let newRootParentId = props.node.data.rootParentId;
    if (isMovingToDifferentParent && newParentId) {
      // Find the new parent node in the tree
      const findParentNode = (nodes: any[]): any => {
        for (const node of nodes) {
          if (node.key === newParentId) {
            return node;
          }
          if (node.children) {
            const found = findParentNode(node.children);
            if (found) return found;
          }
        }
        return null;
      };

      const newParentNode = findParentNode(treeData.value);
      if (newParentNode) {
        newRootParentId = newParentNode.data.rootParentId;
      }
    }

    const response = await knowledgeAPI.editLabel([{
      title: editLabelName.value,
      id: props.node.data.id,
      parent_id: newParentId,
      root_kb_id: newRootParentId
    }]);
    
    if (response.success) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Label updated successfully',
        life: 3000,
      });
      emit('update:visible', false);
      // If moved to a different parent, refresh both old and new parent nodes
      if (isMovingToDifferentParent) {
        emit('refresh', props.node.data.parentId);
        emit('refresh', newParentId);
      } else {
        emit('refresh', props.node.data.parentId);
      }
    } else {
      throw new Error(response.message || 'Failed to update label');
    }
  } catch (error) {
    console.error('Error editing label:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error instanceof Error ? error.message : 'Failed to update label',
      life: 3000,
    });
  } finally {
    isLoading.value = false;
  }
};

const handleRemoveIcon = async () => {
  try {
    const response = await knowledgeAPI.editLabel([{
      id: props.node.data.id,
      icon: null,
      icon_alt_text: null,
      icon_visible: false
    }]);

    if (response.success) {
      editIconUrl.value = '';
      editIcon.value = 'folder';
      editAltText.value = '';
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Icon removed successfully',
        life: 3000,
      });
      emit('refresh');
    } else {
      throw new Error(response.message || 'Failed to remove icon');
    }
  } catch (error) {
    console.error('Error removing icon:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error instanceof Error ? error.message : 'Failed to remove icon',
      life: 3000,
    });
  }
};

const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files?.length) return;
  
  const file = input.files[0];
  const formData = new FormData();

  formData.append('fileField', file);
  formData.append('object', 'kb_labels');
  formData.append('object_id', props.node.data.id);
  formData.append('file_tag', 'icon');
  formData.append('_csrf_token', userStore.csrfToken);

  try {
    const response = await fetch(`admin/v4/files/?sAction=putFile`, {
      method: 'POST',
      body: formData,
      credentials: 'include',
      headers: {
        'x-boomtown-client-instance-id': fetchClientInstanceId(),
        _csrf_token: userStore.csrfToken,
        'x-request-id': crypto.randomUUID(),
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    if (result.success) {
      editIconUrl.value = result.links[0];
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Icon uploaded successfully',
        life: 3000,
      });
      emit('refresh');
    } else {
      throw new Error(result.message || 'Failed to upload icon');
    }
  } catch (error) {
    console.error('Error uploading icon:', error);
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error instanceof Error ? error.message : 'Failed to upload icon',
      life: 3000,
    });
  }
};

const handleIconChange = () => {
  fileInput.value?.click();
};
</script>

<template>
  <BravoDialog 
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    modal 
    :header="t('knowledge.edit_label.title')"
    :style="{ width: '450px' }"
    :closable="true"
    class="knowledge-dialog"
  >
    <div class="edit-label-form">
      <div class="form-field">
        <label for="labelName">{{ t('knowledge.edit_label.label_name') }} <span class="required">*</span></label>
        <BravoInputText 
          id="labelName"
          v-model="editLabelName" 
          :placeholder="t('knowledge.edit_label.label_name')"
          class="w-full"
        />
      </div>
      
      <div class="form-field">
        <label for="nestUnder">{{ t('knowledge.edit_label.nest_under') }}</label>
        <BravoTreeSelect
          id="nestUnder"
          v-model="selectedNode"
          :options="treeData"
          optionLabel="label"
          :placeholder="isLoadingTree ? t('common.loading') : t('knowledge.edit_label.nest_under')"
          class="w-full"
          dataTestId="edit-label-nest-under"
          :expandedKeys="expandedKeys"
          :loading="isLoadingTree"
          :disabled="isLoadingTree"
        >
          <template #loadingicon>
            <i class="pi pi-spin pi-spinner" style="font-size: 1rem"></i>
          </template>
        </BravoTreeSelect>
      </div>
      
      <div class="form-field">
        <label>{{ t('knowledge.edit_label.icon') }}</label>
        <div class="icon-preview">
          <img v-if="editIconUrl" :src="editIconUrl" :alt="editAltText" class="icon-image" />
          <i v-else :class="['pi', `pi-${editIcon}`]"></i>
        </div>
        <div class="icon-actions">
          <a href="#" @click.prevent="handleIconChange">{{ t('knowledge.edit_label.change_icon') }}</a>
          <a href="#" @click.prevent="handleRemoveIcon">{{ t('knowledge.edit_label.remove_icon') }}</a>
        </div>
        <input 
          type="file" 
          ref="fileInput" 
          style="display: none" 
          accept="image/*"
          @change="handleFileUpload"
        />
      </div>
      
      <div class="form-field">
        <label for="altText">{{ t('knowledge.edit_label.alt_text') }}</label>
        <BravoInputText
          id="altText"
          v-model="editAltText"
          :placeholder="t('knowledge.edit_label.alt_text_placeholder')"
          class="w-full"
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <BravoButton 
          class="p-button p-button-text" 
          @click="$emit('update:visible', false)"
          :label="t('common.cancel')"
          severity="secondary"
        />
        <BravoButton 
          class="p-button p-button-primary" 
          @click="handleSaveEdit"
          :loading="isLoading"
          :disabled="!editLabelName"
          :label="t('common.save')"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
.edit-label-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 500;
  color: var(--text-color);
}

.required {
  color: var(--red-500);
}

.icon-preview {
  width: 48px;
  height: 48px;
  border: 1px solid var(--surface-200);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.icon-preview .icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.icon-preview i {
  font-size: 1.5rem;
  color: var(--text-color-secondary);
}

.icon-actions {
  display: flex;
  gap: 1rem;
}

.icon-actions a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
}

.icon-actions a:hover {
  text-decoration: underline;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding-top: 1rem;
}
</style> 