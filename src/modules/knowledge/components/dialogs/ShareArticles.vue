<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useToast } from 'primevue/usetoast';

const knowledgeAPI = useKnowledgeAPI();

const toast = useToast();
const isLoading = ref(false);
const isLoadingMetadata = ref(false);

const props = defineProps<{
    visible: boolean;
    selectedOrganizations?: object[];
    selectedArticlesCount?: number;
    selectedArticles?: any[];
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'share', organizations: string[]): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);
const selectedOrganizations = ref<object[]>(props.selectedOrganizations || []);
const partnerMetadata = ref<any[]>([]);
// Dialog header with article count
const dialogHeader = computed(() => {
    const count = props.selectedArticlesCount || 0;
    return `Share ${count} ${count === 1 ? 'article' : 'articles'}`;
});

// Watch for changes in props.visible
watch(
    () => props.visible,
    (newValue) => {
        isVisible.value = newValue;
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        emit('update:visible', newValue);
        if (newValue) {
            // Check if all selected articles are public or partners ecosystem
            const invalidArticles = props.selectedArticles?.filter(
                (article: any) => !['Public', 'Partner Ecosystem'].includes(article.c__d_visibility)
            );
            if (invalidArticles && invalidArticles.length > 0) {
                toast.add({
                    severity: 'warn',
                    summary: 'Cannot Share',
                    detail: 'Only Public and Partner Ecosystem articles can be shared.',
                    life: 6000,
                });
                isVisible.value = false;
                return;
            }
            loadPartnerMetadata();
        }
    }
);

watch(
    [() => props.visible, partnerMetadata],
    ([visible, partners]) => {
        if (visible && props.selectedArticles && props.selectedArticles.length === 1 && partners.length > 0) {
            const partnerIds = props.selectedArticles[0].partner_ids || [];
            // Find the organization objects that match the partner IDs
            selectedOrganizations.value = partners.filter(org => partnerIds.includes(org.code));
        }
    },
    { immediate: true }
);

const onHide = () => {
    isVisible.value = false;
    selectedOrganizations.value = [];
};

const onShare = async () => {
    isLoading.value = true;
    const selectedOrgCodes = selectedOrganizations.value.map((organization: any) => organization.code);
    let updatedArticles;

    if (props.selectedArticles && props.selectedArticles.length > 1) {
        // For each article, merge its partner_ids with the selected org codes
        updatedArticles = props.selectedArticles.map((article: any) => {
            const currentPartnerIds = Array.isArray(article.partner_ids) ? [...article.partner_ids] : [];
            selectedOrgCodes.forEach(code => {
                if (!currentPartnerIds.includes(code)) {
                    currentPartnerIds.push(code);
                }
            });
            return {
                id: article.id,
                partner_ids: currentPartnerIds,
                _bulkActionSave: true,
            };
        });
    } else {
        // For a single article, use only the selected organizations
        updatedArticles = props.selectedArticles?.map((article: any) => ({
            id: article.id,
            partner_ids: selectedOrgCodes,
            _bulkActionSave: true,
        }));
    }

    try {
        const response = await knowledgeAPI.shareArticles(updatedArticles);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Articles shared successfully',
                life: 3000,
            });
            emit('share', selectedOrgCodes);
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to share articles',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error sharing articles:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to share articles',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};

const loadPartnerMetadata = async () => {
    isLoadingMetadata.value = true;
    try {
        const response = await knowledgeAPI.getPartnerMetadata();
        partnerMetadata.value =
            response.pl__all_partners.map((partner: { lbl: string; val: string }) => ({
                name: partner.lbl,
                code: partner.val,
            })) || [];
    } finally {
        isLoadingMetadata.value = false;
    }
};
</script>

<template>
    <BravoDialog
        v-model:visible="isVisible"
        :header="dialogHeader"
        :modal="true"
        class="knowledge-dialog"
        @hide="onHide"
    >
        <div class="dialog-content">
            <p class="description">
                Share the selected articles with the following organizations. This will not share internal articles.
            </p>
            <div class="organizations-section">
                <BravoMultiSelect
                    v-model="selectedOrganizations"
                    :options="partnerMetadata"
                    optionLabel="name"
                    display="chip"
                    placeholder="Select organizations"
                    class="w-full"
                    :filter="true"
                    :loading="isLoadingMetadata"
                />
            </div>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('knowledge.actionsMenu.share')" :loading="isLoading" :disabled="isLoadingMetadata" @click="onShare" />
        </template>
    </BravoDialog>
</template>
