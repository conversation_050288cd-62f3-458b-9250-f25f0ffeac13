<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { useKnowledgeAPI } from '@/composables/services';
import { useToast } from 'primevue/usetoast';

// Get the knowledge API composable
const knowledgeAPI = useKnowledgeAPI();

const toast = useToast();
const isLoading = ref(false);

const props = defineProps<{
    visible: boolean;
    selectedArticlesCount?: number;
    selectedArticles?: any[];
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'archive'): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);

// Dialog header with article count
const dialogHeader = computed(() => {
    const count = props.selectedArticlesCount || 0;
    return `Archive ${count} ${count === 1 ? 'Article' : 'Articles'}`;
});

// Watch for changes in props.visible
watch(
    () => props.visible,
    (newValue) => {
        isVisible.value = newValue;
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        emit('update:visible', newValue);
    }
);

const onHide = () => {
    isVisible.value = false;
};

const onArchive = async () => {
    const updatedArticles = props.selectedArticles?.map((article) => ({
        id: article.id,
        status: 98,
    }));

    if (!updatedArticles || updatedArticles.length === 0) {
        onHide();
        return;
    }

    isLoading.value = true;
    try {
        const response = await knowledgeAPI.archiveArticles(updatedArticles);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Articles archived successfully',
                life: 3000,
            });
            emit('archive');
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to archive articles',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error archiving articles:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to archive articles',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};
</script>

<template>
    <BravoDialog
        v-model:visible="isVisible"
        :header="dialogHeader"
        :modal="true"
        class="knowledge-dialog"
        @hide="onHide"
    >
        <div class="dialog-content">
            <p class="description">Are you sure you want to archive the selected articles?</p>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('common.archive')" :loading="isLoading" @click="onArchive" />
        </template>
    </BravoDialog>
</template>
