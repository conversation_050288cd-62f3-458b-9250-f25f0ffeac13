<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoForm<PERSON>ield from './BravoFormField.vue';
import { useMetaStore } from '@/stores/meta';
import { useToast } from 'primevue/usetoast';

// Define interface for user data from API
interface UserData {
    val: string;
    id: string;
    lbl: string;
    email: string;
    partners_id: string;
}

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get meta store for user data
const metaStore = useMetaStore();
const toast = useToast();

// Track the edited value locally
const editedOwner = ref('');

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Initialize the edited owner when the article changes
watch(() => props.article.c__revision_owner_id, (newOwnerId, oldOwnerId) => {
  console.log('OwnerFormInput: Article owner ID changed from', oldOwnerId, 'to', newOwnerId);
  console.log('OwnerFormInput: Current editedOwner value:', editedOwner.value);
  
  const newValue = newOwnerId || '';
  console.log('OwnerFormInput: Updating editedOwner from', editedOwner.value, 'to', newValue);
  editedOwner.value = newValue;
}, { immediate: true });

// Computed property for filtered users based on article's partner_id
const filteredUsers = computed(() => {
    if (!metaStore.partnerMetaData?.pl__partners_users) return [];

    console.log('OwnerFormInput: Partner users data:', metaStore.partnerMetaData.pl__partners_users);
    return metaStore.partnerMetaData.pl__partners_users.filter(
        (user: UserData) => user.partners_id === props.article.owner_partner_id
    );
});

// Computed to get current owner ID
const currentOwnerId = computed(() => {
    console.log('OwnerFormInput: Article data for owner lookup:', {
        c__revision_owner_id: props.article.c__revision_owner_id,
        _activeRevisionId: props.article._activeRevisionId,
        owner_id: props.article.owner_id,
        revision_id: props.article.revision_id,
        all_keys: Object.keys(props.article),
    });

    // Check for owner ID in different possible locations
    return props.article.c__revision_owner_id || props.article._activeRevisionId || props.article.owner_id;
});

// Get owner display name
const ownerDisplayName = computed(() => {
    const ownerId = currentOwnerId.value;
    const owner = filteredUsers.value.find((u: UserData) => u.id === ownerId);
    return owner ? owner.lbl : 'No owner';
});

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
    console.log('OwnerFormInput: handleUpdate called with value:', value);
    console.log('OwnerFormInput: Current props.article.c__revision_owner_id:', props.article.c__revision_owner_id);
    console.log('OwnerFormInput: Current editedOwner.value:', editedOwner.value);
    
    // No special handling needed for owner - just pass through
};

// Handle submission start
const handleSubmitStart = () => {
    // Set loading state when submission starts
    isLocalSaving.value = true;
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
    if (value !== props.article.c__revision_owner_id) {
        isLocalSaving.value = true;
        try {
            if (props.onSubmit) {
                const success = await props.onSubmit('c__revision_owner_id', value);
                if (success) {
                    // Update the edited value to reflect the change
                    editedOwner.value = value;
                    
                    // Update the article object and emit events
                    const updatedArticle = { ...props.article, c__revision_owner_id: value };
                    emit('update', updatedArticle);
                    

                    
                    // Complete the form field save
                    formInputRef.value?.handleSaveComplete(true);
                } else {
                    formInputRef.value?.handleSaveComplete(false);
                }
            } else {
                formInputRef.value?.handleSaveComplete(false);
            }
        } catch (error: any) {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: error?.message || 'Failed to update owner',
                life: 3000,
            });
            formInputRef.value?.handleSaveComplete(false);
        } finally {
            isLocalSaving.value = false;
        }
    } else {
        // No change, just complete successfully
        formInputRef.value?.handleSaveComplete(true);
    }
};

// Handle cancel event
const handleCancel = () => {
    // Reset the edited value back to the original
    editedOwner.value = props.article.c__revision_owner_id || '';
};
</script>

<template>
  <BravoFormField
    ref="formInputRef"
    label="Owner"
    fieldName="owner"
    :value="editedOwner"
    :displayValue="ownerDisplayName"
    inputType="dropdown"
    displayType="text"
    :options="filteredUsers"
    optionLabel="lbl"
    optionValue="id"
    :isLoading="isLoading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-user"
    noValueText="No owner"
    dataTestId="article-owner"
    :showFilter="true"
    filterPlaceholder="Search users"
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  />
</template> 