<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';
import { useKnowledgeAPI } from '@/composables/services';
import { useKnowledgeStore } from '../stores/knowledge';
import { useI18n } from 'vue-i18n';
import BravoTreeSelect from '@services/ui-component-library/components/BravoTreeSelect.vue';

interface LabelResponse {
  success: boolean;
  pl__kb_labels: Array<{
    id: string;
    val: string;
    lbl: string;
    path: string;
    actual_path: string;
    indent_lbl: string;
    root_kb_id: string;
    iconCls: string;
    icon: string | null;
    icon_url: string;
    icon_alt_text: string | null;
    icon_visible: boolean;
    url: string;
  }>;
  current_server_time: string;
}

interface TreeNode {
  key: string;
  label: string;
  data: {
    id: string;
    path: string;
    rootParentId: string;
    root_kb_id: string;
    icon: string | null;
    icon_url: string;
    icon_alt_text: string | null;
    icon_visible: boolean;
    isTopLevel?: boolean;
  };
  leaf: boolean;
  children?: TreeNode[];
  selectable?: boolean;
}

interface Props {
  modelValue: string[];
  libraryId: string;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  class?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Select labels',
  disabled: false,
  loading: false,
  class: 'w-full'
});

const emit = defineEmits<Emits>();

const { t } = useI18n();
const knowledgeAPI = useKnowledgeAPI();
const knowledgeStore = useKnowledgeStore();

const loadingLabels = ref(false);
const treeData = ref<TreeNode[]>([]);
const expandedKeys = ref<Record<string, boolean>>({});
const selectedKeys = ref<Record<string, boolean>>({});

// Convert array of label IDs to TreeSelect format
const convertToTreeSelectFormat = (labelIds: string[]): Record<string, boolean> => {
  const result: Record<string, boolean> = {};
  labelIds.forEach(id => {
    result[id] = true;
  });
  return result;
};

// Convert TreeSelect format back to array of label IDs
const convertFromTreeSelectFormat = (selection: Record<string, boolean>): string[] => {
  return Object.keys(selection).filter(key => selection[key]);
};

const cleanChildren = (data: TreeNode[]): TreeNode[] => {
  for (const node of data) {
    if (node.children && node.children.length > 0) {
      cleanChildren(node.children);
    } else {
      node.children = undefined;
      node.leaf = true;
    }
  }
  return data;
};

// Load labels for the selected library
const loadLabelsForLibrary = async (libraryId: string) => {
  if (!libraryId || libraryId === 'default') return;

  loadingLabels.value = true;
  treeData.value = [];
  expandedKeys.value = {};

  try {
    console.log('ArticleLabelsInput: Fetching labels for library:', libraryId);
    
    const query = {
      sAction: 'metaKBLabels',
      query: JSON.stringify([{
        property: 'root_kb_id',
        value: libraryId,
      }, {
        property: 'excludeId',
        value: 'kb.KBTreeModel-2',
      }, {
        property: 'excludeChildrenId',
        value: true,
      }]),
    };
    
    console.log('ArticleLabelsInput: API query:', query);
    const labels = await knowledgeAPI.loadLabels(query) as LabelResponse;
    console.log('ArticleLabelsInput: API response:', labels);
    console.log('ArticleLabelsInput: Labels array length:', labels?.pl__kb_labels?.length || 0);
    
    // Debug: Show all unique library IDs in the response
    if (labels?.pl__kb_labels?.length > 0) {
      const uniqueLibraryIds = [...new Set(labels.pl__kb_labels.map(label => label.root_kb_id))];
      console.log('ArticleLabelsInput: Unique library IDs in response:', uniqueLibraryIds);
      console.log('ArticleLabelsInput: Looking for library ID:', libraryId);
    }

    if (labels && labels?.pl__kb_labels?.length > 0) {
      console.log('Labels fetched successfully:', labels);

      // Filter labels to only include those belonging to the selected library
      const filteredLabels = labels?.pl__kb_labels?.filter(label => label.root_kb_id === libraryId);

      // Create tree structure
      const tree = createLabelTree(filteredLabels, libraryId);
      treeData.value = cleanChildren(tree);
      
      console.log('ArticleLabelsInput: Tree data created:', treeData.value);

      // Update the knowledge store's labelMap with the loaded labels
      console.log('ArticleLabelsInput: Updating labelMap with labels:', filteredLabels.length);
      const labelUpdates: Record<string, string> = {};
      filteredLabels.forEach(label => {
        console.log(`ArticleLabelsInput: Adding to labelMap - ${label.id} -> ${label.lbl}`);
        labelUpdates[label.id] = label.lbl;
      });
      knowledgeStore.updateLabelMap(labelUpdates);
      console.log('ArticleLabelsInput: Updated labelMap:', knowledgeStore.labelMap);

      // Set all nodes as expanded
      const expandedNodes: Record<string, boolean> = {};
      const setExpandedKeys = (nodes: TreeNode[]) => {
        nodes.forEach(node => {
          expandedNodes[node.key] = true;
          if (node.children) {
            setExpandedKeys(node.children);
          }
        });
      };
      setExpandedKeys(tree);
      expandedKeys.value = expandedNodes;

    } else {
      console.warn('ArticleLabelsInput: No labels found for this library');
      console.log('ArticleLabelsInput: Response structure:', {
        success: labels?.success,
        hasLabelsArray: !!labels?.pl__kb_labels,
        labelsArrayType: typeof labels?.pl__kb_labels,
        labelsArrayLength: labels?.pl__kb_labels?.length,
        fullResponse: labels
      });
      treeData.value = [];
    }
  } catch (error) {
    console.error('Error loading labels:', error);
    treeData.value = [];
  } finally {
    loadingLabels.value = false;
  }
};

// Function to create tree structure from flat labels
const createLabelTree = (labels: any[], rootParentId: string): TreeNode[] => {
  const labelMap = new Map();
  const root: TreeNode[] = [];

  // Sort labels by path length to ensure parent nodes are processed before children
  const sortedLabels = [...labels].sort((a, b) => a.path.length - b.path.length);

  // First pass: Create all nodes
  sortedLabels.forEach(label => {
    const isTopLevel = label.path.split('/').length === 1;
    
    const node: TreeNode = {
      key: label.id,
      label: label.lbl,
      data: {
        id: label.id,
        path: label.path,
        rootParentId: rootParentId,
        root_kb_id: label.root_kb_id,
        icon: label.icon,
        icon_url: label.icon_url,
        icon_alt_text: label.icon_alt_text,
        icon_visible: label.icon_visible,
        isTopLevel
      },
      leaf: false,
      children: []
      // Note: selectable property removed as BravoTreeSelect may not support it
    };
    labelMap.set(label.id, node);
  });

  // Second pass: Build tree structure based on path
  sortedLabels.forEach(label => {
    const node = labelMap.get(label.id);
    const pathParts = label.path.split('/');
    
    if (pathParts.length === 1) {
      // This is a root level node (library)
      root.push(node);
    } else {
      // This is a child node
      const parentPath = pathParts.slice(0, -1).join('/');
      const parentLabel = sortedLabels.find(l => l.path === parentPath);
      
      if (parentLabel) {
        const parentNode = labelMap.get(parentLabel.id);
        if (parentNode && parentNode.children) {
          parentNode.children.push(node);
        } else {
          // If parent node not found, add to root
          root.push(node);
        }
      } else {
        // If parent path not found, add to root
        root.push(node);
      }
    }
  });

  return root;
};

// Computed property to determine if component is loading
const isLoading = computed(() => props.loading || loadingLabels.value);

// Computed property to determine if component is disabled
const isDisabled = computed(() => props.disabled || !props.libraryId || loadingLabels.value);

// Flag to prevent infinite loops
const isUpdatingFromParent = ref(false);

// Watch for external model value changes
watch(() => props.modelValue, (newValue) => {
  console.log('ArticleLabelsInput: Model value changed:', newValue);
  isUpdatingFromParent.value = true;
  if (Array.isArray(newValue)) {
    selectedKeys.value = convertToTreeSelectFormat(newValue);
  } else {
    selectedKeys.value = {};
  }
  // Reset the flag after a tick to allow internal changes
  nextTick(() => {
    isUpdatingFromParent.value = false;
  });
}, { immediate: true });

// Watch for internal selection changes
watch(selectedKeys, (newSelection) => {
  // Only emit if this change is not from a parent update
  if (!isUpdatingFromParent.value) {
    console.log('ArticleLabelsInput: Selection changed internally:', newSelection);
    const labelIds = convertFromTreeSelectFormat(newSelection);
    console.log('ArticleLabelsInput: Emitting label IDs:', labelIds);
    emit('update:modelValue', labelIds);
  }
}, { deep: true });

// Watch for library changes to reload labels
watch(() => props.libraryId, async (newLibraryId, oldLibraryId) => {
  console.log('ArticleLabelsInput: Library ID changed from', oldLibraryId, 'to:', newLibraryId);
  console.log('ArticleLabelsInput: Current treeData length:', treeData.value.length);
  console.log('ArticleLabelsInput: Current selectedKeys:', selectedKeys.value);
  
  if (newLibraryId && newLibraryId !== oldLibraryId) {
    console.log('ArticleLabelsInput: Loading labels for library:', newLibraryId);
    // Clear existing data first to force re-render
    treeData.value = [];
    selectedKeys.value = {};
    expandedKeys.value = {};
    
    // Also emit empty array to parent to clear selected labels
    emit('update:modelValue', []);
    
    await loadLabelsForLibrary(newLibraryId);
  } else if (!newLibraryId) {
    console.log('ArticleLabelsInput: No library ID provided, clearing data');
    treeData.value = [];
    selectedKeys.value = {};
    expandedKeys.value = {};
    emit('update:modelValue', []);
  }
}, { immediate: true });

// Computed property to check if we should render the TreeSelect
const shouldRenderTreeSelect = computed(() => {
  // Render if we have a library ID (even if loading)
  return !!props.libraryId;
});

// Computed property for the component key to force re-render on library change
const componentKey = computed(() => {
  const key = `${props.libraryId}-${treeData.value.length}`;
  console.log('ArticleLabelsInput: Component key changed to:', key);
  return key;
});
</script>

<template>
  <div v-if="!props.libraryId" class="no-library-message">
    Please select a library first
  </div>
  <div v-else-if="shouldRenderTreeSelect">
    <BravoTreeSelect
      :key="componentKey"
      v-if="treeData.length > 0"
      v-model="selectedKeys"
      :options="treeData"
      selectionMode="multiple"
      display="chip"
      optionLabel="label"
      :placeholder="placeholder"
      :class="props.class"
      :expandedKeys="expandedKeys"
      :loading="isLoading"
      :disabled="isDisabled"
      :maxSelectedLabels="3"
    >
      <template #loadingicon>
        <i class="pi pi-spin pi-spinner" style="font-size: 1rem"></i>
      </template>
    </BravoTreeSelect>
    <div v-else-if="isLoading" class="loading-message">
      <i class="pi pi-spin pi-spinner" style="font-size: 1rem; margin-right: 0.5rem;"></i>
      {{ t('common.loading') }}...
    </div>
    <div v-else class="no-labels-message">
      No labels available for this library
    </div>
  </div>
</template>

<style scoped>
.no-library-message,
.loading-message,
.no-labels-message {
  padding: 0.75rem;
  text-align: center;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  background: var(--surface-ground);
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 