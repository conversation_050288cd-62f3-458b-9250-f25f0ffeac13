<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoFormField from './BravoFormField.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import ArticleLabelsInput from './ArticleLabelsInput.vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { useToast } from 'primevue/usetoast';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get knowledge store for labels data
const knowledgeStore = useKnowledgeStore();
const toast = useToast();

// Track loading state locally
const isLocalSaving = ref(false);

// Keep a local reactive copy of the current labels that we can manage independently
const currentLabels = ref<string[]>([]);

// Reference to the BravoFormField component
const bravoFormFieldRef = ref<any>(null);

// Initialize currentLabels from the article and watch for changes
watch(() => props.article.bc__tags_object_kb_labels, (newLabels) => {
  console.log('LabelsFormInput: Article labels watcher triggered');
  console.log('LabelsFormInput: New labels from article:', newLabels);
  console.log('LabelsFormInput: Current local labels:', currentLabels.value);
  
  // Update our local copy when the article changes
  const labels = newLabels ? [...newLabels] : [];
  console.log('LabelsFormInput: Updating local labels to:', labels);
  currentLabels.value = labels;
}, { immediate: true, deep: true });

// Computed to get current library ID
const currentLibraryId = computed(() => {
  return props.article.root_parent_id || props.article.library_id || 'default';
});

// Check if labels are still loading (showing as IDs)
const isLabelsDisplayLoading = computed(() => {
  if (!props.article.bc__tags_object_kb_labels) return false;
  
  // If we're explicitly loading, show loading state
  if (isLocalSaving.value) return true;
  
  // Check if any labels are still missing (would show as IDs)
  const missingLabels = props.article.bc__tags_object_kb_labels.filter((id: string) => !knowledgeStore.labelMap[id]);
  return missingLabels.length > 0;
});

// For chip display
const selectedLabelNames = computed(() => {
  if (!currentLabels.value || currentLabels.value.length === 0) return [];
  
  console.log('LabelsFormInput: selectedLabelNames - Label IDs from local copy:', currentLabels.value);
  console.log('LabelsFormInput: selectedLabelNames - LabelMap keys:', Object.keys(knowledgeStore.labelMap));
  
  // Force reactivity by accessing the labelMap through the store's reactive state
  const labelMap = knowledgeStore.labelMap;
  
  const names = currentLabels.value.map((id: string) => {
    const name = labelMap[id];
    console.log(`LabelsFormInput: selectedLabelNames - ID ${id} -> Name: ${name || 'NOT FOUND'}`);
    return name || id;
  });
  
  console.log('LabelsFormInput: selectedLabelNames - Final names:', names);
  return names;
});

// Get labels display value
const labelsDisplayValue = computed(() => {
  if (selectedLabelNames.value.length === 0) return 'No labels';
  if (isLabelsDisplayLoading.value) return 'Loading labels...';
  return selectedLabelNames.value;
});

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
  console.log('LabelsFormInput: handleUpdate called with value:', value);
  // No special handling needed for labels - just pass through
};

// Handle submission start
const handleSubmitStart = () => {
  // Set loading state when submission starts
  isLocalSaving.value = true;
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
  isLocalSaving.value = true;
  try {
    // Ensure we always send an array of strings
    let labelIds = value;
    if (labelIds && typeof labelIds === 'object' && !Array.isArray(labelIds)) {
      // Convert TreeSelect format to array if needed
      labelIds = Object.keys(labelIds).filter(key => labelIds[key]);
    }
    
    // Ensure it's an array
    if (!Array.isArray(labelIds)) {
      labelIds = labelIds ? [labelIds] : [];
    }
    
    // Convert to strings
    labelIds = labelIds.map((id: any) => String(id));
    
    console.log('LabelsFormInput: Processed label IDs for save:', labelIds);
    
    if (props.onSubmit) {
      const success = await props.onSubmit('bc__tags_object_kb_labels', labelIds);
      if (success) {
        // Update our local state
        currentLabels.value = [...labelIds];
        
        console.log('LabelsFormInput: After emitting updates - local labels:', currentLabels.value);
        
        // Update the article object and emit events
        const updatedArticle = { ...props.article, bc__tags_object_kb_labels: labelIds };
        emit('update', updatedArticle);
        
        // Call the BravoFormField's handleSaveComplete method
        if (bravoFormFieldRef.value) {
          bravoFormFieldRef.value.handleSaveComplete(true);
        }
      } else {
        // Call the BravoFormField's handleSaveComplete method with failure
        if (bravoFormFieldRef.value) {
          bravoFormFieldRef.value.handleSaveComplete(false);
        }
      }
    } else {
      // Call the BravoFormField's handleSaveComplete method with failure
      if (bravoFormFieldRef.value) {
        bravoFormFieldRef.value.handleSaveComplete(false);
      }
    }
  } catch (error: any) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to update labels',
      life: 3000,
    });
    // Call the BravoFormField's handleSaveComplete method with failure
    if (bravoFormFieldRef.value) {
      bravoFormFieldRef.value.handleSaveComplete(false);
    }
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  console.log('LabelsFormInput: Canceling labels edit');
  // BravoFormField will handle resetting the value
};

// Helper function to fetch and add label to map if missing
const fetchAndAddLabelToMap = async (labelId: string) => {
  if (!knowledgeStore.labelMap[labelId]) {
    console.log(`LabelsFormInput: Fetching missing label: ${labelId}`);
    try {
      await knowledgeStore.fetchLabelTree();
    } catch (error) {
      console.error(`LabelsFormInput: Error fetching label ${labelId}:`, error);
    }
  }
};

// Ensure all label names are loaded
const ensureLabelsLoaded = async () => {
  if (currentLabels.value.length > 0) {
    for (const id of currentLabels.value) {
      if (!knowledgeStore.labelMap[id]) {
        await fetchAndAddLabelToMap(id);
      }
    }
  }
};
</script>

<template>
  <BravoFormField
    ref="bravoFormFieldRef"
    label="Labels"
    field-name="labels"
    :value="currentLabels"
    :display-value="labelsDisplayValue"
    input-type="articlelabels"
    display-type="chips"
    :is-loading="isLabelsDisplayLoading"
    :is-horizontal="isHorizontal"
    icon-class="pi pi-tag"
    :is-saving="isLocalSaving"
    no-value-text="No labels"
    data-test-id="article-labels"
    :is-editing="isEditing"
    :library-id="currentLibraryId"
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  >
    <!-- Custom display slot for chips -->
    <template #display>
      <div v-if="isLabelsDisplayLoading" class="label-loading">
        <span>Loading labels...</span>
      </div>
      <div v-else-if="selectedLabelNames.length === 0" class="no-labels">
        <span>No labels</span>
      </div>
      <div v-else class="label-chips">
        <BravoTag
          v-for="(label, index) in selectedLabelNames"
          :key="`${label}-${index}`"
          :value="label"
          severity="info"
          class="label-chip"
        />
      </div>
    </template>
  </BravoFormField>
</template>

<style scoped lang="scss">
// Labels-specific styles
.no-labels {
  color: #64748b;
}

.label-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
}

.label-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.label-chip {
  font-size: 0.875rem;
}
</style> 