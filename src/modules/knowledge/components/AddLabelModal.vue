<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { useToast } from 'primevue/usetoast';
import type { KnowledgeLibrary, KnowledgeLabel } from '../../../services/KnowledgeAPI';

import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoTreeSelect from '@services/ui-component-library/components/BravoTreeSelect.vue';
import { useKnowledgeAPI } from '@/composables/services';
import { useI18n } from 'vue-i18n';

interface LabelResponse {
  success: boolean;
  pl__kb_labels: Array<{
    id: string;
    val: string;
    lbl: string;
    path: string;
    actual_path: string;
    indent_lbl: string;
    root_kb_id: string;
    iconCls: string;
    icon: string | null;
    icon_url: string;
    icon_alt_text: string | null;
    icon_visible: boolean;
    url: string;
  }>;
  current_server_time: string;
}

const { t } = useI18n();
const store = useKnowledgeStore();
const toast = useToast();
const knowledgeAPI = useKnowledgeAPI();

const visible = ref(false);
const title = ref('');
const selectedLibraryId = ref('');
const selectedParentLabelId = ref<{ [key: string]: boolean }>({});
const loadingLibraries = ref(false);
const loadingLabels = ref(false);
const loading = ref(false);
const treeData = ref<any[]>([]);
const expandedKeys = ref<Record<string, boolean>>({});

// Format libraries for the dropdown
const libraryOptions = ref<Array<{ label: string; value: string }>>([]);

onMounted(() => {
    // No action needed on mount
});

const provideFallbackOptions = () => {
    // Use a default/fallback library option if API fails
    libraryOptions.value = [{ label: 'Default Library', value: 'default' }];
    selectedLibraryId.value = 'default';
};

// Watch for library selection changes to load labels for that library
watch(selectedLibraryId, async (newLibraryId) => {
    if (newLibraryId) {
        await loadLabelsForLibrary(newLibraryId);
    } else {
        // Clear labels if no library is selected
        treeData.value = [];
        selectedParentLabelId.value = {};
        expandedKeys.value = {};
    }
});

const cleanChildren = (data: any) => {
  for (const node of data) {
    if (node.children && node.children.length) {
      cleanChildren(node.children);
    } else {
      delete node.children;
      delete node.icon;
      node.leaf = true;
    }
  }
  return data;
};

// Load labels for the selected library
const loadLabelsForLibrary = async (libraryId: string) => {
    if (!libraryId || libraryId === 'default') return;

    loadingLabels.value = true;
    selectedParentLabelId.value = {};
    treeData.value = [];
    expandedKeys.value = {};

    try {
        console.log('Fetching labels for library:', libraryId);
        
        const query = {
            sAction: 'metaKBLabels',
            query: JSON.stringify([{
                property: 'root_kb_id',
                value: libraryId,
            },{
                property: 'excludeId',
                value: 'kb.KBTreeModel-2',
            },{
                property: 'excludeChildrenId',
                value: true,
            }]),
        };
        const labels = await knowledgeAPI.loadLabels(query) as LabelResponse;

        if (labels && labels?.pl__kb_labels?.length > 0) {
            console.log('Labels fetched successfully:', labels);

            // Filter labels to only include those belonging to the selected library
            const filteredLabels = labels?.pl__kb_labels?.filter(label => label.root_kb_id === libraryId);

            // Create tree structure
            const tree = createLabelTree(filteredLabels, libraryId);
            treeData.value = cleanChildren(tree);

            // Set all nodes as expanded
            const expandedNodes: Record<string, boolean> = {};
            const setExpandedKeys = (nodes: any[]) => {
                nodes.forEach(node => {
                    expandedNodes[node.key] = true;
                    if (node.children) {
                        setExpandedKeys(node.children);
                    }
                });
            };
            setExpandedKeys(tree);
            expandedKeys.value = expandedNodes;

        } else {
            console.warn('No labels found for this library');
            treeData.value = [];
        }
    } catch (error) {
        console.error('Error loading labels:', error);
        treeData.value = [];
    } finally {
        loadingLabels.value = false;
    }
};

// Function to create tree structure from flat labels
const createLabelTree = (labels: any[], rootParentId: string) => {
    const labelMap = new Map();
    const root: any[] = [];

    // Sort labels by path length to ensure parent nodes are processed before children
    const sortedLabels = [...labels].sort((a, b) => a.path.length - b.path.length);

    // First pass: Create all nodes
    sortedLabels.forEach(label => {
        const node = {
            key: label.id,
            label: label.lbl,
            data: {
                id: label.id,
                path: label.path,
                rootParentId: rootParentId,
                root_kb_id: label.root_kb_id,
                icon: label.icon,
                icon_url: label.icon_url,
                icon_alt_text: label.icon_alt_text,
                icon_visible: label.icon_visible
            },
            leaf: false,
            children: []
        };
        labelMap.set(label.id, node);
    });

    // Second pass: Build tree structure based on path
    sortedLabels.forEach(label => {
        const node = labelMap.get(label.id);
        const pathParts = label.path.split('/');
        
        if (pathParts.length === 1) {
            // This is a root level node
            root.push(node);
        } else {
            // This is a child node
            const parentPath = pathParts.slice(0, -1).join('/');
            const parentLabel = sortedLabels.find(l => l.path === parentPath);
            
            if (parentLabel) {
                const parentNode = labelMap.get(parentLabel.id);
                if (parentNode) {
                    parentNode.children.push(node);
                } else {
                    // If parent node not found, add to root
                    root.push(node);
                }
            } else {
                // If parent path not found, add to root
                root.push(node);
            }
        }
    });

    return root;
};

const showModal = async () => {
    // Reset form
    title.value = '';
    selectedParentLabelId.value = {};
    treeData.value = [];
    expandedKeys.value = {};
    visible.value = true;

    loadingLibraries.value = true;

    // Only try to load libraries if we don't already have options
    if (libraryOptions.value.length === 0) {
        try {
            console.log('Fetching libraries for dropdown...');
            // Directly fetch libraries from API without touching the store
            const libraries = await knowledgeAPI.fetchKnowledgeLibraries();

            if (libraries && libraries.length > 0) {
                console.log('Libraries fetched successfully:', libraries);
                // Format libraries for dropdown without updating the store
                libraryOptions.value = libraries.map((lib: KnowledgeLibrary) => ({
                    label: lib.name,
                    value: lib.id,
                }));

                console.log('Library options prepared:', libraryOptions.value);

                // Select the first one
                if (libraryOptions.value.length > 0) {
                    selectedLibraryId.value = libraryOptions.value[0].value;
                    console.log('Selected library:', selectedLibraryId.value);

                    // Load labels for this library
                    await loadLabelsForLibrary(selectedLibraryId.value);
                }
            } else {
                console.warn('No libraries found, using fallback');
                // Use fallback if no libraries found
                provideFallbackOptions();
            }
        } catch (error) {
            console.error('Error loading libraries:', error);
            provideFallbackOptions();
        } finally {
            loadingLibraries.value = false;
        }
    } else {
        // Libraries are already loaded, just load labels for the selected library
        if (selectedLibraryId.value) {
            await loadLabelsForLibrary(selectedLibraryId.value);
        }
        loadingLibraries.value = false;
    }
};

const hideModal = () => {
    visible.value = false;
};

const createLabel = async () => {
    if (!title.value.trim()) {
        toast.add({
            severity: 'warn',
            summary: 'Validation Error',
            detail: 'Please enter a title for the label',
            life: 3000,
        });
        return;
    }

    if (!selectedLibraryId.value) {
        toast.add({
            severity: 'warn',
            summary: 'Validation Error',
            detail: 'Please select a library',
            life: 3000,
        });
        return;
    }

    loading.value = true;

    try {
        const parentId = Object.keys(selectedParentLabelId.value)[0] || '';

        console.log(
            'Creating label with title:',
            title.value,
            'library ID:',
            selectedLibraryId.value,
            'parent label ID:',
            parentId || 'None (Top Level)'
        );

        // Normalize title for short_name
        const shortName = title.value
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '')
            .substring(0, 30);

        // Create label payload
        const labelData: KnowledgeLabel = {
            id: 'kb.KBTreeModel-2',
            title: title.value,
            root_kb_id: selectedLibraryId.value,
            parent_id: parentId,
            parentId: parentId || null,
            leaf: false,
            owner_partner_id: '',
            short_name: shortName,
            sub_title: '',
            status: 0,
            updated: '',
            created: '',
            _merge: '',
            merge_ids: '',
            url: '',
        };

        const response = await knowledgeAPI.createKnowledgeLabel(labelData);
        console.log('Label creation response:', response);

        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Label created successfully',
            life: 3000,
        });

        hideModal();

        // Refresh only the parent node where the new label was inserted
        setTimeout(() => {
            // If there's a parent ID, refresh that node, otherwise refresh the library node
            const nodeToRefresh = parentId || selectedLibraryId.value;
            store.fetchChildNodes(nodeToRefresh);
        }, 500); // Add a small delay to ensure the server has processed the new label
    } catch (error) {
        console.error('Error creating label:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: error instanceof Error ? error.message : 'Failed to create label',
            life: 5000,
        });
    } finally {
        loading.value = false;
    }
};

defineExpose({
    showModal,
});
</script>

<template>
    <BravoDialog
        v-model:visible="visible"
        header="Create Label"
        :modal="true"
        :closable="true"
        :style="{ width: '450px' }"
        :draggable="false"
    >
        <div class="add-label-form">
            <div class="form-field">
                <label for="label-title">Label Name <span class="required">*</span></label>
                <BravoInputText
                    id="label-title"
                    v-model="title"
                    placeholder="Enter label name"
                    :autoFocus="true"
                    class="w-full"
                />
            </div>

            <div class="form-field">
                <label for="label-library">Library <span class="required">*</span></label>
                <BravoSelect
                    id="label-library"
                    v-model="selectedLibraryId"
                    :options="libraryOptions"
                    placeholder="Select a library"
                    class="w-full"
                    dataTestId="label-library-select"
                    :loading="loadingLibraries"
                />
            </div>

            <div class="form-field">
                <label for="parent-label">{{ t('knowledge.edit_label.nest_under') }}</label>
                <BravoTreeSelect
                    id="parent-label"
                    v-model="selectedParentLabelId"
                    :options="treeData"
                    optionLabel="label"
                    :placeholder="loadingLabels ? t('common.loading') : t('knowledge.edit_label.nest_under')"
                    class="w-full"
                    dataTestId="parent-label-select"
                    :expandedKeys="expandedKeys"
                    :loading="loadingLabels"
                    :disabled="!selectedLibraryId || loadingLabels"
                >
                    <template #loadingicon>
                        <i class="pi pi-spin pi-spinner" style="font-size: 1rem"></i>
                    </template>
                </BravoTreeSelect>
                <small class="help-text"
                    >Select a parent label to nest this label under, or leave empty to create a top-level label.</small
                >
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <BravoButton label="Cancel" severity="secondary" @click="hideModal" :disabled="loading" />
                <BravoButton 
                    label="Create" 
                    @click="createLabel" 
                    :loading="loading"
                    :disabled="!title.trim() || !selectedLibraryId"
                />
            </div>
        </template>
    </BravoDialog>
</template>

<style scoped>
.add-label-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 0.5rem 0;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-field label {
    font-weight: 500;
}

.required {
    color: var(--red-500);
}

.help-text {
    color: var(--text-color-secondary);
    font-size: 0.85rem;
    padding: 0 0.2rem 0rem 0.2rem;
    line-height: 1.25rem;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

.p-error {
    color: var(--red-500);
    font-size: 0.875rem;
}

:deep(.p-invalid) {
    border-color: var(--red-500) !important;
}
</style>
../../features/kb/stores/knowledge
