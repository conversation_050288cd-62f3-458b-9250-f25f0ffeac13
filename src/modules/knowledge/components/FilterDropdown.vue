<!-- FilterDropdown.vue -->
<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoFilterMultiSelect from '@services/ui-component-library/components/BravoFilterMultiSelect.vue';

interface FilterOption {
  label: string;
  value: string;
}

const props = defineProps<{
  label: string;
  options: FilterOption[];
  modelValue: string[];
  borderColor?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void;
  (e: 'remove'): void;
  (e: 'change'): void;
}>();

const internalValue = ref<string[]>([]);
const changeTimeout = ref<number | null>(null);

// Sync internal value with external value
watch(() => props.modelValue, (newVal) => {
  if (JSON.stringify(internalValue.value) !== JSON.stringify(newVal)) {
    internalValue.value = [...newVal];
  }
}, { immediate: true });

// Update external value when internal value changes with debounce
watch(() => internalValue.value, (newVal) => {
  emit('update:modelValue', newVal);
  
  // Clear any existing timeout
  if (changeTimeout.value !== null) {
    window.clearTimeout(changeTimeout.value);
    changeTimeout.value = null;
  }
  
  // Set a new timeout to prevent rapid consecutive changes
  changeTimeout.value = window.setTimeout(() => {
    emit('change');
  }, 300);
}, { deep: true });

// Clean up timeout on component unmount
onBeforeUnmount(() => {
  if (changeTimeout.value !== null) {
    window.clearTimeout(changeTimeout.value);
    changeTimeout.value = null;
  }
});

// Handle the remove button click
const removeFilter = () => {
  emit('remove');
};
</script>

<template>
  <div class="filter-container" :style="{ 
    borderColor: borderColor || 'var(--p-inputtext-border-color, #ced4da)' 
  }">
    <div class="filter-label-with-close" :style="{ 
      borderColor: borderColor || 'var(--p-inputtext-border-color, #ced4da)' 
    }">
      <BravoButton 
        icon="pi pi-times" 
        text 
        size="small" 
        @click="removeFilter" 
        class="filter-remove-btn"
      />
      <div class="filter-label">{{ label }}:</div>
    </div>
    <BravoFilterMultiSelect
      v-model="internalValue"
      :filterOptions="options"
      :placeholder="`Select ${label.toLowerCase()}`"
      optionLabel="label"
      optionValue="value"
      class="w-full"
      @filter-change="(value) => emit('update:modelValue', value)"
    />
  </div>
</template>

<style scoped>
.filter-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--p-inputtext-border-color, #ced4da);
  border-radius: 6px;
  overflow: hidden;
  background-color: var(--surface-0, #ffffff);
}

.filter-label-with-close {
  display: flex;
  align-items: center;
  border-right: 1px solid var(--p-inputtext-border-color, #ced4da);
}

.filter-label {
  font-weight: 600;
  font-size: 0.875rem;
  white-space: nowrap;
  padding: 0 0.75rem 0 0;
  color: var(--text-color-secondary, #6c757d);
}

.filter-remove-btn {
  margin: 0;
  padding: 0 0.5rem 0 0.75rem;
}

.filter-remove-btn:deep(.p-button-icon) {
  font-size: 0.75rem;
}

:deep(.bravo-filter) {
  min-width: 180px;
  border: none;
  border-radius: 0;
}

:deep(.bravo-filter .p-multiselect) {
  border: none !important;
  border-radius: 0;
}
</style> 