<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import BravoForm<PERSON>ield from './BravoFormField.vue';
import Button from 'primevue/button';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Compute the display values for the search terms
const searchTermsDisplayValues = computed(() => {
  return props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
});

// Track the edited value locally
const editedSearchTerms = ref<string[]>([]);

// Track the current filter text
const filterText = ref('');

// Track the new term input separately from filter
const newTermInput = ref('');

// Reference to the new term input element
const newTermInputRef = ref<HTMLInputElement | null>(null);

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Initialize the edited search terms when the article changes
watch(() => props.article.keywords, (newKeywords) => {
  editedSearchTerms.value = newKeywords ? newKeywords.split(',').map((term: string) => term.trim()) : [];
}, { immediate: true });

// Watch for when we enter edit mode to auto-focus the new term input
watch(() => props.isEditing, (isEditing) => {
  if (isEditing) {
    console.log('SearchTermsFormInput: Entering edit mode, attempting to focus input');
    
    // Try multiple attempts with increasing delays
    const attemptFocus = (attempt = 1) => {
      setTimeout(() => {
        console.log(`SearchTermsFormInput: Focus attempt ${attempt}`);
        console.log('SearchTermsFormInput: newTermInputRef.value:', newTermInputRef.value);
        
        if (newTermInputRef.value) {
          console.log('SearchTermsFormInput: Input element found, attempting focus');
          newTermInputRef.value.focus();
          
          // Check if focus was successful
          setTimeout(() => {
            const activeElement = document.activeElement;
            console.log('SearchTermsFormInput: Active element after focus:', activeElement);
            console.log('SearchTermsFormInput: Is our input focused?', activeElement === newTermInputRef.value);
          }, 50);
        } else {
          console.log('SearchTermsFormInput: Input element not found');
          
          // Try again if we haven't tried too many times
          if (attempt < 5) {
            attemptFocus(attempt + 1);
          }
        }
      }, attempt * 200); // Increasing delay: 200ms, 400ms, 600ms, etc.
    };
    
    attemptFocus();
  }
});

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
  editedSearchTerms.value = value;
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Add a new term if not present and not empty
const addNewTerm = async () => {
  const term = newTermInput.value.trim();
  if (term && !editedSearchTerms.value.includes(term)) {
    editedSearchTerms.value = [...editedSearchTerms.value, term];
    await nextTick();
    newTermInput.value = '';
  }
};

// Try to focus the input when the footer becomes visible
const focusNewTermInput = () => {
  console.log('SearchTermsFormInput: focusNewTermInput called');
  nextTick(() => {
    if (newTermInputRef.value) {
      console.log('SearchTermsFormInput: Focusing input via nextTick');
      newTermInputRef.value.focus();
    }
  });
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Check if the arrays are different
    const currentTerms = props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
    const isChanged = 
      value.length !== currentTerms.length ||
      value.some((term: string) => !currentTerms.includes(term)) ||
      currentTerms.some((term: string) => !value.includes(term));
      
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit('keywords', value.join(', '));
      formInputRef.value?.handleSaveComplete(success);
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving search terms:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedSearchTerms.value = props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
  filterText.value = '';
  newTermInput.value = '';
  // Tell the BravoFormField to exit edit mode
  formInputRef.value?.handleSaveComplete(true);
};

// Function to handle adding a new term
const handleNewSearchTerm = () => {
  addNewTerm();
};

// Handle filter event from MultiSelect
const handleFilter = (event: any) => {
  filterText.value = event.value;
};

// Computed property to determine if we can add the current new term
const canAddTerm = computed(() => {
  const term = newTermInput.value.trim();
  return term && !editedSearchTerms.value.includes(term);
});

// Global keydown handler for Enter key
const handleGlobalKeydown = (event: KeyboardEvent) => {
  // Only handle Enter key
  if (event.key === 'Enter') {
    // Check if we're focused on our new term input
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement && activeElement.classList.contains('new-term-input')) {
      console.log('SearchTermsFormInput: Enter key detected in new term input');
      console.log('SearchTermsFormInput: newTermInput:', newTermInput.value, 'canAddTerm:', canAddTerm.value);
      
      // If we can add the current term, do it
      if (canAddTerm.value) {
        console.log('SearchTermsFormInput: Adding new term via Enter key');
        addNewTerm();
        event.preventDefault();
        event.stopPropagation();
      }
    }
  }
};

// Add global keydown listener on mount
onMounted(() => {
  console.log('SearchTermsFormInput: Adding global keydown listener');
  document.addEventListener('keydown', handleGlobalKeydown);
});

// Remove global keydown listener on unmount
onUnmounted(() => {
  console.log('SearchTermsFormInput: Removing global keydown listener');
  document.removeEventListener('keydown', handleGlobalKeydown);
});

</script>

<template>
  <BravoFormField
    ref="formInputRef"
    label="Search Terms"
    fieldName="searchTerms"
    :value="editedSearchTerms"
    :displayValue="searchTermsDisplayValues"
    inputType="multiselect"
    displayType="chips"
    :options="editedSearchTerms"
    :isLoading="isLoading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-search"
    noValueText="No search terms"
    dataTestId="article-search-terms"
    enforceSubmitButton
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
    @filter="handleFilter"
  >
    <template #footer>
      <div class="add-term-section" @vue:mounted="focusNewTermInput">
        <div class="add-term-input-container">
          <input
            ref="newTermInputRef"
            v-model="newTermInput"
            type="text"
            class="new-term-input"
            placeholder="Add new search term..."
            autofocus
            @keydown.enter="canAddTerm && addNewTerm()"
            @vue:mounted="focusNewTermInput"
          />
          <Button
            class="add-term-btn"
            @click="addNewTerm"
            :disabled="!canAddTerm"
            icon="pi pi-plus"
            size="small"
            :outlined="!canAddTerm"
            :severity="canAddTerm ? 'success' : 'secondary'"
          />
        </div>
        <div v-if="newTermInput.trim() && editedSearchTerms.includes(newTermInput.trim())" class="term-exists-message">
          This term already exists
        </div>
      </div>
    </template>
  </BravoFormField>
</template> 

<style scoped>
.add-term-section {
  padding: 0.75rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.add-term-input-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.new-term-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: white;
}

.new-term-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.new-term-input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.add-term-btn {
  flex-shrink: 0;
  height: 36px; /* Match input height */
}

.term-exists-message {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #ef4444;
  font-style: italic;
}

/* Ensure the footer integrates well with MultiSelect */
:deep(.p-multiselect-panel .p-multiselect-footer) {
  padding: 0;
  border-top: none;
}
</style>