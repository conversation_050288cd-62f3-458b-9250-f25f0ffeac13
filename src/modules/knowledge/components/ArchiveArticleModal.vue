<!-- ArchiveArticleModal.vue -->
<script setup lang="ts">
import { ref } from 'vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { stripHtmlAndDecodeEntities } from '../utils/helpers';

const props = defineProps<{
  articleId?: string;
  articleTitle?: string;
}>();

const emit = defineEmits<{
  (e: 'archived', articleId: string): void;
  (e: 'error', error: any): void;
}>();

const store = useKnowledgeStore();
const toast = useToast();
const confirm = useConfirm();
const isArchiving = ref(false);

// Method to show the archive confirmation dialog
const showConfirmation = () => {
  if (!props.articleId || !props.articleTitle) {
    console.error('Missing article ID or title for archive operation');
    return;
  }

  // Clean the article title if it contains HTML
  const cleanTitle = stripHtmlAndDecodeEntities(props.articleTitle);

  confirm.require({
    message: `Are you sure you want to archive the Article, "${cleanTitle}"?`,
    header: 'Archive Article',
    icon: 'pi pi-inbox',
    acceptClass: 'p-button-primary',
    rejectClass: 'p-button-secondary',
    accept: () => archiveArticle(),
    reject: () => {
      // User rejected the confirmation, do nothing
    }
  });
};

// Method to handle article archival
const archiveArticle = async () => {
  if (!props.articleId) return;
  
  isArchiving.value = true;
  
  try {
    const response = await store.archiveArticle(props.articleId);
    console.log('Archive response:', response);
    
    // Show success toast
    toast.add({
      severity: 'success',
      summary: 'Article Archived',
      detail: `"${props.articleTitle}" was successfully archived.`,
      life: 5000
    });
    
    // Emit archived event with article ID
    emit('archived', props.articleId);
  } catch (error: any) {
    console.error('Error archiving article:', error);
    
    // Show error toast
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: `Failed to archive article: ${error.message || 'Unknown error'}`,
      life: 5000
    });
    
    // Emit error event
    emit('error', error);
  } finally {
    isArchiving.value = false;
  }
};

// Method to expose for external components to call
defineExpose({
  showConfirmation
});
</script>

<template>
  <div style="display: none;"></div>
</template> 