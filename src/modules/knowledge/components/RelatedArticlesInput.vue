<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import ToggleSwitch from 'primevue/toggleswitch';
import Dropdown from 'primevue/dropdown';
import Button from 'primevue/button';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import draggable from 'vuedraggable';

// Define props
const props = defineProps<{
  modelValue: {
    autoGenArticles: boolean;
    relatedArticles: string[];
  };
  availableKnowledgeBases: any[];
  loadingKnowledgeBases?: boolean;
  isReady?: boolean;
  disabled?: boolean;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: { autoGenArticles: boolean; relatedArticles: string[] }): void;
}>();

// Local reactive state
const localAutoGenArticles = ref(props.modelValue.autoGenArticles);
const localRelatedArticles = ref([...props.modelValue.relatedArticles]);

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  // Only update if values have actually changed to prevent recursive updates
  if (localAutoGenArticles.value !== newValue.autoGenArticles) {
    localAutoGenArticles.value = newValue.autoGenArticles;
  }
  
  const newRelatedArticles = newValue.relatedArticles || [];
  if (JSON.stringify(localRelatedArticles.value) !== JSON.stringify(newRelatedArticles)) {
    localRelatedArticles.value = [...newRelatedArticles];
  }
}, { deep: true });

// Watch local changes and emit updates
watch([localAutoGenArticles, localRelatedArticles], () => {
  const newValue = {
    autoGenArticles: localAutoGenArticles.value,
    relatedArticles: [...localRelatedArticles.value]
  };
  
  // Only emit if the value has actually changed to prevent recursive updates
  const currentModelValue = props.modelValue;
  const hasChanged = 
    currentModelValue.autoGenArticles !== newValue.autoGenArticles ||
    JSON.stringify(currentModelValue.relatedArticles) !== JSON.stringify(newValue.relatedArticles);
  
  if (hasChanged) {
    emit('update:modelValue', newValue);
  }
}, { deep: true });

// Computed to get the current value for external access
const currentValue = computed(() => ({
  autoGenArticles: localAutoGenArticles.value,
  relatedArticles: [...localRelatedArticles.value]
}));

// Dropdown selection state
const selectedDropdownItem = ref<any>(null);

// Computed to get available options (excluding already selected ones)
const availableOptions = computed(() => {
  return props.availableKnowledgeBases.filter(kb => 
    !localRelatedArticles.value.includes(kb.id)
  );
});

// Computed to get selected articles with full details
const selectedArticlesList = computed(() => {
  return localRelatedArticles.value.map(id => {
    const article = props.availableKnowledgeBases.find(kb => kb.id === id);
    return article || { id, lbl: `Unknown Article (${id})` };
  }).filter(Boolean);
});

// Handle dropdown selection
const handleDropdownSelect = () => {
  if (selectedDropdownItem.value) {
    const newId = selectedDropdownItem.value.id;
    if (!localRelatedArticles.value.includes(newId)) {
      localRelatedArticles.value = [...localRelatedArticles.value, newId];
    }
    selectedDropdownItem.value = null; // Reset dropdown
  }
};

// Handle removing an article from the list
const removeArticle = (articleId: string) => {
  localRelatedArticles.value = localRelatedArticles.value.filter(id => id !== articleId);
};

// Handle drag and drop reordering
const handleDragEnd = (event: any) => {
  // The draggable component will automatically update the array
  // We just need to ensure the change is detected
  localRelatedArticles.value = [...localRelatedArticles.value];
};

// Expose the current value for parent components
defineExpose({
  currentValue
});
</script>

<template>
  <div class="related-articles-input">
    <div class="auto-gen-toggle">
      <ToggleSwitch
        v-model="localAutoGenArticles"
        :disabled="disabled"
        @click.stop
        data-testid="auto-gen-articles-toggle"
      />
      <BravoLabel text="Auto Generate" class="article-field-label toggle-label min-w-[100px]" />
    </div>
    
    <template v-if="!localAutoGenArticles">
      <!-- Selected Articles List (Draggable) -->
      <div v-if="selectedArticlesList.length > 0" class="selected-articles-list">
        <draggable
          v-model="localRelatedArticles"
          item-key="id"
          @end="handleDragEnd"
          class="draggable-list"
          ghost-class="ghost-item"
          chosen-class="chosen-item"
          drag-class="drag-item"
        >
          <template #item="{ element: articleId }">
            <div class="selected-article-item">
              <div class="drag-handle">
                <i class="pi pi-bars" />
              </div>
              <div class="article-content">
                <span class="article-title">
                  {{ selectedArticlesList.find(a => a.id === articleId)?.lbl || 'Unknown Article' }}
                </span>
                <span class="article-status">
                  ({{ selectedArticlesList.find(a => a.id === articleId)?.status || 'Published' }})
                </span>
              </div>
              <Button
                icon="pi pi-times"
                class="p-button-text p-button-sm remove-btn"
                @click="removeArticle(articleId)"
                :disabled="disabled"
                data-testid="remove-article-btn"
              />
            </div>
          </template>
        </draggable>
      </div>
      
      <!-- Dropdown for adding new articles -->
      <div class="add-article-dropdown">
        <Dropdown
          v-model="selectedDropdownItem"
          :options="availableOptions"
          optionLabel="lbl"
          :loading="loadingKnowledgeBases"
          placeholder="Search for Articles"
          class="w-full"
          :disabled="disabled || !isReady"
          filter
          filterPlaceholder="Search articles..."
          @change="handleDropdownSelect"
          @click.stop
          data-testid="add-article-dropdown"
        />
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.related-articles-input {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.auto-gen-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-label {
  font-size: 0.875rem;
  color: #374151;
}

// Selected articles list styling
.selected-articles-list {
  margin-bottom: 0.75rem;
}

.draggable-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.selected-article-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
  }
}

.drag-handle {
  display: flex;
  align-items: center;
  color: #64748b;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
  
  i {
    font-size: 0.875rem;
  }
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.article-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  line-height: 1.4;
}

.article-status {
  font-size: 0.75rem;
  color: #64748b;
}

.remove-btn {
  color: #ef4444 !important;
  
  &:hover {
    background: rgba(239, 68, 68, 0.1) !important;
  }
}

// Drag and drop states
.ghost-item {
  opacity: 0.5;
  background: #e2e8f0;
}

.chosen-item {
  background: #dbeafe;
  border-color: #3b82f6;
}

.drag-item {
  transform: rotate(5deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// Dropdown styling
.add-article-dropdown {
  margin-top: 0.5rem;
}

.add-article-dropdown :deep(.p-dropdown) {
  width: 100%;
  font-size: 0.813rem;
  border-radius: 8px !important;
}

.add-article-dropdown :deep(.p-dropdown:not(.p-disabled).p-focus) {
  outline: 0 none !important;
  outline-offset: 0 !important;
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.2) !important;
  border-color: #3b82f6 !important;
}

.add-article-dropdown :deep(.p-dropdown-label) {
  color: #64748b;
}

.add-article-dropdown :deep(.p-dropdown-panel) {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}
</style> 