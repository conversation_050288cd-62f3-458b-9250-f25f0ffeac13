<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import BravoFormField from './BravoFormField.vue';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import Button from 'primevue/button';
import { useKnowledgeStore } from '../stores/knowledge';
import { useToast } from 'primevue/usetoast';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get knowledge store for libraries data
const knowledgeStore = useKnowledgeStore();
const toast = useToast();

// Libraries from the store, with loading if needed
const libraries = ref<any[]>([]);
const loadingLibraries = ref(false);

// Track the edited value locally
const editedLibrary = ref('');

// Track loading state locally
const isLocalSaving = ref(false);

// Modal state for library change confirmation
const showLibraryChangeModal = ref(false);
const pendingLibraryValue = ref('');

// Reference to the BravoFormField component
const formInputRef = ref<InstanceType<typeof BravoFormField> | null>(null);

// Initialize the edited library when the article changes
watch(() => props.article.root_parent_id, (newLibraryId, oldLibraryId) => {
  console.log('LibraryFormInput: Article library ID changed from', oldLibraryId, 'to', newLibraryId);
  console.log('LibraryFormInput: Current editedLibrary value:', editedLibrary.value);
  
  const newValue = newLibraryId || '';
  console.log('LibraryFormInput: Updating editedLibrary from', editedLibrary.value, 'to', newValue);
  editedLibrary.value = newValue;
}, { immediate: true });

// Load libraries when component is mounted
const loadLibraries = async () => {
  // Check if we already have libraries in the store
  if (knowledgeStore.libraries && knowledgeStore.libraries.length > 0) {
    libraries.value = knowledgeStore.libraries;
  } else {
    // Otherwise load them
    loadingLibraries.value = true;
    try {
      const result = await knowledgeStore.fetchLibrariesOnly();
      libraries.value = result;
    } catch (error) {
      console.error('Error loading libraries:', error);
    } finally {
      loadingLibraries.value = false;
    }
  }
};

// Load libraries on mount
loadLibraries();

// Handle the update event from BravoFormField
const handleUpdate = (fieldName: string, value: any) => {
  console.log('LibraryFormInput: handleUpdate called with value:', value);
  console.log('LibraryFormInput: Current props.article.root_parent_id:', props.article.root_parent_id);
  console.log('LibraryFormInput: Current editedLibrary.value:', editedLibrary.value);
  
  // Compare against our local state instead of the potentially stale article prop
  // This prevents showing the modal when the user is just entering edit mode with the current value
  if (value !== editedLibrary.value) {
    console.log('LibraryFormInput: Values differ from current state - showing modal');
    console.log('LibraryFormInput: User selected new library:', value, 'current local state:', editedLibrary.value);
    pendingLibraryValue.value = value;
    showLibraryChangeModal.value = true;
    // Don't reset editedLibrary here - let the user see their selection
    // Only reset if they cancel the modal
  } else {
    console.log('LibraryFormInput: Values match current state - no modal needed');
  }
};

// Handle submission start
const handleSubmitStart = () => {
  // Don't set loading here since we're handling it in the modal
};

// Handle the save event from BravoFormField
const handleSave = async (fieldName: string, value: any) => {
  // For library changes, we always go through the modal confirmation
  // So if we get here, it means no change was made
  if (value === props.article.root_parent_id) {
    // No change, just complete successfully
    formInputRef.value?.handleSaveComplete(true);
  } else {
    // This shouldn't happen since we intercept changes in handleUpdate
    // But if it does, show the modal
    pendingLibraryValue.value = value;
    showLibraryChangeModal.value = true;
    // Reset the value and don't complete the save yet
    editedLibrary.value = props.article.root_parent_id || '';
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedLibrary.value = props.article.root_parent_id || '';
};

// Confirm library change
const confirmLibraryChange = async () => {
  isLocalSaving.value = true;
  try {
    const response = await knowledgeStore.updateArticleLibrary(props.article, pendingLibraryValue.value);
    if (!response.success) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: response?.message || 'Failed to update library',
        life: 3000,
      });
      throw new Error(response?.message || 'Failed to update library');
    }

    // Update the edited value to reflect the change
    console.log('LibraryFormInput: Before update - editedLibrary:', editedLibrary.value, 'pendingLibraryValue:', pendingLibraryValue.value);
    editedLibrary.value = pendingLibraryValue.value;
    console.log('LibraryFormInput: After update - editedLibrary:', editedLibrary.value);
    
    // Update the article object and emit events
    const updatedArticle = { ...props.article, root_parent_id: pendingLibraryValue.value };
    console.log('LibraryFormInput: Emitting updated article with library:', pendingLibraryValue.value);
    console.log('LibraryFormInput: Original article root_parent_id:', props.article.root_parent_id);
    console.log('LibraryFormInput: Updated article root_parent_id:', updatedArticle.root_parent_id);
    // 300 ms delay
    
    emit('update', updatedArticle);
  
    await new Promise((resolve) => setTimeout(resolve, 300));
    
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Library updated successfully',
      life: 3000,
    });
    
  
    // Clean up modal state
    pendingLibraryValue.value = '';
    showLibraryChangeModal.value = false;
    
    // Complete the form field save
    formInputRef.value?.handleSaveComplete(true);
  } catch (error: any) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to update library',
      life: 3000,
    });
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Cancel library change
const cancelLibraryChange = () => {
  showLibraryChangeModal.value = false;
  pendingLibraryValue.value = '';
  // Reset the dropdown to the original value
  editedLibrary.value = props.article.root_parent_id || '';
  console.log('LibraryFormInput: Canceled library change, reset to:', editedLibrary.value);
  // Tell the form field to cancel and revert
  formInputRef.value?.handleSaveComplete(false);
};

// Get library display name
const libraryDisplayName = computed(() => {
  return props.article.library_name || 'Unknown Library';
});
</script>

<template>
  <BravoFormField
    ref="formInputRef"
    label="Library"
    fieldName="library"
    :value="editedLibrary"
    :displayValue="libraryDisplayName"
    inputType="dropdown"
    displayType="text"
    :options="libraries"
    optionLabel="name"
    optionValue="id"
    :isLoading="isLoading || loadingLibraries"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-book"
    noValueText="Unknown Library"
    dataTestId="article-library"
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  />

  <!-- Library Change Confirmation Modal -->
  <BravoDialog
    v-model:visible="showLibraryChangeModal"
    modal
    :closable="true"
    :closeOnEscape="true"
    :dismissableMask="true"
    style="max-width: 420px"
  >
    <template #header>
      <span style="display: flex; align-items: center; gap: 1rem">
        <i class="pi pi-exclamation-triangle" style="color: #f59e42; font-size: 2rem" />
        <span style="font-weight: bold; font-size: 1.25rem">Change Article Library</span>
      </span>
    </template>
    <div style="line-height: 1.6;">
      <p style="margin-bottom: 1rem;">
        Changing the library will move this article, including all the drafts/revisions, to a different
        Knowledge Base Library.
      </p>
      <p style="font-weight: bold; margin-bottom: 1rem;">
        Moving this article will also remove all labels from every draft/revision on this article.
      </p>
      <p style="margin-bottom: 0;">Are you sure you want to proceed?</p>
    </div>
    <template #footer>
      <Button 
        label="No" 
        class="p-button-text" 
        @click="cancelLibraryChange" 
        :disabled="isLocalSaving" 
        style="min-width: 90px;" 
      />
      <Button 
        :label="isLocalSaving ? 'Saving...' : 'Yes'" 
        class="p-button-warning" 
        @click="confirmLibraryChange" 
        :loading="isLocalSaving" 
        :disabled="isLocalSaving" 
        autofocus 
        style="min-width: 120px;" 
      />
    </template>
  </BravoDialog>
</template> 