<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue';
import { ref } from 'vue';

const isEditing = ref(false);
const organizationName = ref('');
const organizationDescription = ref('');
const organizationWebsite = ref('');
const ecosystemConnections = ref('');

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
};
</script>

<template>
  <div class="settings-content">
    <div class="header-container">
      <div>
        <BravoTitlePage>General</BravoTitlePage>
        <p class="description">Configure general details about your organization.</p>
      </div>
      <BravoButton variant="primary" :label="isEditing ? 'Save' : 'Edit'" @click="toggleEdit" />
    </div>
    
    <div class="form-container" :class="{ 'editing': isEditing }">
      <BravoTitle1 class="section-header">Organization Details</BravoTitle1>
      
      <div class="form-group">
        <BravoLabel text="Organization Name" mode="primary"/>
        <BravoInputText 
          v-model="organizationName" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter organization name' : 'No name set'" 
        />
      </div>
      
      <div class="form-group">
        <BravoLabel text="Description" mode="primary"/>
        <BravoInputText 
          v-model="organizationDescription" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Describe your organization' : 'No description set'" 
        />
      </div>
      
      <div class="form-group">
        <BravoLabel text="Website" mode="primary"/>
        <BravoInputText 
          v-model="organizationWebsite" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter your organization\'s website' : 'No website set'" 
        />
      </div>
      
      <div class="form-group avatar-form-group">
        <BravoLabel text="Organization Avatar" mode="primary"/>
        <div class="avatar-wrapper">
          <BravoAvatar 
            image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png" 
            class="organization-avatar" 
          />
          <div class="avatar-overlay" v-if="isEditing">
            <i class="pi pi-pencil"></i>
          </div>
        </div>
      </div>
      
      <BravoTitle1 class="section-header">Ecosystem Details</BravoTitle1>
      
      <div class="form-group">
        <BravoLabel text="Ecosystem Connections" mode="primary"/>
        <BravoInputText 
          v-model="ecosystemConnections" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter ecosystem connections' : 'No connections set'" 
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-content {
  max-width: 800px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
}

.description {
  margin: 0;
  margin-top: 8px
}

.form-container {
  margin-top: 2rem;
}

.section-header {
  margin-bottom: 1.5rem;
}

.avatar-wrapper {
  position: relative;
  cursor: default;
}

.organization-avatar {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  transition: opacity 0.2s;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.avatar-overlay i {
  color: white;
  font-size: 1.5rem;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 0;
}

.avatar-wrapper:hover .organization-avatar {
  opacity: 1;
}

/* Only show hover effects when in editing mode */
.form-container.editing .avatar-wrapper {
  cursor: pointer;
}

.form-container.editing .avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.form-container.editing .avatar-wrapper:hover .organization-avatar {
  opacity: 0.8;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  height: 37px;
}

.avatar-form-group {
  height: auto;
  align-items: flex-start;
  margin-bottom: 24px;
}

.form-group :deep(.wrapper) {
  width: 150px;
  margin-right: 16px;
}

.form-group :deep(.bravo-inputtext) {
  width: 300px;
}

/* Remove borders and active states from readonly inputs */
.form-group :deep(.bravo-inputtext[readonly]) {
  border: none;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  padding-left: 0;
}

.form-group :deep(.bravo-inputtext[readonly]:focus) {
  outline: none;
  box-shadow: none;
  border-color: transparent;
}
</style> 