<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue';
import { ref } from 'vue';

const isEditing = ref(false);
const emailSubDomain = ref('');
const smtpRelayHost = ref('');
const forwardingAddresses = ref('');

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
};
</script>

<template>
  <div class="settings-content">
    <div class="header-container">
      <div>
        <BravoTitlePage>Email Configuration</BravoTitlePage>
        <p class="description">Configure and manage your email connections.</p>
      </div>
      <BravoButton variant="primary" :label="isEditing ? 'Save' : 'Edit'" @click="toggleEdit" />
    </div>
    
    <div class="form-container" :class="{ 'editing': isEditing }">
      <BravoTitle1 class="section-header">Email Configuration</BravoTitle1>
      
      <div class="form-group">
        <BravoLabel 
          text="Email Sub Domain" 
          mode="primary"
          iconName="info"
          toolTipText="The subdomain used for your organization's email addresses (e.g., support.yourdomain.com)"
          toolTipPosition="bottom"
        />
        <BravoInputText 
          v-model="emailSubDomain" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter email subdomain' : 'No subdomain set'"
          class="input-field"
        />
      </div>
      
      <div class="form-group">
        <BravoLabel 
          text="Email SMTP Relay Host" 
          mode="primary"
          iconName="info"
          toolTipText="The SMTP server used to relay outgoing emails"
          toolTipPosition="bottom"
        />
        <BravoInputText 
          v-model="smtpRelayHost" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter SMTP relay host (e.g., smtp.yourdomain.com)' : 'No SMTP relay host set'"
          class="input-field"
        />
      </div>
      
      <div class="form-group addresses-group">
        <BravoLabel 
          text="Email Forwarding Addresses" 
          mode="primary"
          iconName="info"
          toolTipText="Email addresses where messages will be forwarded. Enter one address per line."
          toolTipPosition="bottom"
        />
        <BravoTextarea 
          v-model="forwardingAddresses" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter email addresses (one per line)' : 'No forwarding addresses set'" 
          rows="4"
          class="addresses-input"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-content {
  max-width: 800px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
}

.description {
  margin: 0;
  margin-top: 8px
}

.form-container {
  margin-top: 2rem;
}

.section-header {
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 24px;
}

.addresses-group {
  margin-bottom: 24px;
}

.form-group :deep(.wrapper) {
  width: 200px;
  min-width: 200px;
  padding-top: 8px;
}

.form-group :deep(.bravo-inputtext),
.form-group :deep(.bravo-textarea) {
  width: 450px;
}

.addresses-input :deep(textarea) {
  min-height: 100px;
}

/* Remove borders and active states from readonly inputs */
.form-group :deep(.bravo-inputtext[readonly]),
.form-group :deep(.bravo-textarea[readonly]) {
  border: none;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  padding-left: 0;
}

.form-group :deep(.bravo-inputtext[readonly]:focus),
.form-group :deep(.bravo-textarea[readonly]:focus) {
  outline: none;
  box-shadow: none;
  border-color: transparent;
}
</style>
