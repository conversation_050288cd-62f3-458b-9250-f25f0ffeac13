<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import { ref } from 'vue';

const isEditing = ref(false);
const allowedDomains = ref('');
const passwordPolicy = ref('');
const textRedactionPatterns = ref('');

const passwordPolicyOptions = [
  { label: 'Basic (8+ characters)', value: 'basic' },
  { label: 'Standard (8+ chars, 1+ number, 1+ special char)', value: 'standard' },
  { label: 'Strong (12+ chars, upper/lower, numbers, special chars)', value: 'strong' },
  { label: 'Enterprise (16+ chars, all character types, rotation policy)', value: 'enterprise' }
];

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
};
</script>

<template>
  <div class="settings-content">
    <div class="header-container">
      <div>
        <BravoTitlePage>Security</BravoTitlePage>
        <p class="description">Configure security settings for your organization.</p>
      </div>
      <BravoButton variant="primary" :label="isEditing ? 'Save' : 'Edit'" @click="toggleEdit" />
    </div>
    
    <div class="form-container" :class="{ 'editing': isEditing }">
      <BravoTitle1 class="section-header">User Level Controls</BravoTitle1>
      
      <div class="form-group">
        <BravoLabel 
          text="Allowed User Email Domains" 
          mode="primary"
          iconName="info"
          toolTipText="Domains added here will enable you to add Users with email addresses from that domain. Leaving it blank will allow you to add users with any domain."
          toolTipPosition="bottom"
        />
        <BravoTextarea 
          v-model="allowedDomains" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter allowed domains (one per line, e.g. example.com)' : 'No domains set'" 
          rows="4"
          class="domains-input"
        />
      </div>
      
      <div class="form-group password-policy-group">
        <BravoLabel 
          text="Password Policy" 
          mode="primary"
        />
        <div class="select-container">
          <BravoSelect
            id="password-policy"
            v-model="passwordPolicy"
            :options="passwordPolicyOptions"
            placeholder="Select password policy"
            class="policy-select"
            dataTestId="password-policy-select"
            :disabled="!isEditing"
          />
          <p class="policy-description" v-if="!isEditing && !passwordPolicy">
            No password policy set
          </p>
        </div>
      </div>
      
      <BravoTitle1 class="section-header data-redaction-section">Data Redaction</BravoTitle1>
      
      <div class="form-group">
        <BravoLabel 
          text="Text Redaction Patterns" 
          mode="primary"
          iconName="info"
          toolTipText="Define regex patterns for sensitive data that should be redacted from text. Enter one pattern per line."
          toolTipPosition="bottom"
        />
        <BravoTextarea 
          v-model="textRedactionPatterns" 
          :readonly="!isEditing" 
          :placeholder="isEditing ? 'Enter regex patterns (one per line, e.g. \\d{4}-\\d{4}-\\d{4}-\\d{4})' : 'No patterns set'" 
          rows="4"
          class="patterns-input"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-content {
  max-width: 800px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
}

.description {
  margin: 0;
  margin-top: 8px
}

.form-container {
  margin-top: 2rem;
}

.section-header {
  margin-bottom: 1.5rem;
}

.data-redaction-section {
  margin-top: 2.5rem;
}

.form-group {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 24px;
}

.password-policy-group {
  margin-bottom: 24px;
}

.select-container {
  display: flex;
  flex-direction: column;
}

.form-group :deep(.wrapper) {
  width: 200px;
  min-width: 200px;
  padding-top: 8px;
}

.policy-select {
  width: 450px;
}

.policy-description {
  margin-top: 8px;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.form-group :deep(.bravo-inputtext),
.form-group :deep(.bravo-textarea) {
  width: 450px;
}

.domains-input :deep(textarea),
.patterns-input :deep(textarea) {
  min-height: 100px;
}

/* Remove borders and active states from readonly inputs */
.form-group :deep(.bravo-inputtext[readonly]),
.form-group :deep(.bravo-textarea[readonly]) {
  border: none;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  padding-left: 0;
}

.form-group :deep(.bravo-inputtext[readonly]:focus),
.form-group :deep(.bravo-textarea[readonly]:focus) {
  outline: none;
  box-shadow: none;
  border-color: transparent;
}
</style> 