<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import { ref } from 'vue';

const isEditing = ref(false);

// Workflow settings
const defaultTeam = ref(null);
const autoSendCustomerSurvey = ref(false);
const autoReopenIssue = ref(false);

// Team options for dropdown
const teamOptions = ref([
  { label: 'Development', value: 'development' },
  { label: 'Support', value: 'support' },
  { label: 'Sales', value: 'sales' }
]);

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
};
</script>

<template>
  <div class="settings-content">
    <div class="header-container">
      <div>
        <BravoTitlePage>Workflows</BravoTitlePage>
        <p class="description">Configure options for your case management workflows.</p>
      </div>
      <BravoButton variant="primary" :label="isEditing ? 'Save' : 'Edit'" @click="toggleEdit" />
    </div>
    
    <div class="form-container" :class="{ 'editing': isEditing }">
      <BravoTitle1 class="section-header">Workflow Configuration</BravoTitle1>
      
      <div class="form-group">
        <BravoLabel text="Default Team" mode="primary"/>
        <BravoSelect
          id="default-team"
          v-model="defaultTeam"
          :options="teamOptions"
          :disabled="!isEditing"
          placeholder="Select a default team"
          dataTestId="default-team-select"
        />
      </div>
      
      <div class="form-group">
        <BravoLabel text="Auto Send Customer Survey" mode="primary"/>
        <div class="toggle-wrapper">
          <input 
            type="checkbox" 
            class="toggle-input" 
            v-model="autoSendCustomerSurvey" 
            :disabled="!isEditing" 
            id="auto-survey"
          />
          <label for="auto-survey" class="toggle-label">
            {{ autoSendCustomerSurvey ? 'Yes' : 'No' }}
          </label>
        </div>
      </div>
      
      <div class="form-group">
        <BravoLabel text="Auto Reopen Case" mode="primary"/>
        <div class="toggle-wrapper">
          <input 
            type="checkbox" 
            class="toggle-input" 
            v-model="autoReopenIssue" 
            :disabled="!isEditing" 
            id="auto-reopen"
          />
          <label for="auto-reopen" class="toggle-label">
            {{ autoReopenIssue ? 'Yes' : 'No' }}
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-content {
  max-width: 800px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
}

.description {
  margin: 0;
  margin-top: 8px
}

.form-container {
  margin-top: 2rem;
}

.section-header {
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  height: 37px;
}

.form-group :deep(.wrapper) {
  width: 200px;
  margin-right: 24px;
}

/* Ensure consistent width for dropdown and toggle elements */
.form-group :deep(.p-dropdown), 
.toggle-wrapper {
  width: 300px;
}

/* Toggle switch styling */
.toggle-wrapper {
  display: flex;
  align-items: center;
}

.toggle-input {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
}

.toggle-label {
  display: inline-block;
  position: relative;
  cursor: pointer;
  height: 24px;
  width: 48px;
  background-color: #e9ecef;
  border-radius: 12px;
  transition: .4s;
  margin-right: 8px;
  padding-left: 55px;
  line-height: 24px;
}

.toggle-label:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

.toggle-input:checked + .toggle-label {
  background-color: #2196F3;
}

.toggle-input:checked + .toggle-label:before {
  transform: translateX(24px);
}

.toggle-input:disabled + .toggle-label {
  opacity: 0.6;
  cursor: not-allowed;
}
</style> 