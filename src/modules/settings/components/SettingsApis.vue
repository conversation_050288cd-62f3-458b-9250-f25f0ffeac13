<script setup lang="ts">
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { ref } from 'vue';

const isEditing = ref(false);

const toggleEdit = () => {
  isEditing.value = !isEditing.value;
};
</script>

<template>
  <div class="settings-content">
    <div class="header-container">
      <div>
        <BravoTitlePage>APIs</BravoTitlePage>
        <p class="description">Configure and manage API connections and settings.</p>
      </div>
      <BravoButton variant="primary" :label="isEditing ? 'Save' : 'Edit'" @click="toggleEdit" />
    </div>
    
    <div class="form-container" :class="{ 'editing': isEditing }">
      <BravoTitle1 class="section-header">API Configuration</BravoTitle1>
      
      <!-- Content will go here -->
    </div>
  </div>
</template>

<style scoped>
.settings-content {
  max-width: 800px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
}

.description {
  margin: 0;
  margin-top: 8px
}

.form-container {
  margin-top: 2rem;
}

.section-header {
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  height: 37px;
}

.form-group :deep(.wrapper) {
  width: 150px;
  margin-right: 16px;
}

.form-group :deep(.bravo-inputtext) {
  width: 300px;
}

/* Remove borders and active states from readonly inputs */
.form-group :deep(.bravo-inputtext[readonly]) {
  border: none;
  background-color: transparent;
  box-shadow: none;
  outline: none;
  padding-left: 0;
}

.form-group :deep(.bravo-inputtext[readonly]:focus) {
  outline: none;
  box-shadow: none;
  border-color: transparent;
}
</style>
