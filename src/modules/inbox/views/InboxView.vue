<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useCasesStore } from '../../../stores/cases'
import InboxViews from '../components/InboxViewsList.vue'
import InboxDashboard from '../components/InboxDashboard.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'

const casesStore = useCasesStore()
const route = useRoute()

// Determine if dashboard is selected based on route query
const isDashboardSelected = computed(() => route.query.view === 'dashboard')

const handleDashboardSelected = (isDashboard: boolean) => {
  // This event is still useful for other behaviors, but route determines display
  console.log('Dashboard selection event:', isDashboard)
}

onMounted(async () => {
  // Fetch views and cases first since they don't depend on the current issue
  await casesStore.fetchViews();
  await casesStore.fetchCases({ page: 1, limit: 25 });
  
  // Fetch the current issue last
  if (casesStore.issueId) {
    await casesStore.fetchCurrentIssue(casesStore.issueId);
    console.log('After fetchCurrentIssue:', casesStore.currentIssue);
  }
})

onUnmounted(() => {
  casesStore.clearCurrentIssue();
})
</script>

<template>
  <div class="inbox-view">
    <div class="inbox-content">
      <InboxViews @dashboard-selected="handleDashboardSelected" />
      
      <!-- Show full-width dashboard when dashboard is selected -->
      <div v-if="isDashboardSelected" class="dashboard-content">
        <InboxDashboard />
      </div>
      
      <!-- Show normal main content when dashboard is not selected -->
      <div v-else class="main-content">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
          <template v-if="!Component">
            <div class="zero-state-container">
              <BravoZeroStateScreen
                title="Get Started"
                message="Select a case or task from the sidebar to get started."
                imageIcon="pi pi-inbox"
              />
            </div>
          </template>
        </router-view>
      </div>
    </div>
  </div>
</template>

<style scoped>
.inbox-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.inbox-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--surface-200);
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.back-link {
  margin-bottom: 1rem;
  color: var(--primary-color);
  cursor: pointer;
  display: inline-block;
}

.back-link:hover {
  text-decoration: underline;
}

.events-toggle-btn {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.events-toggle-btn:hover {
  background-color: var(--primary-600);
}

.current-issue-header {
  margin-bottom: 1rem;
}

.current-issue-header h2 {
  font-size: 1.25rem;
  color: var(--surface-900);
  margin: 0;
}

.inbox-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.main-content {
  flex: 1;
  /* padding: 2rem; */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.dashboard-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
}

.error-container {
  color: var(--red-500);
  padding: 1rem;
  border-radius: 4px;
  background-color: var(--red-50);
}

.issue-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.issue-field {
  margin-bottom: 0.75rem;
}

.issue-field:last-child {
  margin-bottom: 0;
}

.issue-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  color: var(--surface-600);
  padding: 2rem;
}

.empty-child-route {
  text-align: center;
  padding: 2rem;
  color: var(--surface-600);
}

.zero-state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 2rem;
}
</style> 