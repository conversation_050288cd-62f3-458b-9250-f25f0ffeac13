export interface Communication {
  id: string;
  title: string;
  type: 'chat' | 'email' | 'sms' | 'voice';
  roomJid?: string;
  participants: Participant[];
  messages: Message[];
  customerTyping?: boolean;
  lastActivity?: Date;
  status?: 'active' | 'closed' | 'pending';
}

export interface Participant {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  role?: 'customer' | 'agent' | 'system';
}

export interface Message {
  id: string;
  senderId: string;
  senderName?: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'file' | 'system';
  metadata?: {
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    fileType?: string;
  };
}

export interface AppConfig {
  serviceConfig: {
    jid: string;
    password: string;
    websocket_host: string;
    resource: string;
    relay_avatar?: string;
  };
} 