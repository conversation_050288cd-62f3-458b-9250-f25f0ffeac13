/**
 * Communication Types
 * 
 * This file contains all TypeScript interfaces for the communication system,
 * including available communications, detailed communication data, and participants.
 */

// Base communication type mapping
export type CommunicationType = 'chat' | 'email' | 'sms' | 'voice';

export const COMMUNICATION_TYPE_MAP: Record<number, CommunicationType> = {
  0: 'chat',
  1: 'email', 
  2: 'sms',
  3: 'voice'
} as const;

// Communication status mapping
export type CommunicationStatus = 'active' | 'inactive' | 'occupied' | 'unoccupied' | 'closed';
export type CommunicationState = 'active' | 'inactive' | 'agent_active' | 'customer_active';

// Available Communication (from case.availableComms)
export interface AvailableComm {
  id: string;
  object: 'issues';
  object_id: string;
  object_scope: 'private' | 'public' | 'email';
  comm_type: 0 | 1 | 2 | 3; // Maps to CommunicationType
  comm_label: string;
  comm_status: number;
  comm_state: number;
  object_source: string;
  billing_status: number;
  title: string;
  subtitle: string;
  duration_plus: number;
  duration: number;
  user_message_cnt: Record<string, number>;
  external_rpid: string;
  external_lpid: string;
  external_id: string;
  external_status: string;
  created: string; // ISO date string
  updated: string; // ISO date string
  occupied: string; // ISO date string
  completed: string; // ISO date string or "0000-00-00 00:00:00"
  c__status: string; // Human readable status
  c__state: string; // Human readable state
  _highlighted: boolean;
  _highlightmap: Record<string, any>;
  c__avatar: string; // URL
  unread_count: number;
}

// Participant types
export type ParticipantObjectType = 'users' | 'members_users' | 'bots' | 'relay';

export interface CommParticipant {
  object: ParticipantObjectType;
  object_id: string;
  name: string;
  alias: string;
  external_id: string; // XMPP JID
  host: boolean;
  eligible: boolean;
  id: string; // Format: "object:object_id"
  presence: boolean;
}

// Contact data structure
export interface ContactUser {
  val: string; // Participant ID
  sms_number: string | null;
  phone_number: string | null;
  email: string;
  lbl_sms: string;
  lbl_phone: string;
  lbl_email: string;
}

export interface ContactData {
  users: ContactUser[];
  sms: string | null;
  email: string;
  phone: string | null;
  sms_did: string | null;
}

// Full Communication Details (from API response)
export interface CommData {
  id: string;
  object: 'issues';
  object_id: string;
  object_scope: 'private' | 'public' | 'email';
  comm_type: 0 | 1 | 2 | 3;
  comm_label: string;
  comm_status: number;
  comm_state: number;
  object_source: string;
  billing_status: number;
  title: string;
  subtitle: string;
  duration_plus: number;
  duration: number;
  user_message_cnt: Record<string, number>;
  external_rpid: string;
  external_lpid: string;
  external_id: string; // XMPP room JID
  external_status: string;
  created: string; // ISO date string
  updated: string; // ISO date string
  occupied: string; // ISO date string
  completed: string; // ISO date string or "0000-00-00 00:00:00"
  c__status: string;
  c__state: string;
  c__avatar: string; // URL
  email_address: string;
  participants: CommParticipant[];
  files: any[]; // TODO: Define file type
  hasAutoForwarding: boolean;
  autoForwardDest: string;
  contactData: ContactData;
  buttonRedactPatterns: any[]; // TODO: Define pattern type
  autoRedactPatterns: any[]; // TODO: Define pattern type
  cc_recipients: any; // TODO: Define type
  last_sender: any; // TODO: Define type
}

// API Request/Response types
export interface GetCommParams {
  id: string;
  is_enter?: boolean;
}

export interface GetCommResponse {
  success: boolean;
  comm: CommData;
  current_server_time: string; // ISO date string
}

export interface UpdateCommPresenceParams {
  id: string;
  presence: boolean;
}

export interface UpdateCommPresenceResponse {
  success: boolean;
  comm: CommData;
  current_server_time: string; // ISO date string
}

// Utility types
export interface CachedComm {
  id: string;
  data: CommData;
  lastFetched: Date;
  participants: Map<string, CommParticipant>; // external_id -> participant
}

// Helper functions
export function getCommunicationType(commType: number): CommunicationType {
  return COMMUNICATION_TYPE_MAP[commType] || 'chat';
}

export function getCommunicationTypeFromScope(objectScope: string): CommunicationType {
  switch (objectScope) {
    case 'email':
      return 'email';
    case 'private':
    case 'public':
      return 'chat';
    default:
      return 'chat';
  }
}

export function isCommCompleted(completed: string): boolean {
  return completed !== '0000-00-00 00:00:00' && completed !== '';
}

export function isCommActive(commStatus: number): boolean {
  return commStatus === 1;
}

export function getParticipantDisplayName(participant: CommParticipant): string {
  return participant.name || participant.alias || participant.id;
}

export function getParticipantsByType(participants: CommParticipant[]): Record<ParticipantObjectType, CommParticipant[]> {
  return participants.reduce((acc, participant) => {
    const type = participant.object;
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(participant);
    return acc;
  }, {} as Record<ParticipantObjectType, CommParticipant[]>);
}

export function findParticipantById(participants: CommParticipant[], id: string): CommParticipant | undefined {
  return participants.find(p => p.id === id);
}

export function findParticipantByExternalId(participants: CommParticipant[], externalId: string): CommParticipant | undefined {
  return participants.find(p => p.external_id === externalId);
} 