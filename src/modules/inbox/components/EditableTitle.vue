<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'

interface Props {
  modelValue: string
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'save', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const isEditing = ref(false)
const editValue = ref('')
const inputRef = ref()
const isSaving = ref(false)

// Watch for changes to start editing
watch(() => props.modelValue, (newValue) => {
  if (!isEditing.value) {
    editValue.value = newValue
  }
})

async function startEditing() {
  editValue.value = props.modelValue
  isEditing.value = true
  
  await nextTick()
  if (inputRef.value) {
    inputRef.value.$el?.querySelector('input')?.focus()
    inputRef.value.$el?.querySelector('input')?.select()
  }
}

function cancelEditing() {
  isEditing.value = false
  editValue.value = props.modelValue
  isSaving.value = false
}

async function saveEditing() {
  if (editValue.value.trim() && editValue.value !== props.modelValue) {
    isSaving.value = true
    try {
      emit('save', editValue.value.trim())
      // Wait a bit for the save to complete before hiding the editing state
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Error saving:', error)
    } finally {
      isSaving.value = false
    }
  }
  isEditing.value = false
}

function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    event.preventDefault()
    saveEditing()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEditing()
  }
}

function handleBlur() {
  // Small delay to allow click events to fire first
  setTimeout(() => {
    if (isEditing.value && !isSaving.value) {
      saveEditing()
    }
  }, 100)
}
</script>

<template>
  <div class="editable-title">
    <BravoTitle1 
      v-if="!isEditing"
      class="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors"
      @click="startEditing"
      :title="'Click to edit: ' + modelValue"
    >
      {{ modelValue || 'No Title' }}
    </BravoTitle1>
    
    <div v-else class="editing-container">
      <BravoInputText
        ref="inputRef"
        v-model="editValue"
        class="title-input"
        :disabled="loading || isSaving"
        @keydown="handleKeydown"
        @blur="handleBlur"
        :style="{ fontSize: '1.5rem', fontWeight: '600', padding: '0.25rem 0.5rem' }"
      />
      
      <!-- Loading spinner -->
      <div v-if="isSaving || loading" class="loading-spinner">
        <i class="pi pi-spin pi-spinner" style="font-size: 1rem; color: #3b82f6;"></i>
      </div>
    </div>
  </div>
</template>

<style scoped>
.editable-title {
  display: inline-block;
}

.editing-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-input {
  min-width: 300px;
}

.title-input :deep(input) {
  font-size: 1.5rem;
  font-weight: 600;
  border: 2px solid #3b82f6;
  border-radius: 4px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
</style> 