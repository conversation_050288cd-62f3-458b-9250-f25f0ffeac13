<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoPaginator from '@services/ui-component-library/components/BravoPaginator.vue'
import Skeleton from 'primevue/skeleton'
import { useKnowledgeStore } from '../../knowledge/stores/knowledge'

const props = defineProps<{ issue: any }>()
const store = useKnowledgeStore()
const searchTerm = ref('')
const page = ref(1)
const rows = 10
const loading = ref(false)
const selectedArticle = ref<any | null>(null)

const articles = computed(() => store.currentList.slice(0, rows))
const totalRecords = computed(() => store.totalCount)

async function searchKnowledge() {
  loading.value = true
  await store.fetchKnowledgeListing(
    { rootParentId: null, parentId: null },
    {
      page: page.value,
      start: (page.value - 1) * rows,
      limit: rows,
      filter: [
        { property: 'type', value: 0 },
        { property: 'id', value: '_no_filter_' },
        { property: 'parent_id', value: null },
        { property: 'root_parent_id', value: null },
        { property: 'partner_ids', operator: 'intersect_set', value: null },
        { property: 'visibility', operator: '=', value: null },
        { property: 'status', operator: '=', value: null },
        { property: 'owner_partner_id', value: '_no_filter_' },
        { property: 'dict_id', value: null },
        { property: 'tag_ids', value: null }
      ],
      query: searchTerm.value.trim() || undefined
    }
  )
  loading.value = false
}

function onPageChange(e: { page: number }) {
  page.value = e.page + 1
  searchKnowledge()
}

function openArticle(article: any) {
  selectedArticle.value = article
}

function goBackToSearch() {
  selectedArticle.value = null
}

onMounted(() => {
  searchKnowledge()
})
</script>
<template>
  <div class="case-kb-container" style="border-left-color: var(--border-color);">
    <!-- Fixed Header -->
    <div class="case-kb-header px-4 pt-5 pb-2 bg-white">
      <BravoTitle1>Knowledge</BravoTitle1>
    </div>
    
    <!-- Scrollable Body -->
    <div class="case-kb-body">
      <template v-if="selectedArticle">
        <div class="p-4 bg-white border-b border-slate-200 flex items-center">
          <BravoButton label="Back to Search" icon="pi pi-arrow-left" @click="goBackToSearch" severity="secondary" class="mr-2" />
        </div>
        <iframe
          :src="`${selectedArticle.link || selectedArticle.url}?viewmode=embedded`"
          class="w-full flex-1 h-full min-h-[60vh] border-0"
          style="background:white;"
        ></iframe>
      </template>
      <template v-else>
        <div class="p-4 bg-white flex gap-2 items-center">
          <BravoInputText
            v-model="searchTerm"
            placeholder="Search knowledge articles..."
            class="w-full"
            :disabled="loading"
            @keyup.enter="searchKnowledge"
          />
          <BravoButton label="Search" icon="pi pi-search" @click="searchKnowledge" :disabled="loading || !searchTerm.trim()" />
        </div>
        <div class="flex-1 overflow-y-auto px-4 pb-4 bg-white">
          <div v-if="loading" class="mb-4">
            <div v-for="i in 5" :key="i" class="border rounded mb-2 p-3">
              <Skeleton width="80%" height="1.25rem" class="mb-2" />
              <div class="flex gap-2 mt-1">
                <Skeleton width="60px" height="0.75rem" />
                <Skeleton width="1px" height="0.75rem" />
                <Skeleton width="80px" height="0.75rem" />
              </div>
            </div>
          </div>
          <div v-else>
            <div v-if="articles.length === 0" class="text-slate-500 italic mb-4">No articles found.</div>
            <div v-else>
              <div v-for="article in articles" :key="article.id" class="border rounded mb-2 p-3 cursor-pointer hover:bg-slate-50 transition" @click="openArticle(article)">
                <div class="font-semibold text-slate-800">{{ article.title || 'Untitled' }}</div>
                <div class="text-xs text-slate-500 flex gap-2 mt-1">
                  <span>{{ article.access || 'Unknown' }}</span>
                  <span v-if="article.updatedAt">| {{ new Date(article.updatedAt).toLocaleDateString() }}</span>
                </div>
              </div>
            </div>
            <BravoPaginator
              :rows="rows"
              :totalRecords="totalRecords"
              :first="(page - 1) * rows"
              @page="onPageChange"
              class="mt-2"
            />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<style scoped>
.case-kb-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  border-left: 1px solid var(--border-color);
}
.case-kb-header {
  flex-shrink: 0;
  background: white;
  height: 48px;
  display: flex;
  align-items: center;
}
.case-kb-body {
  flex: 1;
  overflow-y: auto;
  background: white;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
</style> 