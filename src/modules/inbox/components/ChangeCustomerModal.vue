<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoBodyBold from '@services/ui-component-library/components/BravoTypography/BravoBodyBold.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import AutoComplete from 'primevue/autocomplete'
import { useToast } from 'primevue/usetoast'
import { useMemberStore } from '../../../stores/member'
import type { Issue } from '../../../services/IssuesAPI'
import type { MemberLocation } from '../../../composables/services/useMemberAPI'

interface Props {
  visible: boolean
  issue: Issue | null
  onSubmit?: (selectedCustomer: MemberLocation) => Promise<void>
  onCancel?: () => void
}

const props = withDefaults(defineProps<Props>(), {})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const memberStore = useMemberStore()
const toast = useToast()
const selectedCustomer = ref<MemberLocation | null>(null)
const isLoading = ref(false)
const searchQuery = ref('')

// Helper function to strip HTML tags
const stripHtml = (html: string) => {
  if (!html) return ''
  const div = document.createElement('div')
  div.innerHTML = html
  return div.textContent || div.innerText || ''
}

// Computed property for clean option labels (without HTML)
const getCleanLabel = (option: any) => {
  return stripHtml(option.c__name) || stripHtml(option.site_name) || 'Unknown Customer'
}

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleConfirm = async () => {
  if (!props.onSubmit || !selectedCustomer.value) return
  
  isLoading.value = true
  
  try {
    await props.onSubmit(selectedCustomer.value)
    // Close modal on success
    emit('update:visible', false)
    selectedCustomer.value = null
  } catch (error) {
    console.error('Error changing customer:', error)
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while changing customer'
    toast.add({ 
      severity: 'error', 
      summary: 'Failed to Change Customer', 
      detail: errorMessage,
      life: 5000
    })
    // Keep modal open on error so user can retry
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  selectedCustomer.value = null
  if (props.onCancel) {
    props.onCancel()
  } else {
    emit('update:visible', false)
  }
}

const handleDialogHide = () => {
  if (isLoading.value) return // Prevent closing when loading
  selectedCustomer.value = null
  emit('update:visible', false)
}

// Handle search input changes
const onFilter = async (event: any) => {
  const query = event.query || ''
  searchQuery.value = query
  
  if (query && query.length >= 2) {
    try {
      await memberStore.searchCustomerLocations(query)
    } catch (error) {
      console.error('Error searching customers:', error)
    }
  } else if (query.length === 0) {
    // Clear results when search is empty
    memberStore.clearCustomerLocations()
  }
}

// Watch for visible prop changes to reset form when modal opens
watch(() => props.visible, (newVisible, oldVisible) => {
  if (newVisible && !oldVisible) {
    // Modal is opening, reset form
    selectedCustomer.value = null
    searchQuery.value = ''
    isLoading.value = false
    // Clear previous search results
    memberStore.clearCustomerLocations()
  }
})
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    header="Change Customer"
    :style="{ width: '500px' }"
    :closable="true"
    :close-on-escape="!isLoading"
    @hide="handleDialogHide"
    :class="['change-customer-modal', { loading: isLoading }]"
  >
    <div class="change-customer-content">
      <BravoBody class="instruction-text">
        Search and select a new customer for this case.
      </BravoBody>
      
      <div class="customer-search-section">
        <label for="customer-search" class="search-label">
          <BravoBodyBold>Search Customers</BravoBodyBold>
        </label>
        <AutoComplete
          id="customer-search"
          v-model="selectedCustomer"
          :suggestions="memberStore.customerLocations"
          :optionLabel="getCleanLabel"
          placeholder="Type to search customers..."
          :loading="memberStore.loadingCustomerLocations"
          :disabled="isLoading"
          class="w-full"
          @complete="onFilter"
          :showClear="true"
          :minLength="2"
          fluid
        >
          <template #option="slotProps">
            <div class="customer-option">
              <div class="customer-main-info">
                <div class="customer-name" v-html="slotProps.option.c__name || slotProps.option.site_name"></div>
                <div class="customer-details">
                  <span class="customer-address">{{ slotProps.option.c__address }}</span>
                  <span v-if="slotProps.option.merchant_ids" class="merchant-id">
                    MID: <span v-html="slotProps.option.merchant_ids"></span>
                  </span>
                  <span v-if="slotProps.option.c__case_summary" class="case-summary">
                    {{ slotProps.option.c__case_summary }}
                  </span>
                </div>
              </div>
              <div class="customer-meta">
                <div class="customer-id">#{{ slotProps.option.id }}</div>
                <div v-if="slotProps.option.c__phone" class="customer-phone">
                  {{ slotProps.option.c__phone }}
                </div>
              </div>
            </div>
          </template>
          
          <template #empty>
            <div class="empty-state">
              <BravoBody v-if="searchQuery.length < 2">
                Type at least 2 characters to search
              </BravoBody>
              <BravoBody v-else>
                No customers found for "{{ searchQuery }}"
              </BravoBody>
            </div>
          </template>
        </AutoComplete>
      </div>
      
      <div v-if="selectedCustomer && selectedCustomer.id" class="selected-customer-info">
        <BravoBodyBold>Selected Customer:</BravoBodyBold>
        <div class="customer-card">
          <div class="customer-header">
            <div class="customer-name" v-html="selectedCustomer.c__name || selectedCustomer.site_name"></div>
            <div class="customer-id">#{{ selectedCustomer.id }}</div>
          </div>
          <div class="customer-details">
            <div class="customer-address">{{ selectedCustomer.c__address }}</div>
            <div v-if="selectedCustomer.merchant_ids" class="customer-merchant-id">
              MID: <span v-html="selectedCustomer.merchant_ids"></span>
            </div>
            <div v-if="selectedCustomer.c__phone" class="customer-phone">
              Phone: {{ selectedCustomer.c__phone }}
            </div>
            <div v-if="selectedCustomer.c__case_summary" class="case-summary">
              {{ selectedCustomer.c__case_summary }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          label="Change Customer"
          severity="primary"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!selectedCustomer"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
/* Fix modal border radius and prevent resizing */
:deep(.change-customer-modal .p-dialog) {
  border-radius: 8px !important;
}

:deep(.change-customer-modal .p-dialog-content) {
  border-radius: 0 0 8px 8px !important;
}

:deep(.change-customer-modal .p-dialog-header) {
  border-radius: 8px 8px 0 0 !important;
}

/* Disable close button when loading instead of hiding it */
:deep(.change-customer-modal.loading .p-dialog-header-close) {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.change-customer-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.instruction-text {
  margin-bottom: 0.5rem;
  color: var(--surface-600);
}

.customer-search-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.search-label {
  margin-bottom: 0.25rem;
}

.customer-option {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem;
  gap: 1rem;
}

.customer-main-info {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-weight: 600;
  color: var(--surface-900);
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.customer-address {
  font-size: 0.875rem;
  color: var(--surface-600);
  line-height: 1.3;
}

.merchant-id {
  font-size: 0.75rem;
  color: var(--primary-600);
  font-weight: 500;
  font-family: monospace;
}

.case-summary {
  font-size: 0.75rem;
  color: var(--surface-500);
  font-style: italic;
}

.customer-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.125rem;
  flex-shrink: 0;
}

.customer-id {
  font-size: 0.75rem;
  color: var(--surface-500);
  font-family: monospace;
}

.customer-phone {
  font-size: 0.75rem;
  color: var(--surface-600);
}

.selected-customer {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.customer-info .customer-name {
  font-weight: 500;
  margin-bottom: 0.125rem;
}

.customer-info .customer-address {
  font-size: 0.875rem;
  color: var(--surface-600);
}

.placeholder-text {
  color: var(--surface-500);
}

.selected-customer-info {
  padding: 1rem;
  background-color: var(--surface-50);
  border-radius: 6px;
  border-left: 4px solid var(--primary-500);
}

.customer-card {
  margin-top: 0.5rem;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.customer-header .customer-name {
  font-weight: 600;
  color: var(--surface-900);
}

.customer-header .customer-id {
  font-size: 0.875rem;
  color: var(--surface-500);
  font-family: monospace;
}

.customer-card .customer-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-card .customer-address {
  color: var(--surface-700);
}

.customer-card .customer-merchant-id {
  font-size: 0.875rem;
  color: var(--primary-600);
  font-weight: 500;
  font-family: monospace;
}

.customer-card .customer-phone {
  font-size: 0.875rem;
  color: var(--surface-600);
}

.customer-card .case-summary {
  font-size: 0.875rem;
  color: var(--surface-500);
  font-style: italic;
}

.empty-state {
  padding: 1rem;
  text-align: center;
  color: var(--surface-500);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Fix button width consistency to prevent layout shifts */
.modal-actions :deep(.p-button) {
  min-width: 120px;
  height: 40px;
}

.modal-actions :deep(.p-button-label) {
  white-space: nowrap;
}

/* Custom dropdown styling */
:deep(.p-select-overlay) {
  max-height: 300px;
}

/* Highlight styling for search results */
:deep(.highlight) {
  background-color: #fef3c7;
  color: #92400e;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
}

/* Selected chip styling */
.selected-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.chip-name {
  font-weight: 500;
  color: var(--surface-900);
}

.chip-mid {
  font-size: 0.875rem;
  color: var(--primary-600);
  font-family: monospace;
  font-weight: 500;
}

:deep(.p-select-option) {
  padding: 0 !important;
}

:deep(.p-select-option:hover) {
  background-color: var(--surface-100);
}

:deep(.p-select-option[aria-selected="true"]) {
  background-color: var(--primary-50);
  color: var(--primary-700);
}
</style> 