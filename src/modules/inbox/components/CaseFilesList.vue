<template>
  <div class="files-list">
    <div v-if="loading" class="files-loading">
      <BravoSkeleton v-for="i in 3" :key="i" width="100%" height="60px" class="mb-2" />
    </div>
    
    <div v-else-if="files.length === 0" class="no-files">
      <p class="no-files-text">No files attached to this case.</p>
    </div>
    
    <div v-else class="files-grid">
      <div 
        v-for="file in files" 
        :key="file.id" 
        class="file-item"
        @click="openFile(file)"
      >
        <div class="file-icon">
          <BravoSkeleton 
            v-if="file.thumbnail && (file.type.startsWith('image/') || file.type === 'application/pdf') && !failedThumbnails.has(file.id) && loadingThumbnails.has(file.id)"
            width="100%" 
            height="100%" 
            border-radius="4px"
          />
          <img 
            v-else-if="file.thumbnail && (file.type.startsWith('image/') || file.type === 'application/pdf') && !failedThumbnails.has(file.id)"
            :src="file.thumbnail"
            :alt="file.name"
            class="file-thumbnail"
            @load="handleThumbnailLoad(file.id)"
            @error="handleThumbnailError(file.id)"
            @loadstart="handleThumbnailLoadStart(file.id)"
          />
          <i v-else :class="getFileIcon(file.type)" class="file-type-icon"></i>
        </div>
        <div class="file-details">
          <div class="file-name" :title="file.name">{{ file.c__d__name }}</div>
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <span class="file-date">{{ file.c__d__created }}</span>
          </div>
          <div v-if="file.c__d__fullname" class="file-uploader">
            Uploaded by {{ file.c__d__fullname }}
          </div>
        </div>
        <div class="file-actions">
          <BravoButton
            icon="pi pi-download"
            severity="secondary"
            size="small"
            @click.stop="downloadFile(file)"
            :title="`Download ${file.name}`"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import type { FileItem } from '@/composables/services/useFilesAPI'

const props = defineProps<{
  files: FileItem[]
  loading?: boolean
}>()

// Track files with failed thumbnails
const failedThumbnails = ref<Set<string>>(new Set())
const loadingThumbnails = ref<Set<string>>(new Set())

// Methods
function getFileIcon(mimeType: string): string {
  if (mimeType.startsWith('image/')) {
    return 'pi pi-image'
  } else if (mimeType === 'application/pdf') {
    return 'pi pi-file-pdf'
  } else if (mimeType.includes('word') || mimeType.includes('document')) {
    return 'pi pi-file-word'
  } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
    return 'pi pi-file-excel'
  } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
    return 'pi pi-file'
  } else if (mimeType.startsWith('video/')) {
    return 'pi pi-video'
  } else if (mimeType.startsWith('audio/')) {
    return 'pi pi-volume-up'
  } else if (mimeType.includes('zip') || mimeType.includes('archive')) {
    return 'pi pi-file-archive'
  } else {
    return 'pi pi-file'
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function openFile(file: FileItem) {
  // Open file in new tab
  window.open(file.file, '_blank')
}

function downloadFile(file: FileItem) {
  // Create a temporary link to download the file
  const link = document.createElement('a')
  link.href = file.file
  link.download = file.name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

function handleThumbnailError(id: string) {
  // Handle thumbnail error
  console.error(`Error loading thumbnail for file with id: ${id}`)
  failedThumbnails.value.add(id)
}

function handleThumbnailLoad(id: string) {
  // Handle thumbnail load
  loadingThumbnails.value.delete(id)
}

function handleThumbnailLoadStart(id: string) {
  // Handle thumbnail load start
  loadingThumbnails.value.add(id)
}
</script>

<style scoped>
.files-list {
  padding: 0;
}

.files-loading {
  padding: 1rem 0;
}

.no-files {
  padding: 2rem 1rem;
  text-align: center;
}

.no-files-text {
  margin: 0;
  color: var(--surface-500);
  font-style: italic;
}

.files-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  background: var(--surface-0);
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: var(--primary-color);
  background: var(--primary-50);
}

.file-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface-100);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--surface-200);
}

.file-type-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 600;
  color: var(--surface-900);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--surface-600);
  margin-bottom: 0.25rem;
}

.file-uploader {
  font-size: 0.75rem;
  color: var(--surface-500);
}

.file-actions {
  flex-shrink: 0;
}

.file-actions .p-button {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.file-item:hover .file-actions .p-button {
  opacity: 1;
}

.file-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
</style> 