<template>
  <BravoDialog
    :visible="visible"
    @update:visible="(value) => emit('update:visible', value)"
    header="Escalate Case"
    :modal="true"
    :style="{ width: '500px', minHeight: '400px' }"
    :closable="true"
    :closeOnEscape="!isLoading"
    :dismissableMask="!isLoading"
  >
    <div class="escalate-case-content">
      <div class="status-display">
        <span class="status-label">Status:</span>
        <span class="status-value">Escalated</span>
      </div>
      
      <div class="form-fields">
        <!-- Escalation Team -->
        <div class="field">
          <BravoLabel text="Escalation Team" required />
          <Dropdown
            v-model="escalationTeamId"
            :options="teams"
            optionLabel="lbl"
            optionValue="val"
            placeholder="Select escalation team"
            :filter="true"
            :loading="loadingTeams"
            class="w-full"
            :disabled="isLoading"
          />
        </div>

        <!-- Owner User -->
        <div class="field">
          <BravoLabel text="Owner User" />
          <Dropdown
            v-model="escalationUserId"
            :options="filteredUsers"
            optionLabel="lbl"
            optionValue="val"
            placeholder="Select owner user"
            :filter="true"
            :loading="loadingUsers"
            class="w-full"
            :disabled="isLoading"
          />
        </div>

        <!-- Reason For Escalation -->
        <div class="field">
          <BravoLabel text="Reason For Escalation" required />
          <Dropdown
            v-model="escalationReasonId"
            :options="escalationReasons"
            optionLabel="lbl"
            optionValue="val"
            placeholder="Select escalation reason"
            :filter="true"
            :loading="loadingReasons"
            class="w-full"
            :disabled="isLoading"
          />
        </div>

        <!-- Escalation Notes -->
        <div class="field">
          <BravoLabel text="Escalation Notes" />
          <BravoTextarea
            v-model="escalationNotes"
            rows="4"
            placeholder="Enter escalation notes..."
            class="w-full"
            :disabled="isLoading"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          :label="isLoading ? 'Escalating...' : 'Escalate'"
          severity="primary"
          @click="handleSubmit"
          :loading="isLoading"
          :disabled="isLoading || !isFormValid"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import Dropdown from 'primevue/dropdown'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import type { Issue } from '../../../services/IssuesAPI'
import { useMetaStore } from '@/stores/meta'
import { useCasesStore } from '@/stores/cases'

const props = defineProps<{
  visible: boolean
  issue: Issue | null
  onSubmit?: (params: {
    escalationTeamId: string
    escalationUserId?: string
    escalationReasonId: string
    escalationNotes?: string
  }) => Promise<void>
  onCancel?: () => void
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const metaStore = useMetaStore()
const casesStore = useCasesStore()

// Form state
const escalationTeamId = ref('')
const escalationUserId = ref('')
const escalationReasonId = ref('')
const escalationNotes = ref('')
const isLoading = ref(false)

// Data loading states
const loadingTeams = ref(false)
const loadingUsers = ref(false)
const loadingReasons = ref(false)

// Computed properties
const teams = computed(() => {
  return casesStore.issuePartnerTeams?.pl__issues_partners_teams || []
})

const escalationReasons = computed(() => {
  // Filter tags for category 51 (escalation reasons)
  return metaStore.metaData?.pl__tags?.filter((tag: any) => tag.category === 51) || []
})

const filteredUsers = computed(() => {
  if (!escalationTeamId.value || !metaStore.partnerMetaData?.pl__partners_users) {
    return []
  }
  
  // Find the selected team to get its partners_id
  const selectedTeam = teams.value.find(team => team.val === escalationTeamId.value)
  if (!selectedTeam) {
    return []
  }
  
  // Filter users by the selected team's partners_id
  return metaStore.partnerMetaData.pl__partners_users.filter((user: any) => 
    user.partners_id === selectedTeam.partners_id
  ) || []
})

const isFormValid = computed(() => {
  return escalationTeamId.value && escalationReasonId.value
})

// Watch for modal visibility changes
watch(() => props.visible, (newValue) => {
  if (newValue) {
    resetForm()
    loadData()
  }
})

// Watch for team selection changes to clear user selection
watch(escalationTeamId, () => {
  escalationUserId.value = ''
})

// Methods
function resetForm() {
  escalationTeamId.value = ''
  escalationUserId.value = ''
  escalationReasonId.value = ''
  escalationNotes.value = ''
}

async function loadData() {
  await Promise.all([
    loadTeams(),
    loadUsers(),
    loadEscalationReasons()
  ])
}

async function loadTeams() {
  if (!props.issue?.members_locations_id) {
    console.warn('No members_locations_id available for issue')
    return
  }
  
  loadingTeams.value = true
  try {
    await casesStore.fetchIssuePartnerTeams(props.issue.members_locations_id, props.issue.id)
  } catch (error) {
    console.error('Error loading teams:', error)
  } finally {
    loadingTeams.value = false
  }
}

async function loadUsers() {
  loadingUsers.value = true
  try {
    // Ensure partner metadata is loaded
    if (!metaStore.partnerMetaData) {
      await metaStore.loadPartnerMetaData()
    }
  } catch (error) {
    console.error('Error loading users:', error)
  } finally {
    loadingUsers.value = false
  }
}

async function loadEscalationReasons() {
  loadingReasons.value = true
  try {
    // Ensure meta data is loaded for escalation reasons
    if (!metaStore.metaData) {
      await metaStore.loadMetaData()
    }
  } catch (error) {
    console.error('Error loading escalation reasons:', error)
  } finally {
    loadingReasons.value = false
  }
}

async function handleSubmit() {
  if (!isFormValid.value || !props.onSubmit) return
  
  isLoading.value = true
  try {
    await props.onSubmit({
      escalationTeamId: escalationTeamId.value,
      escalationUserId: escalationUserId.value || undefined,
      escalationReasonId: escalationReasonId.value,
      escalationNotes: escalationNotes.value || undefined
    })
    
    emit('update:visible', false)
  } catch (error) {
    console.error('Error escalating case:', error)
    // Error handling is done in the parent component
  } finally {
    isLoading.value = false
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel()
  }
  emit('update:visible', false)
}

// Load initial data on mount
onMounted(() => {
  loadEscalationReasons()
})
</script>

<style scoped>
.escalate-case-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
}

.status-label {
  font-weight: 600;
  color: #92400e;
}

.status-value {
  font-weight: 700;
  color: #d97706;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

/* Ensure consistent button sizing */
.modal-footer .p-button {
  min-height: 40px;
}
</style> 