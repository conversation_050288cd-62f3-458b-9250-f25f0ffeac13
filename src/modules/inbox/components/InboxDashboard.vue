<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useCasesStore } from '../../../stores/cases'
import { useUserStore } from '@/stores/user'
import { useTasksStore } from '../../../stores/tasks'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'

// Props to match other inbox components
interface Props {
  selectedView?: any
  initialLoading?: boolean
  sidebarWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
  sidebarWidth: 280
})

// Dashboard state
const isLoading = ref(false)
const casesStore = useCasesStore()
const userStore = useUserStore()
const tasksStore = useTasksStore()

// Metrics data
const myOpenCasesCount = ref(0)
const myOpenTasksCount = ref(0)
const teamUnassignedCasesCount = ref(0)

// Fetch My Open Cases count (same logic as "Your Inbox")
const fetchMyOpenCasesCount = async () => {
  try {
    const params: any = {
      filter: [
        {
          property: 'owner_users_id',
          value: userStore.userData?.id || '',
          operator: 'eq'
        },
        {
          property: 'status',
          value: ['2', '1'], // Ready and New
          operator: 'in'
        }
      ],
      sort: [
        {
          property: 'updated',
          direction: 'DESC'
        }
      ]
    }
    
    const { useIssuesAPI } = await import('@/composables/services')
    const issuesAPI = useIssuesAPI()
    const result = await issuesAPI.fetchCases(params)
    
    myOpenCasesCount.value = result.totalCount || 0
  } catch (err) {
    console.error('Error fetching My Open Cases count:', err)
    myOpenCasesCount.value = 0
  }
}

// Fetch My Open Tasks count (same logic as "My Open Tasks" view)
const fetchMyOpenTasksCount = async () => {
  try {
    const userId = userStore.userData?.id || ''
    if (!userId) {
      myOpenTasksCount.value = 0
      return
    }
    
    // Load user tasks (same as in InboxViewsList)
    await tasksStore.loadUserTasks(userId)
    
    // Count tasks with status 'Not Started' (same filter as myTasksCount computed in InboxViewsList)
    const openTasks = tasksStore.userTasks?.filter(task => task.status === 'Not Started') || []
    myOpenTasksCount.value = openTasks.length
  } catch (err) {
    console.error('Error fetching My Open Tasks count:', err)
    myOpenTasksCount.value = 0
  }
}

// Fetch Team Unassigned Cases count
const fetchTeamUnassignedCasesCount = async () => {
  try {
    const views = casesStore.views || []
    const teamUnassignedView = views.find(view => view.label === "My Team's Unassigned Cases")
    
    if (!teamUnassignedView) {
      console.warn("My Team's Unassigned Cases view not found")
      teamUnassignedCasesCount.value = 0
      return
    }

    const { useIssuesAPI } = await import('@/composables/services')
    const issuesAPI = useIssuesAPI()
    
    const params: any = {
      page: 1,
      limit: 1, // We only need the count
      start: 0
    }
    
    if (teamUnassignedView.filters && teamUnassignedView.filters.length > 0) {
      params.filter = []
      for (const filterParam of teamUnassignedView.filters) {
        params.filter.push({ 
          property: filterParam.filter_field, 
          value: filterParam.filter_compare_field, 
          operator: filterParam.filter_operator 
        })
      }
    }
    
    const result = await issuesAPI.fetchCases(params)
    teamUnassignedCasesCount.value = result.totalCount || 0
  } catch (err) {
    console.error('Error fetching Team Unassigned Cases count:', err)
    teamUnassignedCasesCount.value = 0
  }
}

// Fetch all metrics
const fetchMetrics = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      fetchMyOpenCasesCount(),
      fetchMyOpenTasksCount(),
      fetchTeamUnassignedCasesCount()
    ])
  } catch (err) {
    console.error('Error fetching dashboard metrics:', err)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  console.log('Inbox Dashboard mounted')
  // Wait for views to be loaded before fetching team metrics
  if (casesStore.views && casesStore.views.length > 0) {
    await fetchMetrics()
  } else {
    // Wait for views to load
    await casesStore.fetchViews()
    await fetchMetrics()
  }
})
</script>

<template>
  <div class="inbox-dashboard">
    <div class="dashboard-header">
      <div class="header-content">
        <BravoTitlePage>Dashboard</BravoTitlePage>
        <div class="header-actions">
          <BravoButton
            icon="pi pi-refresh"
            severity="secondary"
            text
            :loading="isLoading"
            @click="fetchMetrics"
            aria-label="Refresh dashboard"
          />
        </div>
      </div>
    </div>
    
    <div class="dashboard-content">
      <div class="dashboard-grid">
        <!-- My Open Cases Metric -->
        <div class="dashboard-section metric-card">
          <div v-if="isLoading" class="metric-skeleton">
            <div class="skeleton-header">
              <BravoSkeleton shape="circle" size="48px" />
              <div class="skeleton-info">
                <BravoSkeleton width="120px" height="18px" />
                <BravoSkeleton width="100px" height="14px" />
              </div>
            </div>
            <div class="skeleton-value">
              <BravoSkeleton width="60px" height="40px" />
            </div>
          </div>
          <template v-else>
            <div class="metric-header">
              <div class="metric-icon">
                <i class="pi pi-inbox"></i>
              </div>
              <div class="metric-info">
                <h3>My Open Cases</h3>
                <p class="metric-description">Cases assigned to me</p>
              </div>
            </div>
            <div class="metric-value">
              <span class="count">{{ myOpenCasesCount }}</span>
            </div>
          </template>
        </div>
        
        <!-- My Open Tasks Metric -->
        <div class="dashboard-section metric-card">
          <div v-if="isLoading" class="metric-skeleton">
            <div class="skeleton-header">
              <BravoSkeleton shape="circle" size="48px" />
              <div class="skeleton-info">
                <BravoSkeleton width="120px" height="18px" />
                <BravoSkeleton width="100px" height="14px" />
              </div>
            </div>
            <div class="skeleton-value">
              <BravoSkeleton width="60px" height="40px" />
            </div>
          </div>
          <template v-else>
            <div class="metric-header">
              <div class="metric-icon task-icon">
                <i class="pi pi-list"></i>
              </div>
              <div class="metric-info">
                <h3>My Open Tasks</h3>
                <p class="metric-description">Tasks assigned to me</p>
              </div>
            </div>
            <div class="metric-value">
              <span class="count">{{ myOpenTasksCount }}</span>
            </div>
          </template>
        </div>
        
        <!-- My Team's Open Cases Metric -->
        <div class="dashboard-section metric-card">
          <div v-if="isLoading" class="metric-skeleton">
            <div class="skeleton-header">
              <BravoSkeleton shape="circle" size="48px" />
              <div class="skeleton-info">
                <BravoSkeleton width="130px" height="18px" />
                <BravoSkeleton width="110px" height="14px" />
              </div>
            </div>
            <div class="skeleton-value">
              <BravoSkeleton width="60px" height="40px" />
            </div>
          </div>
          <template v-else>
            <div class="metric-header">
              <div class="metric-icon team-icon">
                <i class="pi pi-users"></i>
              </div>
              <div class="metric-info">
                <h3>My Team's Open Cases</h3>
                <p class="metric-description">Unassigned cases for my team</p>
              </div>
            </div>
            <div class="metric-value">
              <span class="count">{{ teamUnassignedCasesCount }}</span>
            </div>
          </template>
        </div>
        
        <div class="dashboard-section full-width">
          <div class="section-header">
            <h3>Recent Activity</h3>
          </div>
          <div class="section-content">
            <p class="placeholder-text">Recent activity feed and updates will be shown here.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.inbox-dashboard {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden;
}

.dashboard-header {
  padding: 1rem 2rem;
  background: var(--surface-50);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  background: var(--surface-50);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
  max-width: 100%;
}

.dashboard-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.dashboard-section.full-width {
  grid-column: 1 / -1;
}

.section-header {
  padding: 1.25rem 1.5rem 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.25rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--surface-900);
  padding-bottom: 1rem;
}

.section-content {
  padding: 0 1.5rem 1.5rem;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  color: var(--surface-600);
  text-align: center;
  font-size: 0.9rem;
  margin: 0;
  padding: 1.5rem;
  border: 2px dashed var(--border-color);
  border-radius: 6px;
  background: var(--surface-50);
  width: 100%;
}

/* Metric Card Styles */
.metric-card {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140px;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--primary-100);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-icon i {
  font-size: 1.5rem;
  color: var(--primary-600);
}

.metric-icon.team-icon {
  background: var(--green-100);
}

.metric-icon.team-icon i {
  color: var(--green-600);
}

.metric-icon.task-icon {
  background: var(--blue-100);
}

.metric-icon.task-icon i {
  color: var(--blue-600);
}

.metric-info {
  flex: 1;
}

.metric-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--surface-900);
}

.metric-description {
  margin: 0;
  font-size: 0.875rem;
  color: var(--surface-600);
  line-height: 1.4;
}

.metric-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: auto;
}

.metric-value .count {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--surface-900);
  line-height: 1;
}

.loading-text {
  font-size: 2rem;
  color: var(--surface-400);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Skeleton Loading Styles */
.metric-skeleton {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
}

.skeleton-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: auto;
}

/* Responsive design for narrower right panel */
@media (max-width: 1400px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .dashboard-content {
    padding: 1.5rem;
  }
  
  .dashboard-header {
    padding: 1rem 1.5rem;
  }
}

/* Adjust for very narrow panels */
@media (max-width: 800px) {
  .dashboard-content {
    padding: 1rem;
  }
  
  .section-content {
    min-height: 120px;
  }
  
  .placeholder-text {
    font-size: 0.85rem;
    padding: 1rem;
  }
}
</style> 