<template>
  <BravoDialog
    :visible="visible"
    @update:visible="(value) => emit('update:visible', value)"
    header="Add File"
    :modal="true"
    :style="{ width: '500px', minHeight: '300px' }"
    :closable="true"
    :closeOnEscape="!isLoading"
    :dismissableMask="!isLoading"
  >
    <div class="add-file-content">
      <div class="form-fields">
        <!-- File Upload -->
        <div class="field">
          <BravoLabel text="Select File" required />
          <div class="file-upload-area">
            <input
              ref="fileInputRef"
              type="file"
              @change="handleFileSelect"
              class="file-input"
              :disabled="isLoading"
              accept="*/*"
            />
            <div 
              class="file-drop-zone"
              :class="{ 'drag-over': isDragOver, 'has-file': selectedFile, 'has-error': fileError }"
              @click="triggerFileInput"
              @dragover.prevent="handleDragOver"
              @dragleave.prevent="handleDragLeave"
              @drop.prevent="handleDrop"
            >
              <div v-if="!selectedFile" class="drop-zone-content">
                <i class="pi pi-cloud-upload" style="font-size: 2rem; color: var(--primary-color);"></i>
                <p class="drop-zone-text">Click to select a file or drag and drop</p>
                <p class="drop-zone-subtext">Supported file types: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV, ZIP, RAR, JPG, PNG, GIF, MP4, AVI, MOV</p>
                <p class="drop-zone-restricted">Restricted: HTML, HTM, PHP, JS, EXE, VBS, SFX, PY, JAR, COM, VB, BAT, DLL, SH, SVG</p>
              </div>
              <div v-else class="selected-file-info">
                <i class="pi pi-file" style="font-size: 1.5rem; color: var(--primary-color);"></i>
                <div class="file-details">
                  <p class="file-name">{{ selectedFile.name }}</p>
                  <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
                <BravoButton
                  icon="pi pi-times"
                  severity="secondary"
                  size="small"
                  @click.stop="clearSelectedFile"
                  :disabled="isLoading"
                  class="remove-file-btn"
                />
              </div>
            </div>
            <div v-if="fileError" class="file-error">
              {{ fileError }}
            </div>
          </div>
        </div>

        <!-- Upload Progress -->
        <div v-if="isLoading && uploadProgress > 0" class="field">
          <BravoLabel text="Upload Progress" />
          <ProgressBar :value="uploadProgress" class="w-full" />
          <p class="progress-text">{{ uploadProgress }}% uploaded</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          :label="isLoading ? 'Uploading...' : 'Upload File'"
          severity="primary"
          @click="handleSubmit"
          :loading="isLoading"
          :disabled="isLoading || !isFormValid"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import ProgressBar from 'primevue/progressbar'
import type { Issue } from '../../../services/IssuesAPI'

const props = defineProps<{
  visible: boolean
  issue: Issue | null
  onSubmit?: (file: File, fileTag: string) => Promise<void>
  onCancel?: () => void
  uploadProgress?: number
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// Form state
const selectedFile = ref<File | null>(null)
const fileTag = ref('attachment')
const isLoading = ref(false)
const isDragOver = ref(false)
const fileInputRef = ref<HTMLInputElement>()
const fileError = ref<string | null>(null)

// Restricted file types
const restrictedFileTypes = ['html', 'htm', 'php', 'js', 'exe', 'vbs', 'sfx', 'py', 'jar', 'com', 'vb', 'bat', 'dll', 'sh', 'svg']

// File tag options (keeping for potential future use, but not displayed)
const fileTagOptions = [
  { label: 'Attachment', value: 'attachment' },
  { label: 'Document', value: 'document' },
  { label: 'Image', value: 'image' },
  { label: 'Screenshot', value: 'screenshot' },
  { label: 'Log File', value: 'log' },
  { label: 'Other', value: 'other' }
]

// Computed properties
const isFormValid = computed(() => {
  return selectedFile.value !== null && !fileError.value
})

const uploadProgress = computed(() => props.uploadProgress || 0)

// Watch for modal visibility changes
watch(() => props.visible, (newValue) => {
  if (newValue) {
    resetForm()
  }
})

// Methods
function resetForm() {
  selectedFile.value = null
  fileTag.value = 'attachment'
  isDragOver.value = false
  fileError.value = null
}

function validateFile(file: File): boolean {
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  
  if (!fileExtension) {
    fileError.value = 'File must have an extension'
    return false
  }
  
  if (restrictedFileTypes.includes(fileExtension)) {
    fileError.value = `${fileExtension.toUpperCase()} files are not allowed for security reasons`
    return false
  }
  
  fileError.value = null
  return true
}

function setSelectedFile(file: File) {
  if (validateFile(file)) {
    selectedFile.value = file
  } else {
    selectedFile.value = null
  }
}

function triggerFileInput() {
  if (!isLoading.value && fileInputRef.value) {
    fileInputRef.value.click()
  }
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    setSelectedFile(target.files[0])
  }
}

function handleDragOver(event: DragEvent) {
  if (!isLoading.value) {
    isDragOver.value = true
  }
}

function handleDragLeave(event: DragEvent) {
  isDragOver.value = false
}

function handleDrop(event: DragEvent) {
  if (!isLoading.value) {
    isDragOver.value = false
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      setSelectedFile(event.dataTransfer.files[0])
    }
  }
}

function clearSelectedFile() {
  selectedFile.value = null
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

async function handleSubmit() {
  if (!isFormValid.value || !selectedFile.value || !props.onSubmit) return
  
  isLoading.value = true
  try {
    await props.onSubmit(selectedFile.value, 'attachment')
    emit('update:visible', false)
  } catch (error) {
    console.error('Error uploading file:', error)
    // Error handling is done in the parent component
  } finally {
    isLoading.value = false
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel()
  }
  emit('update:visible', false)
}
</script>

<style scoped>
.add-file-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-upload-area {
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 0;
  height: 0;
}

.file-drop-zone {
  border: 2px dashed var(--surface-300);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--surface-50);
}

.file-drop-zone:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-50);
}

.file-drop-zone.drag-over {
  border-color: var(--primary-color);
  background-color: var(--primary-100);
}

.file-drop-zone.has-file {
  border-color: var(--green-500);
  background-color: var(--green-50);
}

.file-drop-zone.has-error {
  border-color: var(--red-500);
  background-color: var(--red-50);
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.drop-zone-text {
  margin: 0;
  font-weight: 600;
  color: var(--surface-700);
}

.drop-zone-subtext {
  margin: 0;
  font-size: 0.875rem;
  color: var(--surface-500);
}

.drop-zone-restricted {
  margin: 0;
  font-size: 0.75rem;
  color: var(--red-600);
  font-weight: 500;
}

.selected-file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  margin: 0;
  font-weight: 600;
  color: var(--surface-700);
  word-break: break-all;
}

.file-size {
  margin: 0;
  font-size: 0.875rem;
  color: var(--surface-500);
}

.remove-file-btn {
  flex-shrink: 0;
}

.file-error {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--red-500);
  text-align: center;
}

.progress-text {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
  color: var(--surface-600);
  text-align: center;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

/* Ensure consistent button sizing */
.modal-footer .p-button {
  min-height: 40px;
}
</style> 