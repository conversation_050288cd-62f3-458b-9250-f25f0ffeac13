<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import Dropdown from 'primevue/dropdown'
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue'
import BravoInputNumber from '@services/ui-component-library/components/BravoInputNumber.vue'
import Calendar from 'primevue/calendar'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import type { SchemaField } from '@/composables/services/useIssuesAPI'
import type { Issue } from '@/services/IssuesAPI'
import { useMetaStore } from '@/stores/meta'

interface Props {
  schemaFields: Schema<PERSON>ield[]
  issue?: Issue | null
  loading?: boolean
  readonly?: boolean
}

interface Emits {
  (e: 'field-change', field: string, value: any): void
  (e: 'save', changes: Record<string, any>): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  issue: null,
  loading: false,
  readonly: true
})

const emit = defineEmits<Emits>()

// Store instances
const metaStore = useMetaStore()

// Edit state management - now per field
const editingField = ref<string | null>(null)
const fieldChanges = ref<Record<string, any>>({})
const savingField = ref<string | null>(null)

// Members users state for the special field
const membersUsers = ref<any[]>([])
const loadingMembersUsers = ref(false)

// Member products state for the special field
const memberProducts = ref<any[]>([])
const loadingMemberProducts = ref(false)

// Sort schema fields by position
const sortedFields = computed(() => {
  console.log('🔍 DynamicCaseForm: All schema fields received:', props.schemaFields.map(f => ({ field: f.field, lbl: f.lbl, fieldType: f.fieldType })))
  
  // Check specifically for resolution fields
  const resolutionFields = props.schemaFields.filter(f => f.field === 'c__d_resolution' || f.field === 'idr_resolution')
  console.log('🔍 DynamicCaseForm: Resolution fields found:', resolutionFields.map(f => ({ field: f.field, lbl: f.lbl, fieldType: f.fieldType })))
  
  return [...props.schemaFields].sort((a, b) => a.position - b.position)
})

// Type for field options
type FieldOption = {
  id?: string
  lbl: string
  val: string
  default?: boolean
}

// Map schema field names to issue data field names
function getIssueFieldName(schemaFieldName: string): string {
  const fieldMapping: Record<string, string> = {
    'resolution_status': 'idr_resolution',  // Schema field -> Issue data field (resolution notes)
    'resolve_issue': 'resolution'           // Schema field -> Issue data field (resolution status integer)
  }
  
  return fieldMapping[schemaFieldName] || schemaFieldName
}

// Get the value for a field from the issue data
function getFieldValue(field: SchemaField): any {
  if (!props.issue) return null
  
  // Map schema field name to actual issue data field name
  const issueFieldName = getIssueFieldName(field.field)
  let value = (props.issue as any)[issueFieldName]
  
  // Special handling for idr_resolution - convert array to string if needed
  if (issueFieldName === 'idr_resolution') {
    if (Array.isArray(value)) {
      // Join array elements with newlines for textarea display
      return value.join('\n')
    }
    return value || ''
  }
  
  // Special handling for resolve_issue - ensure the value matches the dropdown options
  if (field.field === 'resolve_issue') {
    // Get the integer value from resolution field
    const integerValue = value
    
    // Get the options to find the matching value format
    const options = getFieldOptions(field)
    
    // Find the option that matches our integer value
    const matchingOption = options.find((opt: any) => {
      // Try both string and integer comparison
      return opt.val == integerValue || opt.val === String(integerValue)
    })
    
    // Return the value in the format expected by the dropdown
    return matchingOption ? matchingOption.val : integerValue
  }
  
  // Special handling for bc__tags_object_members_devices - handle array values
  if (field.field === 'bc__tags_object_members_devices') {
    // If it's an array, return as-is for multi-select
    if (Array.isArray(value)) {
      return value
    }
    // If it's a string that looks like JSON, try to parse it
    if (typeof value === 'string' && value.startsWith('[')) {
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    }
    return value
  }
  
  // Handle special cases for different field types
  if (field.fieldType === 'picklist_multi' && typeof value === 'string') {
    try {
      // Try to parse JSON string for multi-select fields
      value = JSON.parse(value)
    } catch {
      // If not JSON, split by comma
      value = value ? value.split(',').map((v: string) => v.trim()) : []
    }
  }
  
  return value
}

// Get the display value for read-only mode
function getDisplayValue(field: SchemaField): string {
  const value = getFieldValue(field)
  
  if (value === null || value === undefined || value === '') {
    return '—'
  }
  
  // Get the actual issue field name for special handling
  const issueFieldName = getIssueFieldName(field.field)
  
  switch (field.fieldType) {
    case 'picklist':
      // Find the option label for the value
      const options = getFieldOptions(field)
      const option = options.find((opt: FieldOption) => opt.val === value)
      return option ? option.lbl : value
    case 'picklist_multi':
      if (Array.isArray(value)) {
        const options = getFieldOptions(field)
        const labels = value.map(val => {
          const option = options.find((opt: FieldOption) => opt.val === val)
          return option ? option.lbl : val
        })
        return labels.join(', ')
      }
      return value
    case 'currency':
      if (typeof value === 'number') {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: field.decimalPlaces || 2,
          maximumFractionDigits: field.decimalPlaces || 2
        }).format(value)
      }
      return value
    case 'datetime':
      if (value) {
        return new Date(value).toLocaleString()
      }
      return value
    case 'date':
      if (value) {
        return new Date(value).toLocaleDateString()
      }
      return value
    default:
      // Special handling for members_users_id field
      if (field.field === 'members_users_id') {
        const user = membersUsers.value.find((user: any) => user.val === value)
        return user ? user.lbl : value
      }
      
      // Special handling for member products fields
      if (field.field === 'bc__tags_object_members_devices' || field.field === 'product_picklist') {
        // Handle array of product IDs
        if (Array.isArray(value)) {
          const productLabels = value.map(productId => {
            const product = memberProducts.value.find((product: any) => product.val === productId)
            return product ? product.lbl : productId
          })
          return productLabels.join(', ')
        }
        // Handle single product ID
        const product = memberProducts.value.find((product: any) => product.val === value)
        return product ? product.lbl : value
      }
      
      // Special handling for category field
      if (field.field === 'category') {
        const categoryOptions = getFieldOptions(field)
        const category = categoryOptions.find((cat: FieldOption) => cat.val === value)
        return category ? category.lbl : value
      }
      
      // Special handling for resolution status field (resolve_issue -> resolution integer, but display c__d_resolution string)
      if (field.field === 'resolve_issue') {
        // For display, show the string value from c__d_resolution if available
        const stringValue = (props.issue as any)?.c__d_resolution
        if (stringValue) {
          return stringValue
        }
        // Fallback to finding the label from options using the integer value
        const resolutionOptions = getFieldOptions(field)
        const resolution = resolutionOptions.find((res: FieldOption) => res.val === value)
        return resolution ? resolution.lbl : value
      }
      
      // Special handling for resolution notes field (resolution_status -> idr_resolution)
      if (field.field === 'resolution_status') {
        // Resolution notes are just text, return as-is
        return value ? String(value) : '—'
      }
      
      return String(value)
  }
}

// Handle field value changes
function handleFieldChange(field: SchemaField, newValue: any) {
  // Get the actual issue field name for saving
  const issueFieldName = getIssueFieldName(field.field)
  
  // Special handling for idr_resolution - convert string back to array format
  if (issueFieldName === 'idr_resolution') {
    // Convert string to array format for API compatibility
    const arrayValue = newValue ? [newValue] : []
    fieldChanges.value[issueFieldName] = arrayValue
    emit('field-change', issueFieldName, arrayValue)
    return
  }
  
  fieldChanges.value[issueFieldName] = newValue
  emit('field-change', issueFieldName, newValue)
}

// Get the appropriate component for a field type
function getFieldComponent(field: SchemaField): string {
  // Special case for resolve_issue - force it to be a picklist
  if (field.field === 'resolve_issue') {
    return 'Dropdown'
  }
  
  // Special case for product fields - force them to be picklists
  if (field.field === 'bc__tags_object_members_devices') {
    return 'BravoMultiSelect'
  }
  
  switch (field.fieldType) {
    case 'text':
      return 'BravoInputText'
    case 'textarea':
      return 'BravoTextarea'
    case 'picklist':
      return 'Dropdown'
    case 'picklist_multi':
      return 'BravoMultiSelect'
    case 'currency':
    case 'number':
      return 'BravoInputNumber'
    case 'datetime':
    case 'date':
      return 'Calendar'
    default:
      return 'BravoInputText'
  }
}

// Get props for the field component
function getFieldProps(field: SchemaField) {
  const baseProps = {
    modelValue: getFieldValue(field),
    placeholder: field.lbl,
    disabled: field.readOnly || props.loading,
    required: field.required
  }
  
  // Special case for resolve_issue - treat as picklist
  if (field.field === 'resolve_issue') {
    return {
      ...baseProps,
      options: getFieldOptions(field),
      optionLabel: 'lbl',
      optionValue: 'val',
      placeholder: `Select ${field.lbl}`
    }
  }
  
  // Special case for product fields - treat as picklists
  if (field.field === 'bc__tags_object_members_devices') {
    return {
      ...baseProps,
      options: getFieldOptions(field),
      optionLabel: 'lbl',
      optionValue: 'val',
      placeholder: `Select ${field.lbl}`,
      multiple: true,
      display: 'chip'
    }
  }
  
  if (field.field === 'product_picklist') {
    return {
      ...baseProps,
      options: getFieldOptions(field),
      optionLabel: 'lbl',
      optionValue: 'val',
      placeholder: `Select ${field.lbl}`
    }
  }
  
  switch (field.fieldType) {
    case 'picklist':
    case 'picklist_multi':
      return {
        ...baseProps,
        options: getFieldOptions(field),
        optionLabel: 'lbl',
        optionValue: 'val',
        placeholder: `Select ${field.lbl}`,
        ...(field.fieldType === 'picklist_multi' && { 
          multiple: true,
          display: 'chip'
        })
      }
    case 'currency':
      return {
        ...baseProps,
        mode: 'currency',
        currency: 'USD',
        locale: 'en-US',
        minFractionDigits: field.decimalPlaces || 2,
        maxFractionDigits: field.decimalPlaces || 2
      }
    case 'number':
      return {
        ...baseProps,
        mode: 'decimal',
        minFractionDigits: field.decimalPlaces || 0,
        maxFractionDigits: field.decimalPlaces || 2
      }
    case 'datetime':
      return {
        ...baseProps,
        showTime: true,
        showSeconds: false,
        dateFormat: 'mm/dd/yy',
        placeholder: 'Select date and time'
      }
    case 'date':
      return {
        ...baseProps,
        dateFormat: 'mm/dd/yy',
        placeholder: 'Select date'
      }
    case 'textarea':
      return {
        ...baseProps,
        rows: 3,
        autoResize: true
      }
    default:
      return baseProps
  }
}

// Get options for a field, with special handling for bc__tags_support
function getFieldOptions(field: SchemaField): FieldOption[] {
  // Special case for bc__tags_support - get from meta store
  if (field.field === 'bc__tags_support') {
    const tags = metaStore.metaData?.pl__tags || []
    const caseOwnerPartnersTeamsId = (props.issue as any)?.owner_partners_teams_id
    const caseCategoryId = (props.issue as any)?.category
    
    return tags
      .filter((tag: any) => {
        // First filter: category must be 3
        if (tag.category !== 3) return false
        
        // Second filter: check partners_teams_ids
        const partnersTeamsIds = tag.partners_teams_ids || []
        
        // If -1 is in the array, show this tag (universal tag)
        if (partnersTeamsIds.includes('-1')) {
          // Still need to check category_tag_ids even for universal tags
        } else if (caseOwnerPartnersTeamsId && partnersTeamsIds.includes(caseOwnerPartnersTeamsId)) {
          // Team-specific tag matches
        } else {
          // No team match, don't show this tag
          return false
        }
        
        // dont delete this - we may go off sponsior teams in the future or make it dynamic
        // const caseSponsorPartnersTeamsId = (props.issue as any)?.sponsor_partners_teams_id
        // if (caseSponsorPartnersTeamsId && partnersTeamsIds.includes(caseSponsorPartnersTeamsId)) {
        //   return true
        // }
        
        // Third filter: check category_tag_ids
        const categoryTagIds = tag.category_tag_ids || []
        
        // If category_tag_ids is empty, keep it in the list
        if (categoryTagIds.length === 0) return true
        
        // If it has values, validate that one of those categories matches our case.category
        if (caseCategoryId && categoryTagIds.includes(caseCategoryId)) {
          return true
        }
        
        // If no category match, don't show this tag
        return false
      })
      .map((tag: any) => ({
        lbl: tag.lbl,
        val: tag.val,
        id: tag.id
      }))
  }
  
  // Special case for category field - get from meta store
  if (field.field === 'category') {
    const tags = metaStore.metaData?.pl__tags || []
    return tags
      .filter((tag: any) => tag.category === 5)
      .map((tag: any) => ({
        lbl: tag.lbl,
        val: tag.val,
        id: tag.id
      }))
  }
  
  // Special case for resolution status (resolve_issue -> c__d_resolution) - get from meta store
  if (field.field === 'resolve_issue') {
    // Get resolution options from meta store
    const resolutionOptions = metaStore.metaData?.pl__issues_resolution || []
    const mappedOptions = resolutionOptions.map((resolution: any) => ({
      lbl: resolution.lbl,
      val: resolution.val,
      id: resolution.id
    }))
    
    return mappedOptions
  }
  
  // Special case for members_users_id - get from members users
  if (field.field === 'members_users_id') {
    return membersUsers.value.map((user: any) => ({
      lbl: user.lbl,
      val: user.val,
      id: user.id
    }))
  }
  
  // Special case for member products field - get from member products
  if (field.field === 'bc__tags_object_members_devices' || field.field === 'product_picklist') {
    return memberProducts.value.map((product: any) => ({
      lbl: product.lbl,
      val: product.val,
      id: product.id
    }))
  }
  
  // Default to schema options
  return field.options || []
}

// Check if field should be displayed (handle type 2 fields which seem to be section headers)
function shouldDisplayField(field: SchemaField): boolean {
  // Type 2 seems to be section headers, type 1 and 5 are actual fields
  return field.type === 1 || field.type === 5
}

// Check if field is a section header
function isSectionHeader(field: SchemaField): boolean {
  return field.type === 2
}

// Watch for issue changes to fetch members users if needed
watch(() => [props.issue, props.schemaFields] as const, ([newIssue, newSchemaFields]) => {
  if (newIssue && newSchemaFields && Array.isArray(newSchemaFields)) {
    // Check if we have a members_users_id field in the schema and fetch data for display
    const hasMembersUsersField = newSchemaFields.some((field: SchemaField) => field.field === 'members_users_id')
    if (hasMembersUsersField && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
      fetchMembersUsers()
    }
    
    // Check if we have a member products field in the schema and fetch data for display
    const hasMemberProductsField = newSchemaFields.some((field: SchemaField) => 
      field.field === 'bc__tags_object_members_devices' || field.field === 'product_picklist'
    )
    if (hasMemberProductsField && memberProducts.value.length === 0 && !loadingMemberProducts.value) {
      fetchMemberProducts()
    }
  }
}, { immediate: true })

// Fetch data on component mount for display mode
onMounted(() => {
  // Fetch member products if we have the required case data
  if (props.issue && memberProducts.value.length === 0 && !loadingMemberProducts.value) {
    fetchMemberProducts()
  }
  
  // Fetch members users if we have the required case data
  if (props.issue && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
    fetchMembersUsers()
  }
})

// Start editing a specific field
function startEditingField(fieldName: string) {
  editingField.value = fieldName
  fieldChanges.value = {}
  
  // Fetch members users if editing the members_users_id field and data not already loaded
  if (fieldName === 'members_users_id' && membersUsers.value.length === 0 && !loadingMembersUsers.value) {
    fetchMembersUsers()
  }
  
  // Fetch member products if editing product fields and data not already loaded
  if ((fieldName === 'bc__tags_object_members_devices' || fieldName === 'product_picklist') && 
      memberProducts.value.length === 0 && !loadingMemberProducts.value) {
    fetchMemberProducts()
  }
}

// Cancel editing for current field
function cancelEditingField() {
  editingField.value = null
  fieldChanges.value = {}
}

// Save changes for current field
async function saveFieldChanges(field: SchemaField) {
  const issueFieldName = getIssueFieldName(field.field)
  if (!editingField.value || !fieldChanges.value[issueFieldName]) return
  
  savingField.value = field.field
  
  try {
    const changes = { [issueFieldName]: fieldChanges.value[issueFieldName] }
    await emit('save', changes)
    editingField.value = null
    fieldChanges.value = {}
  } catch (error) {
    console.error('Failed to save field changes:', error)
    // Keep in edit mode on error
  } finally {
    savingField.value = null
  }
}

// Handle escape key to cancel editing
function handleKeydown(event: KeyboardEvent, field: SchemaField) {
  if (event.key === 'Escape') {
    cancelEditingField()
  } else if (event.key === 'Enter' && !event.shiftKey) {
    // Save on Enter for non-textarea fields
    if (field.fieldType !== 'textarea') {
      event.preventDefault()
      saveFieldChanges(field)
    }
  }
}

// Check if a field is currently being edited
function isFieldEditing(fieldName: string): boolean {
  return editingField.value === fieldName
}

// Check if a field is currently being saved
function isFieldSaving(fieldName: string): boolean {
  return savingField.value === fieldName
}

// Check if field has changes
function hasFieldChanges(fieldName: string): boolean {
  const issueFieldName = getIssueFieldName(fieldName)
  return fieldChanges.value[issueFieldName] !== undefined
}

// Fetch members users based on case data
async function fetchMembersUsers() {
  if (!props.issue) return
  
  loadingMembersUsers.value = true
  try {
    // Extract required data from the case
    const members_id = (props.issue as any).members_id
    const members_locations_id = props.issue.members_locations_id
    const sponsor_id = props.issue.sponsor_partners_id || props.issue.owner_partners_id
    const context_org_id = props.issue.owner_partners_id || props.issue.sponsor_partners_id
    
    if (!members_id || !members_locations_id || !sponsor_id || !context_org_id) {
      console.warn('Missing required data for fetching members users:', {
        members_id,
        members_locations_id,
        sponsor_id,
        context_org_id
      })
      return
    }
    
    const response = await metaStore.fetchMembersUsers({
      members_id,
      members_locations_id,
      sponsor_id,
      context_org_id
    })
    
    membersUsers.value = response.pl__members_users || []
    console.log('Fetched members users:', membersUsers.value)
  } catch (error) {
    console.error('Failed to fetch members users:', error)
    membersUsers.value = []
  } finally {
    loadingMembersUsers.value = false
  }
}

// Fetch member products based on case data
async function fetchMemberProducts() {
  if (!props.issue) return
  
  loadingMemberProducts.value = true
  try {
    // Extract required data from the case
    const members_id = (props.issue as any).members_id
    const members_locations_id = props.issue.members_locations_id
    
    if (!members_id || !members_locations_id) {
      console.warn('Missing required data for fetching member products:', {
        members_id,
        members_locations_id
      })
      return
    }
    
    const response = await metaStore.fetchMemberProducts({
      members_id,
      members_locations_id
    })
    
    memberProducts.value = response.pl__members_devices || []
    console.log('Fetched member products:', memberProducts.value)
  } catch (error) {
    console.error('Failed to fetch member products:', error)
    memberProducts.value = []
  } finally {
    loadingMemberProducts.value = false
  }
}
</script>

<template>
  <div class="dynamic-case-form">
    <!-- Loading State -->
    <div v-if="loading" class="form-skeleton">
      <BravoSkeleton v-for="i in 6" :key="i" width="100%" height="40px" class="mb-4" />
    </div>
    
    <!-- Form Fields -->
    <div v-else class="form-fields">
      <template v-for="field in sortedFields" :key="field.field">
        <!-- Section Headers -->
        <div v-if="isSectionHeader(field)" class="section-header">
          <h4>{{ field.lbl }}</h4>
        </div>
        
        <!-- Regular Fields -->
        <div v-else-if="shouldDisplayField(field)" class="field-group">
          <label :for="field.field" class="field-label">
            {{ field.lbl }}
            <span v-if="field.required" class="required-indicator">*</span>
          </label>
          
          <!-- Read-Only Display with hover effect -->
          <div 
            v-if="!isFieldEditing(field.field) && !readonly" 
            class="field-display editable-field"
            @click="startEditingField(field.field)"
            :title="'Click to edit ' + field.lbl"
          >
            {{ getDisplayValue(field) }}
          </div>
          
          <!-- Read-Only Display (non-editable) -->
          <div v-else-if="!isFieldEditing(field.field) && readonly" class="field-display">
            {{ getDisplayValue(field) }}
          </div>
          
          <!-- Edit Mode for Current Field -->
          <div v-else class="field-edit-container">
            <div class="field-input-wrapper">
              <!-- Text Input -->
              <BravoInputText
                v-if="getFieldComponent(field) === 'BravoInputText'"
                :id="field.field"
                v-bind="getFieldProps(field)"
                @update:model-value="(value: any) => handleFieldChange(field, value)"
                @keydown="(event: any) => handleKeydown(event, field)"
                class="field-input"
                :autofocus="true"
              />
              
              <!-- Textarea -->
              <BravoTextarea
                v-else-if="getFieldComponent(field) === 'BravoTextarea'"
                :id="field.field"
                v-bind="getFieldProps(field)"
                @update:model-value="(value: any) => handleFieldChange(field, value)"
                @keydown="(event: any) => handleKeydown(event, field)"
                class="field-input"
                :autofocus="true"
              />
              
              <!-- Dropdown -->
              <Dropdown
                v-else-if="getFieldComponent(field) === 'Dropdown'"
                :id="field.field"
                v-bind="getFieldProps(field)"
                @update:model-value="(value: any) => handleFieldChange(field, value)"
                @keydown="(event: any) => handleKeydown(event, field)"
                class="field-input"
                :autofocus="true"
              />
              
              <!-- Multi-Select -->
              <BravoMultiSelect
                v-else-if="getFieldComponent(field) === 'BravoMultiSelect'"
                :id="field.field"
                v-bind="getFieldProps(field)"
                @update:model-value="(value: any) => handleFieldChange(field, value)"
                @keydown="(event: any) => handleKeydown(event, field)"
                class="field-input"
                :autofocus="true"
              />
              
              <!-- Number/Currency Input -->
              <BravoInputNumber
                v-else-if="getFieldComponent(field) === 'BravoInputNumber'"
                :id="field.field"
                v-bind="getFieldProps(field)"
                @update:model-value="(value: any) => handleFieldChange(field, value)"
                @keydown="(event: any) => handleKeydown(event, field)"
                class="field-input"
                :autofocus="true"
              />
              
              <!-- Date/DateTime Picker -->
              <Calendar
                v-else-if="getFieldComponent(field) === 'Calendar'"
                :id="field.field"
                v-bind="getFieldProps(field)"
                @update:model-value="(value: any) => handleFieldChange(field, value)"
                @keydown="(event: any) => handleKeydown(event, field)"
                class="field-input"
                :autofocus="true"
              />
              
              <!-- Fallback for unknown field types -->
              <div v-else class="field-placeholder">
                <span class="field-type-label">{{ field.fieldType || 'unknown' }}</span>
                <span class="field-value">{{ getDisplayValue(field) }}</span>
              </div>
            </div>
            
            <!-- Field Action Buttons -->
            <div class="field-actions">
              <!-- Loading Spinner -->
              <BravoProgressSpinner 
                v-if="isFieldSaving(field.field)"
                style="width: 20px; height: 20px;"
              />
              
              <!-- Save/Cancel Buttons -->
              <template v-else>
                <BravoButton
                  icon="pi pi-check"
                  size="small"
                  severity="success"
                  :disabled="!hasFieldChanges(field.field)"
                  @click="saveFieldChanges(field)"
                  class="field-action-btn"
                  :title="'Save ' + field.lbl"
                />
                <BravoButton
                  icon="pi pi-times"
                  size="small"
                  severity="secondary"
                  @click="cancelEditingField"
                  class="field-action-btn"
                  :title="'Cancel editing ' + field.lbl"
                />
              </template>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.dynamic-case-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-header {
  margin: 1.5rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-600);
}

.section-header h4 {
  margin: 0;
  color: var(--primary-600);
  font-size: 1.1rem;
  font-weight: 600;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.required-indicator {
  color: var(--red-500);
  margin-left: 0.25rem;
}

.field-input {
  width: 100%;
  max-width: 400px; /* Limit maximum width for all field inputs */
}

/* Specific width constraints for picklist components */
.field-input :deep(.p-dropdown),
.field-input :deep(.p-multiselect) {
  max-width: 400px;
  width: 100%;
}

/* Ensure dropdown panels don't exceed container width */
.field-input :deep(.p-dropdown-panel),
.field-input :deep(.p-multiselect-panel) {
  max-width: 400px;
}

.field-display {
  padding: 0.75rem 0;
  color: var(--text-color);
  font-size: 0.875rem;
  line-height: 1.5;
  min-height: 1.5rem;
  word-wrap: break-word;
}

/* Editable field hover effect */
.editable-field {
  cursor: pointer;
  padding: 0.5rem;
  margin: -0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.editable-field:hover {
  background-color: var(--surface-100);
}

/* Field edit container */
.field-edit-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.field-input-wrapper {
  flex: 1;
}

/* Field action buttons */
.field-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0;
}

.field-action-btn {
  min-width: 32px;
  height: 32px;
  border-radius: 6px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Success button (check mark) styling */
.field-action-btn.p-button-success {
  background-color: #10b981;
  border-color: #10b981;
  color: white;
}

.field-action-btn.p-button-success:hover {
  background-color: #059669;
  border-color: #059669;
}

/* Secondary button (cancel) styling */
.field-action-btn.p-button-secondary {
  background-color: #6b7280;
  border-color: #6b7280;
  color: white;
}

.field-action-btn.p-button-secondary:hover {
  background-color: #4b5563;
  border-color: #4b5563;
}

.field-placeholder {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--surface-50);
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  font-size: 0.875rem;
}

.field-type-label {
  color: var(--text-color-secondary);
  font-style: italic;
}

.field-value {
  color: var(--text-color);
  font-weight: 500;
}

.edit-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.form-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0.5rem;
}

.edit-button {
  margin-left: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem 0 0 0;
  margin-top: 1rem;
  border-top: 1px solid var(--surface-200);
}
</style> 