<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import Dropdown from 'primevue/dropdown'
import BravoMessage from '@services/ui-component-library/components/BravoMessage.vue'
import type { Journey } from '@/composables/services/useAdminJourneysAPI'
import { useJourneysStore } from '@/stores/journeys'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoToast from '@services/ui-component-library/components/BravoToast.vue'
import { useToast } from 'primevue/usetoast'

const props = withDefaults(defineProps<{
  visible: boolean
  caseId?: string
  currentJourneyId?: string | null
  onChange: (journey: Journey) => Promise<void>
}>(), {
  visible: false,
  caseId: undefined,
  currentJourneyId: null
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'journey-changed': [journey: Journey]
}>()

const journeysStore = useJourneysStore()
const selectedJourney = ref<Journey | null>(null)
const isSubmitting = ref(false)
const showValidation = ref(false)
const isLoading = ref(false)
const toast = useToast()

// Computed properties
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const availableJourneys = computed(() => {
  // Filter out the current journey from the list
  return journeysStore.journeys.filter(journey => journey.id !== props.currentJourneyId)
})

const isFormValid = computed(() => {
  return selectedJourney.value !== null
})

const selectedJourneyDetails = computed(() => {
  if (!selectedJourney.value) return null
  
  return {
    title: selectedJourney.value.title,
    description: selectedJourney.value.description || 'No description available',
    stagesCount: selectedJourney.value.stages?.length || 0,
    stages: selectedJourney.value.stages?.map(stage => stage.title).join(', ') || 'No stages defined'
  }
})

// Watch for modal visibility changes
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    // Reset form when modal opens
    selectedJourney.value = null
    showValidation.value = false
    // Fetch journeys with local loading state
    if (journeysStore.journeys.length === 0) {
      isLoading.value = true
      try {
        await journeysStore.fetchJourneys()
      } finally {
        isLoading.value = false
      }
    } else {
      isLoading.value = false
    }
  }
})

// Methods
function handleCancel() {
  dialogVisible.value = false
}

async function handleSubmit() {
  if (!isFormValid.value || !selectedJourney.value) {
    showValidation.value = true
    return
  }

  isSubmitting.value = true

  try {
    await props.onChange(selectedJourney.value)
    // delay to allow journey to create 
    // setTimeout(() => {
    //   toast.add({
    //     severity: 'success',
    //     summary: 'Journey Changed',
    //     detail: `Successfully changed journey to "${selectedJourney?.value?.title}"`,
    //     life: 3000
    //   })
    // }, 400)
    dialogVisible.value = false
  } catch (error: any) {
    console.error('Error changing journey:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to change journey. Please try again.',
      life: 5000
    })
  } finally {
    isSubmitting.value = false
  }
}

// Custom option template for dropdown
function getOptionLabel(journey: Journey) {
  return journey.title
}

function getOptionDescription(journey: Journey) {
  const description = journey.description || 'No description available'
  return description.length > 72 ? description.substring(0, 72) + '...' : description
}

function getOptionStages(journey: Journey) {
  const stagesCount = journey.stages?.length || 0
  return `${stagesCount} stage${stagesCount !== 1 ? 's' : ''}`
}
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    header="Change Journey"
    :style="{ width: '600px' }"
    :closable="true"
    :draggable="false"
  >
    <div class="change-journey-modal">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-state-content flex space-around">
          <BravoProgressSpinner style="width: 1.5rem; height: 1.5rem;" />
          <p class="ml-2">Loading journeys...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="journeysStore.error" class="error-state">
        <BravoMessage severity="error" :closable="false">
          <template #messageicon>
            <i class="pi pi-exclamation-triangle"></i>
          </template>
          Failed to load journeys: {{ journeysStore.error }}
        </BravoMessage>
      </div>

      <!-- Form Content -->
      <div v-else class="form-content">
        <p class="modal-description">
          Select a new journey to assign to this case. The current journey will be replaced.
        </p>

        <!-- Journey Selection -->
        <div class="field">
          <label for="journey-select" class="field-label">Select New Journey</label>
          <Dropdown
            id="journey-select"
            v-model="selectedJourney"
            :options="availableJourneys"
            option-label="title"
            placeholder="Choose a journey..."
            filter
            :filter-placeholder="'Search journeys...'"
            class="journey-dropdown"
            :class="{ 'p-invalid': !selectedJourney && showValidation }"
          >
            <template #option="{ option }">
              <div class="journey-option">
                <div class="journey-option-header">
                  <span class="journey-title">{{ option.title }}</span>
                  <span class="journey-stages">{{ getOptionStages(option) }}</span>
                </div>
                <div class="journey-description">
                  {{ getOptionDescription(option) }}
                </div>
              </div>
            </template>
          </Dropdown>
          <small v-if="!selectedJourney && showValidation" class="field-error">Please select a journey</small>
        </div>

        <!-- Selected Journey Details -->
        <div v-if="selectedJourneyDetails" class="journey-details">
          <h4>Journey Details</h4>
          <div class="detail-item">
            <strong>Title:</strong> {{ selectedJourneyDetails.title }}
          </div>
          <div class="detail-item">
            <strong>Description:</strong> {{ selectedJourneyDetails.description }}
          </div>
          <div class="detail-item">
            <strong>Stages:</strong> {{ selectedJourneyDetails.stagesCount }} total
          </div>
          <div v-if="selectedJourneyDetails.stages" class="detail-item">
            <strong>Stage Names:</strong> {{ selectedJourneyDetails.stages }}
          </div>
        </div>

        <!-- Warning Message -->
        <BravoMessage severity="warn" :closable="false" class="warning-message">
          <template #messageicon>
            <i class="pi pi-exclamation-triangle"></i>
          </template>
          Changing the journey will replace the current workflow. Any progress in the current journey may be lost.
        </BravoMessage>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isSubmitting"
        />
        <BravoButton
          label="Change Journey"
          icon="pi pi-refresh"
          @click="handleSubmit"
          :disabled="!isFormValid || isSubmitting"
          :loading="isSubmitting"
        />
      </div>
    </template>
  </BravoDialog>

  <BravoToast position="top-right" />
</template>

<style scoped>
.change-journey-modal {
  padding: 0.5rem 0;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 2rem;
}

.loading-state p {
  margin-top: 1rem;
  color: var(--surface-600);
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modal-description {
  margin: 0;
  color: var(--surface-600);
  line-height: 1.5;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-weight: 600;
  color: var(--surface-900);
}

.journey-dropdown {
  width: 100%;
}

.field-error {
  color: var(--red-500);
  font-size: 0.875rem;
}

.journey-option {
  padding: 0.5rem 0;
}

.journey-option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.journey-title {
  font-weight: 600;
  color: var(--surface-900);
}

.journey-stages {
  font-size: 0.875rem;
  color: var(--surface-500);
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.journey-description {
  font-size: 0.875rem;
  color: var(--surface-600);
  line-height: 1.4;
}

.journey-details {
  background: var(--surface-50);
  border: 1px solid var(--surface-200);
  border-radius: 0.5rem;
  padding: 1rem;
}

.journey-details h4 {
  margin: 0 0 0.75rem 0;
  color: var(--surface-900);
  font-size: 1rem;
}

.detail-item {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.4;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item strong {
  color: var(--surface-900);
}

.warning-message {
  margin-top: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

.loading-state {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  height: 120px;
  color: var(--surface-600);
  font-size: 1rem;
}

.loading-state p {
  margin: 0;
  padding: 0;
  font-size: 1rem;
}
</style> 