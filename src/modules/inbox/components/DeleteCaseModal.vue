<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoBodyBold from '@services/ui-component-library/components/BravoTypography/BravoBodyBold.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import type { Issue } from '../../../services/IssuesAPI'

interface Props {
  visible: boolean
  issue: Issue | null
  onSubmit?: () => Promise<void>
  onCancel?: () => void
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const isLoading = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleConfirm = async () => {
  if (!props.onSubmit) return
  
  isLoading.value = true
  
  try {
    await props.onSubmit()
    // Close modal on success
    emit('update:visible', false)
  } catch (error) {
    console.error('Error deleting case:', error)
    // Keep modal open on error so user can retry
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  if (props.onCancel) {
    props.onCancel()
  } else {
    emit('update:visible', false)
  }
}

const handleDialogHide = () => {
  if (isLoading.value) return // Prevent closing when loading
  emit('update:visible', false)
}

// Watch for visible prop changes to reset loading state when modal opens
watch(() => props.visible, (newVisible, oldVisible) => {
  if (newVisible && !oldVisible) {
    // Modal is opening, reset loading state
    isLoading.value = false
  }
})
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    header="Delete Case"
    :style="{ width: '450px' }"
    :closable="true"
    :close-on-escape="!isLoading"
    @hide="handleDialogHide"
    :class="['delete-case-modal', { loading: isLoading }]"
  >
    <div class="delete-modal-content">
      <BravoBody class="confirmation-text">
        Are you sure you want to delete this case? This action cannot be undone.
      </BravoBody>
      
      <div class="warning-info">
        <i class="pi pi-exclamation-triangle warning-icon"></i>
        <div class="warning-text">
          <BravoBodyBold>Warning</BravoBodyBold>
          <BravoBody class="warning-description">
            Deleting this case will permanently remove it from the system.
          </BravoBody>
        </div>
      </div>
      
      <div v-if="issue" class="case-info">
        <BravoBodyBold>Case:</BravoBodyBold>
        <span class="case-name">{{ issue.display_name || issue.id }}</span>
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          label="Delete Case"
          severity="danger"
          @click="handleConfirm"
          :loading="isLoading"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
/* Fix modal border radius and prevent resizing */
:deep(.delete-case-modal .p-dialog) {
  border-radius: 8px !important;
}

:deep(.delete-case-modal .p-dialog-content) {
  border-radius: 0 0 8px 8px !important;
}

:deep(.delete-case-modal .p-dialog-header) {
  border-radius: 8px 8px 0 0 !important;
}

/* Disable close button when loading instead of hiding it */
:deep(.delete-case-modal.loading .p-dialog-header-close) {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-modal-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.confirmation-text {
  margin-bottom: 0.5rem;
}

.warning-info {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: var(--orange-50);
  border-radius: 6px;
  border-left: 4px solid var(--orange-500);
}

.warning-icon {
  color: var(--orange-600);
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.warning-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.warning-description {
  color: var(--orange-700);
  font-size: 0.875rem;
}

.case-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--surface-50);
  border-radius: 6px;
  border-left: 4px solid var(--surface-300);
}

.case-name {
  color: var(--surface-700);
  font-weight: 500;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Fix button width consistency to prevent layout shifts */
.modal-actions :deep(.p-button) {
  min-width: 120px;
  height: 40px;
}

.modal-actions :deep(.p-button-label) {
  white-space: nowrap;
}
</style> 