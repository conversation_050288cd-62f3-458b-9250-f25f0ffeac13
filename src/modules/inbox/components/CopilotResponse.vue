<script setup lang="ts">
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { ref } from 'vue';
import type { CopilotSearchResult } from '@/types/copilot';
import { useToast } from 'primevue/usetoast';

const toast = useToast();

// Define props
defineProps<{
    response: CopilotSearchResult;
}>();

// Define the method to handle feedback
const handleSubmitFeedback = (feedback: string) => {
    console.log('Feedback submitted:', feedback);
    toast.add({ severity: 'success', summary: 'Feedback submitted', detail: feedback, life: 3000 });
};
</script>

<template>
    <div class="copilot-response">
        <div class="copilot-response-title">
            {{ response.title }}
        </div>
        <div class="copilot-response-body">
            {{ response.message }}
        </div>
        <div class="copilot-response-tagline">Generated using AI</div>
        <div class="copilot-response-source">
            <div class="copilot-response-subheader">Source</div>
            <a
                v-for="(source, index) in response.sources"
                :key="index"
                :href="source.url"
                target="_blank"
                class="copilot-response-link"
                >{{ source.title || source.url }}</a
            >
        </div>
        <div class="copilot-response-related-content" v-if="response.relatedLinks.length">
            <div class="copilot-response-subheader">Related</div>
            <a
                v-for="(relatedLink, index) in response.relatedLinks"
                :key="index"
                :href="relatedLink.url"
                target="_blank"
                class="copilot-response-link"
                >{{ relatedLink.title || relatedLink.url }}</a
            >
        </div>
        <div class="copilot-feedback" v-if="false">
            <div>Did this answer your question?</div>
            <div class="copilot-feedback-buttons">
                <BravoButton label="Yes" outlined @click="handleSubmitFeedback('Yes')" />
                <BravoButton label="Partially" outlined @click="handleSubmitFeedback('Partially')" />
                <BravoButton label="No" outlined @click="handleSubmitFeedback('No')" />
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.copilot-response {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.copilot-response-title {
    color: #303336;
    font-size: 15px;
    font-weight: 500;
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.copilot-response-body {
    color: var(--text-color-primary);
    font-size: 14px;
    font-weight: 400;
    margin-top: 4px;
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.copilot-response-tagline {
    font-size: 12px;
    color: #6d7379;
    border-bottom: 1px solid var(--border-color);
    margin-top: 6px;
    width: 100%;
    padding-bottom: 16px;
}

.copilot-response-subheader {
    font-size: 14px;
    color: var(--text-color-primary);
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: 8px;
}

.copilot-feedback {
    margin-top: 20px;
    width: 100%;
}

.copilot-feedback-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.copilot-response-related-content,
.copilot-response-source {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    width: 100%;
    max-width: 100%;
}

.copilot-response-link {
    color: #005db3;
    font-size: 13px;
    text-decoration: none;
    margin-top: 10px;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-all;
    max-width: 100%;
}

.copilot-response-link:hover {
    text-decoration: underline;
}
</style> 