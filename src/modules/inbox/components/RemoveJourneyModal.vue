<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoMessage from '@services/ui-component-library/components/BravoMessage.vue'
import BravoToast from '@services/ui-component-library/components/BravoToast.vue'
import { useToast } from 'primevue/usetoast'

const props = withDefaults(defineProps<{
  visible: boolean
  caseId?: string
  onRemove: () => Promise<void>
}>(), {
  visible: false,
  caseId: undefined
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
}>()

const isSubmitting = ref(false)
const isLoading = ref(false)
const toast = useToast()

// Computed properties
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// Methods
function handleCancel() {
  dialogVisible.value = false
}

async function handleConfirmRemoval() {
  isSubmitting.value = true

  try {
    await props.onRemove()
    // this adds 2 toasts every time OMG why =/
    // toast.add({
    //   severity: 'success',
    //   summary: 'Journey Removed',
    //   detail: 'Successfully removed journey from case',
    //   life: 3000
    // })
    dialogVisible.value = false
  } catch (error: any) {
    console.error('Error removing journey:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to remove journey. Please try again.',
      life: 5000
    })
  } finally {
    isSubmitting.value = false
  }
}

// Watch for modal visibility changes (future extensibility)
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    isLoading.value = false // set to true if you add async fetches in the future
  }
})
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    header="Remove Journey"
    :style="{ width: '500px' }"
    :closable="true"
    :draggable="false"
    :closable-on-escape="!isSubmitting"
    :close-on-click="!isSubmitting"
  >
    <div class="remove-journey-modal">
      <div class="confirmation-content">
        <!-- Warning Icon -->
        <div class="warning-icon">
          <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: var(--orange-500);"></i>
        </div>

        <!-- Confirmation Message -->
        <h3 class="confirmation-title">Remove Journey from Case?</h3>
        
        <p class="confirmation-description">
          Are you sure you want to remove the journey from this case? This action will:
        </p>

        <!-- Impact List -->
        <div class="impact-list">
          <div class="impact-item">
            <i class="pi pi-times-circle"></i>
            <span>Remove the current workflow and all progress</span>
          </div>
          <div class="impact-item">
            <i class="pi pi-times-circle"></i>
            <span>Clear all journey-related stage information</span>
          </div>
          <div class="impact-item">
            <i class="pi pi-times-circle"></i>
            <span>Return the case to manual management</span>
          </div>
        </div>

        <!-- Warning Message -->
        <BravoMessage severity="warn" :closable="false" class="warning-message">
          <template #messageicon>
            <i class="pi pi-exclamation-triangle"></i>
          </template>
          <strong>This action cannot be undone.</strong> You can add a new journey later, but any current progress will be lost.
        </BravoMessage>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isSubmitting"
        />
        <BravoButton
          label="Remove Journey"
          icon="pi pi-trash"
          severity="danger"
          @click="handleConfirmRemoval"
          :disabled="isSubmitting"
          :loading="isSubmitting"
        />
      </div>
    </template>
  </BravoDialog>

  <BravoToast position="top-right" />
</template>

<style scoped>
.remove-journey-modal {
  padding: 0.5rem 0;
}

.confirmation-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.warning-icon {
  margin-bottom: 0.5rem;
}

.confirmation-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--surface-900);
}

.confirmation-description {
  margin: 0;
  color: var(--surface-600);
  line-height: 1.5;
  text-align: left;
}

.impact-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  text-align: left;
}

.impact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--surface-700);
}

.impact-item i {
  color: var(--red-500);
  font-size: 1.1rem;
  flex-shrink: 0;
}

.warning-message {
  margin-top: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}
</style> 