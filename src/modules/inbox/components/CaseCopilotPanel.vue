<script setup lang="ts">
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import { useCopilotStore } from '@/stores/copilot'
import { ref } from 'vue'
import type { Issue } from '../../../services/IssuesAPI'
import type { CopilotSearchResult } from '@/types/copilot'
import InputText from 'primevue/inputtext'
import InputGroup from 'primevue/inputgroup'
import InputGroupAddon from 'primevue/inputgroupaddon'
import CopilotResponse from './CopilotResponse.vue'

const props = withDefaults(defineProps<{
  issue?: Issue
  loading?: boolean
}>(), {
  issue: undefined,
  loading: false
})

const copilotStore = useCopilotStore()
const searchQuery = ref('')
const response = ref<CopilotSearchResult | null>(null)
const searchLoading = ref(false)
const error = ref(null)

const handleSearch = async () => {
    console.log('searchQuery', searchQuery.value)
    console.log('---- execute the search! ')

    searchLoading.value = true
    error.value = null

    try {
        const result = await copilotStore.submitSearch(searchQuery.value)
        searchLoading.value = false
        error.value = null
        response.value = result
    } catch (e: any) {
        console.error('error', e)
        searchLoading.value = false
        error.value = e
    }
}
</script>

<template>
  <div class="panel-content">
    <div v-if="props.loading" class="copilot-skeleton">
      <div class="copilot-header-skeleton">
        <BravoSkeleton width="150px" height="24px" class="mb-4" />
      </div>
      <div class="copilot-content-skeleton">
        <BravoSkeleton width="100%" height="40px" border-radius="6px" class="mb-3" />
        <BravoSkeleton width="100%" height="40px" border-radius="6px" class="mb-3" />
        <BravoSkeleton width="70%" height="40px" border-radius="6px" />
      </div>
    </div>
    <div v-else class="copilot-main">
      <div class="card-layout">
        <div class="copilot-content-wrapper">
          <TransitionGroup name="fadeIn" tag="div" :class="['copilot-content', { 'center-content': !searchLoading && !response && !error }]">
            <BravoSkeleton v-if="searchLoading" width="100%" height="200px" border-radius="6px" class="loader" />
            <BravoZeroStateScreen 
              v-if="!searchLoading && !response"
              title="Find Answers"
              message="Ask a question below and your CX Copilot will generate an answer for you from all the knowledge that it has access to."
              :showButton="false"
              image-src="https://cdn.prod.website-files.com/5d4b12a4c03a64ab754f23bd/67f9df4569df927d4c0065e1_content-zero-state.png"
              image-alt="Copilot Search"
              :action-handler="() => {}"
            />
            <CopilotResponse v-if="!searchLoading && response" :response="response"></CopilotResponse>
            <div v-if="!searchLoading && error" class="error-message">We're sorry, something went wrong.</div>
          </TransitionGroup>
        </div>
        <div class="sticky-input-container">
          <InputGroup class="search-input-group">
            <InputText
              class="copilot-search-input"
              placeholder="Ask a question"
              v-model="searchQuery"
              @keydown.enter="handleSearch"
              icon="pi pi-search"
            />
            <InputGroupAddon @click="handleSearch" class="input-icon">
              <i class="pi pi-search"></i>
            </InputGroupAddon>
          </InputGroup>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel-content {
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 0;
  flex: 1;
  overflow: hidden;
  height: 100%;
  min-height: 100%;
  width: 100%;
  max-width: 100%;
  position: relative;
}

.copilot-main {
  display: flex;
  justify-content: center;
  height: 100%;
  min-height: 100%;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  flex: 1;
}

.card-layout {
  background-color: white;
  font-family: 'Inter';
  color: #303336;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  height: 100%;
  min-height: 100%;
  border-left: none;
  border: none;
  overflow: hidden;
  position: relative;
  flex: 1;
}

.copilot-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 20px 0 20px;
  margin-bottom: 80px; /* Space for sticky input */
  height: 100%;
  min-height: 0; /* Allow flex child to shrink */
}

.sticky-input-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  padding: 16px 20px;
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-group {
  width: 100%;
  max-width: 100%;
}

.copilot-search-input {
  width: 100%;
  max-width: 100%;
  min-width: 0;
}

.copilot-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 100%;
}

.center-content {
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loader {
  margin-left: 0px;
}

.input-icon {
  cursor: pointer;
  flex-shrink: 0;
}

.error-message {
  color: var(--red-500);
  text-align: center;
  padding: 1rem;
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.copilot-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 100%;
  padding: 20px;
  height: 100%;
  flex: 1;
}

.copilot-header-skeleton {
  display: flex;
  justify-content: center;
  align-items: center;
}

.copilot-content-skeleton {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Transition animations */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.3s ease;
}

.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}
</style> 