<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import type { Communication } from '../../types';
import { format, formatDistanceToNow } from 'date-fns';
import { useCommunicationsStore } from '../../stores/communications';
import { xmppService } from '../../services/xmpp';
import { nextTick } from 'vue';
import FileUploadProgress from '../FileUploadProgress.vue';
import Dropdown from 'primevue/dropdown';
import Editor from 'primevue/editor';
import Dialog from 'primevue/dialog';
import ProgressSpinner from 'primevue/progressspinner';
import { openAiService } from '../../services/openai';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';

const props = defineProps<{
  communication: Communication;
}>();

const store = useCommunicationsStore();
const expandedMessages = ref<Set<string>>(new Set());
const showMetadata = ref<Set<string>>(new Set());
const fileInput = ref<HTMLInputElement | null>(null);
const uploadingFiles = ref<Array<{ id: string; name: string; size: number; progress: number }>>([]);
const attachedFiles = ref<Array<{ id: string; name: string; size: number }>>([]);
const showTemplates = ref(false);
const selectedTemplate = ref(null);
const showAiMenu = ref(false);
const selectedAiAction = ref(null);
const showSummaryDialog = ref(false);
const summaryContent = ref('');
const isSummarizing = ref(false);
const isGeneratingReply = ref(false);
const isReformatting = ref(false);
const showLinkDialog = ref(false);
const linkUrl = ref('');
const selectedText = ref('');
const editorRef = ref();
const showEditorToolbar = ref(false);
const textareaRef = ref<HTMLTextAreaElement | null>(null);
const selectionRange = ref<{ start: number; end: number } | null>(null);
const messagesContainer = ref<HTMLElement | null>(null);
const isLoading = ref(true);

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// Define messages computed property early
const messages = computed(() => {
  // First, filter out system messages and map sender details
  const processedMessages = props.communication.messages
    .filter(message => message.type !== 'system')
    .map((message, index, array) => {
      const sender = getSenderDetails(message.senderId);
      return {
        ...message,
        sender: {
          name: sender?.name || message.senderId.split('@')[0],
          email: sender?.email || `${message.senderId}@example.com`,
          initials: (sender?.name || message.senderId).charAt(0).toUpperCase()
        }
      };
    });

  // Now group file attachments with their parent messages
  const groupedMessages = [];
  for (let i = 0; i < processedMessages.length; i++) {
    const currentMessage = processedMessages[i];
    const nextMessage = processedMessages[i + 1];
    
    // Try to parse the current message content
    let currentContent;
    try {
      currentContent = JSON.parse(currentMessage.content);
    } catch (e) {
      currentContent = null;
    }
    
    // Try to parse the next message content
    let nextContent;
    try {
      nextContent = nextMessage ? JSON.parse(nextMessage.content) : null;
    } catch (e) {
      nextContent = null;
    }
    
    // If the next message is a file attachment (has attachment but no email body)
    if (
      nextContent && 
      nextContent.attachment && 
      (!nextContent.email || !nextContent.email.body) &&
      currentContent?.email?.body // Current message has an email body
    ) {
      // Merge the attachment into the current message
      currentMessage.content = JSON.stringify({
        email: currentContent.email,
        attachment: nextContent.attachment
      });
      // Skip the next message since we've merged it
      i++;
    }
    
    groupedMessages.push(currentMessage);
  }
  
  return groupedMessages;
});

// Initialize with the latest message expanded
onMounted(async () => {
  isLoading.value = true;
  try {
    // Clear any existing expanded messages
    expandedMessages.value.clear();
    
    // If there are messages, expand only the most recent one
    const latestMessage = messages.value[messages.value.length - 1];
    if (latestMessage) {
      expandedMessages.value = new Set([latestMessage.id]);
    }
  } finally {
    // Short delay to prevent flash
    setTimeout(async () => {
      isLoading.value = false;
      await scrollToBottom();
    }, 250);
  }
});

// Watch for new messages and scroll to bottom
watch(() => messages.value.length, (newLength, oldLength) => {
  // If new messages were added, expand only the latest one
  if (newLength > oldLength) {
    const latestMessage = messages.value[messages.value.length - 1];
    if (latestMessage) {
      expandedMessages.value = new Set([latestMessage.id]);
    }
  }
  scrollToBottom();
});

// Add watcher for communication changes
watch(() => props.communication.id, () => {
  isLoading.value = true;
  setTimeout(async () => {
    isLoading.value = false;
    await scrollToBottom();
  }, 250);
});

const templates = [
  { 
    label: 'Account Setup Instructions',
    value: 'setup',
    content: 'Here are the steps to set up your account:\n\n1. Log in to your dashboard\n2. Complete your profile\n3. Set up two-factor authentication'
  },
  { 
    label: 'Payment Confirmation',
    value: 'payment',
    content: 'Thank you for your payment. Your transaction has been processed successfully.'
  },
  { 
    label: 'Technical Support Response',
    value: 'support',
    content: 'I understand you\'re experiencing technical difficulties. Let me help you troubleshoot the issue.'
  }
];

const aiActions = [
  {
    label: 'Summarize Conversation',
    value: 'summarize',
    icon: 'pi pi-list'
  },
  {
    label: 'Generate Next Reply',
    value: 'generate',
    icon: 'pi pi-reply'
  },
  {
    label: 'Reformat Current Response',
    value: 'reformat',
    icon: 'pi pi-pencil'
  }
];

const handleAiAction = async () => {
  if (!selectedAiAction.value) return;

  try {
    switch (selectedAiAction.value) {
      case 'summarize':
        showSummaryDialog.value = true;
        isSummarizing.value = true;
        summaryContent.value = await openAiService.summarizeConversation(messages.value);
        break;
      case 'generate':
        isGeneratingReply.value = true;
        const generatedReply = await openAiService.generateReply(messages.value);
        // Update the editor content with the generated reply
        composerState.value.content = generatedReply;
        // Ensure the editor updates
        await nextTick();
        if (editorRef.value) {
          const editor = editorRef.value.getQuill();
          if (editor) {
            editor.setText(generatedReply);
          }
        }
        break;
      case 'reformat':
        if (!composerState.value.content.trim()) {
          return;
        }
        isReformatting.value = true;
        const reformattedContent = await openAiService.reformatResponse(composerState.value.content);
        // Update the editor content with the reformatted text
        composerState.value.content = reformattedContent;
        // Ensure the editor updates
        await nextTick();
        if (editorRef.value) {
          const editor = editorRef.value.getQuill();
          if (editor) {
            editor.setText(reformattedContent);
          }
        }
        break;
    }
  } catch (error) {
    console.error('AI action failed:', error);
    if (selectedAiAction.value === 'summarize') {
      summaryContent.value = 'Failed to process request. Please try again.';
    }
  } finally {
    isSummarizing.value = false;
    isGeneratingReply.value = false;
    isReformatting.value = false;
    selectedAiAction.value = null;
    showAiMenu.value = false;
  }
};
const composerState = ref({
  to: [] as string[],
  cc: [] as string[],
  bcc: [] as string[],
  subject: '',
  content: '',
  minimizedFields: true,
  height: 128
});

const editorOptions = {
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }]
    ]
  },
  placeholder: 'Write your message...',
  formats: ['bold', 'italic', 'underline', 'strike', 'list']
};

const handleEditorContentChange = () => {
  if (!editorRef.value) return;
  const editor = editorRef.value.getQuill();
  if (!editor) return;
  const editorContainer = editor.container.querySelector('.ql-editor');
  if (!editorContainer) return;
  const contentHeight = editorContainer.scrollHeight;
  composerState.value.height = Math.min(Math.max(contentHeight + 24, 128), 512);
};

const applyTemplate = () => {
  if (selectedTemplate.value) {
    const template = templates.find(t => t.value === selectedTemplate.value);
    if (template) {
      composerState.value.content = template.content;
    }
    selectedTemplate.value = null;
  }
};

const handleTemplateSelect = (template: any) => {
  composerState.value.content = template.content;
};

const handleAiActionSelect = (action: any) => {
  selectedAiAction.value = action.value;
  handleAiAction();
};

const handleInsertLink = () => {
  if (!textareaRef.value) return;
  const start = textareaRef.value.selectionStart;
  const end = textareaRef.value.selectionEnd;
  if (start === end) return;
  selectedText.value = composerState.value.content.substring(start, end);
  selectionRange.value = { start, end };
  showLinkDialog.value = true;
};

const insertLink = () => {
  if (!selectionRange.value || !linkUrl.value.trim()) return;
  const { start, end } = selectionRange.value;
  const linkHtml = `<a href="${linkUrl.value}">${selectedText.value}</a>`;
  composerState.value.content = 
    composerState.value.content.substring(0, start) +
    linkHtml +
    composerState.value.content.substring(end);
  showLinkDialog.value = false;
  linkUrl.value = '';
  selectedText.value = '';
  selectionRange.value = null;
};

const handleFileUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    const fileId = crypto.randomUUID();
    uploadingFiles.value.push({
      id: fileId,
      name: file.name,
      size: file.size,
      progress: 0
    });

    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      const fileIndex = uploadingFiles.value.findIndex(f => f.id === fileId);
      if (fileIndex !== -1) {
        uploadingFiles.value[fileIndex].progress = progress;
      }
      
      if (progress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== fileId);
          attachedFiles.value.push({
            id: fileId,
            name: file.name,
            size: file.size
          });
        }, 500);
      }
    }, 200);

    input.value = '';
  }
};

const startResizing = (e: MouseEvent) => {
  e.preventDefault();
  const startY = e.clientY;
  const startHeight = composerState.value.height;

  const handleMouseMove = (e: MouseEvent) => {
    const delta = startY - e.clientY;
    composerState.value.height = Math.min(Math.max(startHeight + delta, 128), 512);
  };

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.metadata-toggle') && !target.closest('.metadata-dropdown')) {
    showMetadata.value.clear();
  }
};

onMounted(() => {
  window.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  window.removeEventListener('click', handleClickOutside);
});

const getSenderDetails = (senderId: string) => {
  return props.communication.participants.find(p => p.id === senderId);
};

const toggleMessage = (messageId: string) => {
  if (expandedMessages.value.has(messageId)) {
    expandedMessages.value.delete(messageId);
  } else {
    expandedMessages.value.add(messageId);
  }
};

const isMessageExpanded = (messageId: string) => {
  return expandedMessages.value.has(messageId);
};

const formatEmailDate = (date: Date) => {
  return format(date, 'MMM d, yyyy · h:mm a');
};

const toggleMetadata = (messageId: string) => {
  if (showMetadata.value.has(messageId)) {
    showMetadata.value.delete(messageId);
  } else {
    showMetadata.value.add(messageId);
  }
};

const getMessageMetadata = (content: string) => {
  try {
    const messageData = JSON.parse(content);
    console.log('DBUG messageData', messageData);
    const context = messageData.context;
    // console.log('DBUG context', context);
    if (!context) return null;
    
    let toDisplay = context.to || context.to_name;
    // Special case handling for internal routing emails
    if (context.to) {
      const inboxPattern = /inbox\.[A-Z0-9-]+@smarttechtechnologies\.stage\.goboomtown\.com/;
      const commPattern = /comm\.[a-z0-9]+@smarttechtechnologies\.stage\.goboomtown\.com/;
      
      if (inboxPattern.test(context.to) || commPattern.test(context.to)) {
        toDisplay = '<EMAIL>';
      }
    }

    return {
      from: context.from,
      fromName: context.from_name || context.from,
      to: toDisplay,
      toName: context.to_name || context.to,
      subject: messageData.email?.subject || 'No subject',
      cc: messageData.email?.cc,
      bcc: context.bcc
    };
  } catch (e) {
    console.error('Error parsing message metadata:', e);
    return null;
  }
};

const formatRecipients = (content: string) => {
  const metadata = getMessageMetadata(content);
  if (!metadata) return 'No recipients';
  
  // Return the 'to' field, or fall back to 'No recipients'
  return metadata.to || 'No recipients';
};

const formatDetailedRecipients = (content: string) => {
  const metadata = getMessageMetadata(content);
  if (!metadata) return 'No recipients';
  
  // For detailed view, we might want to show more information
  const recipients = [];
  if (metadata.to) recipients.push(metadata.to);
  
  return recipients.join('; ') || 'No recipients';
};

const formatDetailedCC = (content: string) => {
  const metadata = getMessageMetadata(content);
  if (!metadata) return '';
  
  // For detailed view, we might want to show more information
  const recipients = [];
  if (metadata.cc) recipients.push(`${metadata.cc}`);
  
  return recipients.join('; ') || '';
};

const formatDetailedDate = (date: Date) => {
  return `${format(date, 'MMM d, yyyy, h:mm:ss a')} (${
    Intl.DateTimeFormat().resolvedOptions().timeZone
  })`;
};

const renderHTML = (content: string) => {
  try {
    const parsedContent = JSON.parse(content);
    const htmlContent = parsedContent.email.body;
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    doc.querySelectorAll('script').forEach(script => script.remove());
    doc.querySelectorAll('*').forEach(element => {
      Array.from(element.attributes).forEach(attr => {
        if (attr.name.startsWith('on')) {
          element.removeAttribute(attr.name);
        }
      });
    });
    return doc.body.innerHTML;
  } catch (error) {
    return '';
  }
};

const getEmailAttachments = (content: string) => {
  try {
    const parsedContent = JSON.parse(content);
    // Handle both single attachment and array of attachments
    const attachments = Array.isArray(parsedContent.attachment) 
      ? parsedContent.attachment 
      : parsedContent.attachment ? [parsedContent.attachment] : [];
    
    return attachments.map((attachment: any) => ({
      id: parsedContent.attachment.id,
      filename: parsedContent.attachment.name,
      size: parsedContent.attachment.size,
      type: parsedContent.attachment.type,
      preview: parsedContent.attachment.preview,
      url: parsedContent.attachment.url
    }));
  } catch (error) {
    return [];
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

const getPreviewText = (content: string) => {
  try {
    const parsedContent = JSON.parse(content);
    const htmlContent = parsedContent.email.body || '';
    const attachment = parsedContent.attachment;
    
    const div = document.createElement('div');
    div.innerHTML = htmlContent;
    
    const bodyText = div.textContent?.replace(/\s+/g, ' ').trim();
    
    if (attachment?.name) {
      return bodyText ? `📎 ${attachment.name} - ${bodyText}` : `📎 ${attachment.name}`;
    }

    // console.log('DBUG bodyText', bodyText);
    return bodyText || '';
  } catch (error) {
    return '';
  }
};

const sendEmail = async () => {
  if (composerState.value.content.trim()) {
    await store.sendMessage(props.communication.id, composerState.value.content.trim());
    composerState.value.content = '';
  }
};

// Add this computed property after other computed properties
const getEmailSummary = computed(() => {
  if (!props.communication.messages.length) return { to: 'No recipients' };

  // Get the latest message
  const latestMessage = props.communication.messages[props.communication.messages.length - 1];
  
  try {
    const messageData = JSON.parse(latestMessage.content);
    console.log('DBUG messageData', messageData);
    const context = messageData.context;
    console.log('DBUG context', context);
    
    if (!context) return { to: 'No recipients' };

    return {
      from: context.from_name || context.from,
      to: context.to_name || context.to,
      subject: messageData.email?.subject || 'No subject',
      cc: context.cc,
      bcc: context.bcc
    };
  } catch (e) {
    console.error('Error parsing email data:', e);
    return { to: 'No recipients' };
  }
});
</script>

<template>
  <div class="email-channel-container">
    <!-- Loading state -->
    <div 
      v-if="isLoading"
      class="flex items-center justify-center bg-white h-full"
    >
      <div class="text-center">
        <ProgressSpinner 
          style="width: 50px; height: 50px;" 
          strokeWidth="4" 
          animationDuration="1.5s"
        />
        <div class="mt-4 text-gray-600">
          Loading messages...
        </div>
      </div>
    </div>

    <!-- Content (only show when not loading) -->
    <template v-else>
      <!-- Messages area (scrollable) with space for composer -->
      <div ref="messagesContainer" class="messages-content-wrapper">
        <!-- Empty state -->
        <div 
          v-if="!messages.length"
          class="flex items-center justify-center h-full"
        >
          <BravoZeroStateScreen 
            title="No Messages"
            message="This email conversation doesn't have any messages yet. Start typing below to send the first message."
            :showButton="false"
            :imageSrc="EmailZeroStateSvg"
            image-alt="Email Messages"
            :action-handler="() => {}"
          />
        </div>
        
        <!-- Email thread -->
        <div v-else class="max-w-4xl mx-auto p-4">
          <!-- Message thread -->
          <div class="divide-y divide-gray-100">
            <template v-for="message in messages" :key="message.id">
              <div 
                class="py-4 message-item"
                :class="{ 'cursor-pointer hover:bg-gray-50': !isMessageExpanded(message.id) }"
                @click="!isMessageExpanded(message.id) && toggleMessage(message.id)"
              >
                <!-- Message header -->
                <div 
                  class="flex items-start gap-2 cursor-pointer"
                  @click.stop="toggleMessage(message.id)"
                >
                  <!-- Avatar -->
                  <div 
                    class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0 text-gray-600 font-medium"
                  >
                    {{ message.sender.initials }}
                  </div>

                  <div class="flex-1 min-w-0">
                    <div class="flex justify-between items-start">
                      <div class="flex flex-col">
                        <span class="font-medium text-gray-900 leading-none">{{ message.sender.name }}</span>

                      </div>
                      <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500 whitespace-nowrap">
                          {{ formatEmailDate(message.timestamp) }}
                        </span>
                      </div>
                    </div>
                    <div class="relative w-full">
                      <div class="flex items-center space-x-1 max-w-full">
                        <span class="text-sm text-gray-500 mt-0.5 truncate inline-flex items-center max-w-[calc(100%-24px)]">
                          <span class="truncate">to {{ formatRecipients(message.content) }}</span>
                        </span>
                        <button 
                          class="p-1 -m-1 text-gray-500 hover:text-gray-700 metadata-toggle flex-shrink-0"
                          @click.stop="toggleMetadata(message.id)"
                        >
                          <i class="pi pi-chevron-down text-xs"></i>
                        </button>
                      </div>
                      
                      <!-- Email metadata dropdown -->
                      <div 
                        v-if="showMetadata.has(message.id)"
                        class="absolute left-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 w-[500px] metadata-dropdown"
                        @click.stop
                      >
                        <div class="p-4 space-y-2 text-sm">
                          <div class="grid grid-cols-[80px_1fr] gap-2">
                            <span class="text-gray-500">from:</span>
                            <span>{{ message.sender.name }} &lt;{{ message.sender.email }}&gt;</span>
                            
                            <span class="text-gray-500">to:</span>
                            <span>{{ formatDetailedRecipients(message.content) }}</span>
                            
                            <template v-if="getMessageMetadata(message.content)?.cc?.length">
                              <span class="text-gray-500">cc:</span>
                              <span>{{ formatDetailedCC(message.content) }}</span>
                            </template>
                            
                            <span class="text-gray-500">date:</span>
                            <span>{{ formatDetailedDate(message.timestamp) }}</span>
                            
                            <span class="text-gray-500">subject:</span>
                            <span>{{ getMessageMetadata(message.content)?.subject || communication.title }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Message preview -->
                    <div
                      v-if="!isMessageExpanded(message.id)"
                      class="text-gray-600 line-clamp-1 mt-2 pointer-events-none"
                    >
                      {{ getPreviewText(message.content) }}
                    </div>
                  </div>
                </div>

                <!-- Expanded message -->
                <div 
                  v-if="isMessageExpanded(message.id)"
                  class="mt-2 pl-10 cursor-default"
                  @click.stop
                >
                  <div 
                    class="prose text-gray-800 max-w-full break-words"
                    v-html="renderHTML(message.content)"
                  ></div>

                  <!-- Attachments -->
                  <div 
                    v-if="getEmailAttachments(message.content).length"
                    class="mt-4 space-y-2"
                  >
                    <div class="text-sm font-medium text-gray-500 mb-2">
                      Attachments ({{ getEmailAttachments(message.content).length }})
                    </div>
                    <div 
                      v-for="attachment in getEmailAttachments(message.content)"
                      :key="attachment.id"
                      class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg attachment-item"
                    >
                      <i 
                        class="pi text-gray-400 text-xl"
                        :class="{
                          'pi-file-pdf': attachment.type === 'application/pdf',
                          'pi-image': attachment.type.startsWith('image/'),
                          'pi-file': !attachment.type.startsWith('image/') && attachment.type !== 'application/pdf'
                        }"
                      ></i>
                      <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">{{ attachment.filename }}</div>
                        <div class="text-sm text-gray-500">{{ formatFileSize(attachment.size) }}</div>
                      </div>
                      <a 
                        :href="attachment.url"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="p-2 hover:bg-gray-100 rounded"
                        download
                      >
                        <i class="pi pi-download"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      
      <!-- Email composer - sticky to bottom with absolute positioning -->
      <div class="sticky-composer-container">
        <div 
          class="h-1 bg-gray-100 hover:bg-gray-300 cursor-ns-resize relative group resize-handle"
          @mousedown="startResizing"
        >
          <div class="absolute inset-x-0 top-1/2 -translate-y-1/2 h-4 opacity-0 group-hover:opacity-100 transition-opacity">
            <div class="mx-auto w-16 h-1 bg-gray-400 rounded"></div>
          </div>
        </div>
        <div class="max-w-4xl mx-auto px-4 pt-4 pb-2">
          <!-- Attached files -->
          <div v-if="attachedFiles.length" class="mb-4">
            <div class="text-sm font-medium text-gray-500 mb-2">
              Attached Files
            </div>
            <div class="space-y-2">
              <div 
                v-for="file in attachedFiles"
                :key="file.id"
                class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg attachment-item"
              >
                <i class="pi pi-file text-gray-400 text-xl"></i>
                <div class="flex-1 min-w-0">
                  <div class="font-medium truncate">{{ file.name }}</div>
                  <div class="text-sm text-gray-500">
                    {{ Math.round(file.size / 1024) }} KB
                  </div>
                </div>
                <button 
                  class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                  @click="attachedFiles = attachedFiles.filter(f => f.id !== file.id)"
                  title="Remove file"
                >
                  <i class="pi pi-times"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- File upload progress -->
          <div v-if="uploadingFiles.length" class="mb-4 space-y-2">
            <FileUploadProgress
              v-for="file in uploadingFiles"
              :key="file.id"
              :file-name="file.name"
              :size="file.size"
              :progress="file.progress"
            />
          </div>

          <div v-if="!composerState.minimizedFields" class="space-y-3 mb-4">
            <div class="flex items-center space-x-2">
              <span class="w-12 text-gray-500 pl-1">To:</span>
              <input 
                type="text" 
                class="flex-1 border-0 border-b border-gray-200 px-0 py-1 focus:ring-0 focus:border-blue-500 text-[#282A2C]"
                placeholder="Recipients"
              >
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-12 text-gray-500 pl-1">CC:</span>
              <input 
                type="text" 
                class="flex-1 border-0 border-b border-gray-200 px-0 py-1 focus:ring-0 focus:border-blue-500 text-[#282A2C]"
                placeholder="Carbon copy recipients"
              >
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-12 text-gray-500 pl-1">BCC:</span>
              <input 
                type="text" 
                class="flex-1 border-0 border-b border-gray-200 px-0 py-1 focus:ring-0 focus:border-blue-500 text-[#282A2C]"
                placeholder="Blind carbon copy recipients"
              >
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-12 text-gray-500 pl-1">Subject:</span>
              <input 
                type="text" 
                v-model="composerState.subject"
                class="flex-1 border-0 border-b border-gray-200 px-0 py-1 focus:ring-0 focus:border-blue-500 text-[#282A2C]"
                placeholder="Email subject"
              >
            </div>
          </div>
          
          <div class="bg-white relative">
            <Editor
              v-model="composerState.content"
              :editorStyle="{ height: `${composerState.height}px`, 'font-family': 'inherit' }"
              :disabled="isGeneratingReply || isReformatting"
              ref="editorRef"
              :class="{ 'toolbar-visible': showEditorToolbar }"
              :options="editorOptions"
              class="email-editor"
              @text-change="handleEditorContentChange"
            />
            
            <!-- Loading overlay for reply generation -->
            <div 
              v-if="isGeneratingReply || isReformatting"
              class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"
            >
              <div class="text-center">
                <ProgressSpinner 
                  style="width: 40px; height: 40px;" 
                  strokeWidth="4" 
                  animationDuration="1.5s"
                />
                <div class="mt-2 text-gray-600 text-sm">
                  {{ isGeneratingReply ? 'Generating reply...' : 'Reformatting response...' }}
                </div>
              </div>
            </div>
            
            <div class="flex justify-between items-center pt-2">
              <div class="flex items-center space-x-2">
                <!-- 1. Formatting options -->
                <BravoButton
                  text
                  severity="secondary"
                  icon="pi pi-palette"
                  @click="showEditorToolbar = !showEditorToolbar"
                  aria-label="Formatting Options"
                />
                
                <!-- 2. Attach file -->
                <BravoButton
                  text
                  severity="secondary"
                  icon="pi pi-paperclip"
                  @click="fileInput?.click()"
                  aria-label="Attach File"
                />
                
                <!-- 3. Quick reply -->
                <div class="relative">
                  <BravoButton
                    text
                    severity="secondary"
                    icon="pi pi-comments"
                    @click="showTemplates = !showTemplates; showAiMenu = false"
                    aria-label="Quick Reply"
                  />
                  
                  <!-- Quick Reply Dropdown -->
                  <div 
                    v-if="showTemplates"
                    class="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[250px]"
                  >
                    <div class="p-2">
                      <div class="text-xs font-medium text-gray-500 mb-2 px-2">Quick Reply Templates</div>
                      <div 
                        v-for="template in templates" 
                        :key="template.value"
                        class="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer"
                        @click="() => { handleTemplateSelect(template); showTemplates = false; }"
                      >
                        {{ template.label }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 4. AI Assist -->
                <div class="relative">
                  <BravoButton
                    text
                    severity="secondary"
                    icon="pi pi-sparkles"
                    @click="showAiMenu = !showAiMenu; showTemplates = false"
                    aria-label="AI Assistant"
                  />
                  
                  <!-- AI Actions Dropdown -->
                  <div 
                    v-if="showAiMenu"
                    class="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[220px]"
                  >
                    <div class="p-2">
                      <div class="text-xs font-medium text-gray-500 mb-2 px-2">AI Actions</div>
                      <div 
                        v-for="action in aiActions" 
                        :key="action.value"
                        class="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer flex items-center gap-2"
                        @click="() => { handleAiActionSelect(action); showAiMenu = false; }"
                      >
                        <i :class="action.icon" class="text-xs"></i>
                        {{ action.label }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 5. Show Fields -->
                <BravoButton
                  text
                  severity="secondary"
                  :label="composerState.minimizedFields ? 'Show Fields' : 'Hide Fields'"
                  @click="composerState.minimizedFields = !composerState.minimizedFields"
                  aria-label="Toggle To and Subject fields"
                />
              </div>
              
              <!-- Send button -->
              <BravoButton
                label="Send"
                icon="pi pi-send"
                :disabled="!composerState.content.trim() || isGeneratingReply || isReformatting"
                @click="sendEmail"
                aria-label="Send Email"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
      
    <!-- Link Dialog -->
    <Dialog
      v-model:visible="showLinkDialog"
      modal
      header="Insert Link"
      :style="{ width: '400px' }"
      :closable="true"
    >
      <div class="space-y-4 p-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Text to Link
          </label>
          <div class="text-gray-600 bg-gray-50 p-2 rounded">
            {{ selectedText }}
          </div>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            URL
          </label>
          <input
            type="url"
            v-model="linkUrl"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="https://"
            @keydown.enter="insertLink"
          >
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
            @click="showLinkDialog = false"
          >
            Cancel
          </button>
          <button
            class="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700"
            @click="insertLink"
            :disabled="!linkUrl.trim()"
          >
            Insert
          </button>
        </div>
      </template>
    </Dialog>

    <!-- Summary Dialog -->
    <Dialog
      v-model:visible="showSummaryDialog"
      modal
      header="Conversation Summary"
      :style="{ width: '600px' }"
      :closable="true"
    >
      <div class="p-4">
        <div v-if="isSummarizing" class="flex flex-col items-center justify-center py-8">
          <ProgressSpinner 
            style="width: 50px; height: 50px;" 
            strokeWidth="4" 
            animationDuration="1.5s"
          />
          <div class="mt-4 text-gray-600">
            Generating summary...
          </div>
        </div>
        <div 
          v-else
          class="prose max-w-none"
          v-html="summaryContent"
        ></div>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <button
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
            @click="showSummaryDialog = false"
          >
            Close
          </button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
/* Main container similar to CaseCopilotPanel */
.email-channel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100%;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  background-color: white;
}

/* Messages content wrapper - scrollable area with space for composer */
.messages-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  min-height: 0; /* Allow flex child to shrink */
  margin-bottom: 200px; /* Space for sticky composer */
}

/* Sticky composer container - positioned absolutely at bottom */
.sticky-composer-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  z-index: 10;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, .1);
}

.email-metadata-dropdown {
  min-width: 500px;
  max-width: calc(100vw - 2rem);
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ensure the email thread container uses full height */
:deep(.prose) {
  max-width: none;
  width: 100%;
}

:deep(.prose p) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

:deep(.prose) {
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}

:deep(.prose img) {
  max-width: 100%;
  height: auto;
}

:deep(.prose a) {
  color: #2563eb;
  text-decoration: underline;
}

:deep(.prose ul),
:deep(.prose ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
  list-style-position: outside;
}

:deep(.prose ul) {
  list-style-type: disc;
}

:deep(.prose ol) {
  list-style-type: decimal;
}

:deep(.prose ul ul) {
  list-style-type: circle;
  margin: 0;
}

:deep(.prose ol ol) {
  list-style-type: lower-alpha;
  margin: 0;
}

:deep(.prose li) {
  margin: 0.25em 0;
  padding-left: 0.5em;
}

:deep(.prose blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  color: #4b5563;
}

:deep(.p-dropdown) {
  @apply border border-gray-200 rounded-lg shadow-lg;
  min-width: 200px;
}

:deep(.p-dropdown-item) {
  @apply px-4 py-2;
}

:deep(.p-dialog-header) {
  @apply py-4 px-6 border-b border-gray-200;
}

:deep(.p-dialog-content) {
  @apply p-0;
}

:deep(.p-dialog-footer) {
  @apply p-4 border-t border-gray-200;
}

/* Email editor improvements */
:deep(.email-editor) {
  .ql-toolbar {
    display: none;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
  }

  &.toolbar-visible .ql-toolbar {
    display: block;
  }

  .ql-container {
    border: none;
    border-radius: 6px;
  }

  .ql-editor {
    padding: 1rem;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    min-height: 120px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  /* Remove any default Quill borders */
  .ql-toolbar,
  .ql-container,
  .ql-editor {
    border: none !important;
  }

  /* Apply our custom border only to the editor */
  .ql-editor {
    border: 1px solid #e5e7eb !important;
    border-radius: 6px;
  }

  &.toolbar-visible .ql-toolbar {
    border: none !important;
    border-bottom: 1px solid #e5e7eb !important;
  }
}

/* Custom scrollbar for messages container */
.messages-content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.messages-content-wrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.messages-content-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.messages-content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}




/* Resize handle styling */
.resize-handle {
  position: relative;
  transition: all 0.2s ease;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 4px;
  background: #d1d5db;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resize-handle:hover::before {
  opacity: 1;
}
</style>