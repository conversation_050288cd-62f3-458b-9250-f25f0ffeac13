<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import type { Communication, Message } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { useCommunicationsStore } from '../../stores/communications';
import { xmppService } from '../../services/xmpp';
import FileUploadProgress from '../FileUploadProgress.vue';
import Dropdown from 'primevue/dropdown';
import Avatar from '../Avatar.vue';
import CryptoJS from 'crypto-js';
import ProgressSpinner from 'primevue/progressspinner';
import { useXMPPCommunications } from '@/composables/services/useXMPPCommunications';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';

const props = defineProps<{
  communication: Communication;
}>();

const fileInput = ref<HTMLInputElement | null>(null);
const uploadingFiles = ref<Array<{ id: string; name: string; size: number; progress: number }>>([]);
const attachedFiles = ref<Array<{ id: string; name: string; size: number }>>([]);
const showTemplates = ref(false);
const selectedTemplate = ref(null);
const mentionDropdown = ref<{
  show: boolean;
  top: number;
  left: number;
}>({
  show: false,
  top: 0,
  left: 0
});
const filteredUsers = ref<Array<{ id: string; name: string }>>([]);
const mentionQuery = ref('');
const mentionStartIndex = ref<number | null>(null);
const selectedMentionIndex = ref(0);

// Add loading state
const isLoading = ref(true);

// Get the communication from the store to ensure we have the latest data
const storeComm = computed(() => {
  return store.communications.find(c => c.id === props.communication.id) || props.communication;
});

// Use messages from the store communication
const messages = computed(() => {
  return storeComm.value.messages || [];
});

// Function to format message content with highlighted mentions
const formatMessageContent = (content: string) => {
  // Replace @mentions with styled spans
  return content.replace(/@(\w+)/g, '<span class="mention">@$1</span>');
};

const templates = [
  { 
    label: 'Account Setup Instructions',
    value: 'setup',
    content: 'Here are the steps to set up your account:\n\n1. Log in to your dashboard\n2. Complete your profile\n3. Set up two-factor authentication'
  },
  { 
    label: 'Payment Confirmation',
    value: 'payment',
    content: 'Thank you for your payment. Your transaction has been processed successfully.'
  },
  { 
    label: 'Technical Support Response',
    value: 'support',
    content: 'I understand you\'re experiencing technical difficulties. Let me help you troubleshoot the issue.'
  }
];

const applyTemplate = () => {
  if (selectedTemplate.value) {
    const template = templates.find(t => t.value === selectedTemplate.value);
    if (template) {
      newMessage.value = template.content;
    }
    selectedTemplate.value = null;
  }
};

const textareaRef = ref<HTMLTextAreaElement | null>(null);
const store = useCommunicationsStore();
const newMessage = ref('');
const messagesContainer = ref<HTMLElement | null>(null);
const expandedMessages = ref<Set<string>>(new Set());

// Import XMPP composable for connection status
const xmppComms = useXMPPCommunications();

const adjustTextareaHeight = async () => {
  if (!textareaRef.value) return;
  
  // Reset height to auto to get the correct scrollHeight
  textareaRef.value.style.height = 'auto';
  
  // Calculate new height (min 80px, max 300px)
  const newHeight = Math.min(Math.max(textareaRef.value.scrollHeight, 80), 300);
  textareaRef.value.style.height = `${newHeight}px`;
  
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const handleInput = (e: Event) => {
  const textarea = e.target as HTMLTextAreaElement;
  adjustTextareaHeight();

  const text = textarea.value;
  if (!text) return;

  const caretPos = textarea.selectionStart;
  
  // Find the last @ before the caret
  const lastAtPos = text.lastIndexOf('@', caretPos - 1);
  
  if (lastAtPos !== -1 && (lastAtPos === 0 || text[lastAtPos - 1] === ' ')) {
    const query = text.substring(lastAtPos + 1, caretPos).toLowerCase();
    mentionQuery.value = query;
    mentionStartIndex.value = lastAtPos;
    
    // Filter users based on query
    filteredUsers.value = props.communication.participants
      .filter(p => p.name.toLowerCase().includes(query))
      .map(p => ({ id: p.id, name: p.name }));

    // Reset selected index when filtering
    selectedMentionIndex.value = 0;
    
    if (filteredUsers.value.length > 0) {
      // Position the dropdown below the @ symbol
      const coords = getCaretCoordinates(textarea, lastAtPos);
      mentionDropdown.value = {
        show: true,
        top: coords.top - 200, // Position above with space for the dropdown
        left: coords.left
      };
    } else {
      mentionDropdown.value.show = false;
    }
  } else {
    mentionDropdown.value.show = false;
    mentionStartIndex.value = null;
  }
};

const handleEnterKey = () => {
  if (mentionDropdown.value.show) {
    insertMention(filteredUsers.value[selectedMentionIndex.value]);
  } else {
    sendMessage();
  }
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (!mentionDropdown.value.show) return;

  switch (e.key) {
    case 'Tab':
      e.preventDefault();
      insertMention(filteredUsers.value[selectedMentionIndex.value]);
      break;
    case 'ArrowDown':
      e.preventDefault();
      selectedMentionIndex.value = (selectedMentionIndex.value + 1) % filteredUsers.value.length;
      break;
    case 'ArrowUp':
      e.preventDefault();
      selectedMentionIndex.value = selectedMentionIndex.value - 1;
      if (selectedMentionIndex.value < 0) selectedMentionIndex.value = filteredUsers.value.length - 1;
      break;
    case 'Enter':
      e.preventDefault();
      break;
    case 'Escape':
      mentionDropdown.value.show = false;
      break;
  }
};

const getCaretCoordinates = (element: HTMLTextAreaElement, position: number) => {
  const { offsetLeft: inputX, offsetTop: inputY } = element;
  
  // Create a temporary div to measure text
  const div = document.createElement('div');
  const styles = window.getComputedStyle(element);
  
  // Copy styles to ensure accurate measurement
  const styleProperties = [
    'fontFamily', 'fontSize', 'fontWeight', 'letterSpacing',
    'padding', 'border', 'wordSpacing', 'lineHeight'
  ];
  
  styleProperties.forEach(prop => {
    (div.style as any)[prop] = (styles as any)[prop];
  });
  
  // Set content and styles
  div.style.position = 'absolute';
  div.style.visibility = 'hidden';
  div.style.whiteSpace = 'pre-wrap';
  div.style.width = `${element.offsetWidth}px`;
  
  // Add text up to the caret position
  div.textContent = element.value.substring(0, position);
  
  // Add a span at caret position to measure its position
  const span = document.createElement('span');
  span.textContent = element.value.charAt(position) || '.';
  div.appendChild(span);
  
  document.body.appendChild(div);
  const { offsetLeft: spanX, offsetTop: spanY } = span;
  document.body.removeChild(div);
  
  return {
    left: inputX + spanX,
    top: inputY + spanY,
    height: parseInt(styles.lineHeight)
  };
};

const insertMention = (user: { id: string; name: string }) => {
  if (mentionStartIndex.value === null || !textareaRef.value) return;
  
  if (!newMessage.value) {
    newMessage.value = '';
  }

  const start = mentionStartIndex.value;
  const end = textareaRef.value.selectionStart;
  const beforeMention = newMessage.value.slice(0, start);
  const afterMention = newMessage.value.slice(end);
  
  newMessage.value = `${beforeMention}@${user.name} ${afterMention}`;
  mentionDropdown.value.show = false;
  mentionStartIndex.value = null;
  
  // Set focus back to textarea
  nextTick(() => {
    if (textareaRef.value) {
      const newCursorPos = start + user.name.length + 2; // +2 for @ and space
      textareaRef.value.focus();
      textareaRef.value.setSelectionRange(newCursorPos, newCursorPos);
    }
  });
};

const handleFileUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    
    // Use the store's sendFile method
    store.sendFile(storeComm.value.id, file).catch(error => {
      console.error('Failed to upload file:', error);
    });

    input.value = ''; // Reset the input
  }
};

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// Watch for new messages and scroll to bottom
watch(() => messages.value.length, (newLength, oldLength) => {
  // If new messages were added, expand only the latest one
  if (newLength > oldLength) {
    const latestMessage = messages.value[messages.value.length - 1];
    if (latestMessage) {
      expandedMessages.value = new Set([latestMessage.id]);
    }
  }
  scrollToBottom();
});

// Initial scroll on mount
onMounted(async () => {
  isLoading.value = true;
  try {
    // Clear any existing expanded messages
    expandedMessages.value.clear();
    
    // If there are messages, expand only the most recent one
    const latestMessage = messages.value[messages.value.length - 1];
    if (latestMessage) {
      expandedMessages.value = new Set([latestMessage.id]);
    }
    
    // Scroll to the bottom to show the expanded message
  } finally {
    // Short delay to prevent flash
    setTimeout(async() => {
      isLoading.value = false;
      await scrollToBottom();
    }, 250);
  }
});

// Add watcher for communication changes
watch(() => props.communication.id, () => {
  isLoading.value = true;
  // Short delay to prevent flash
  setTimeout(() => {
    isLoading.value = false;
  }, 100);
});

const sendMessage = () => {
  if (newMessage.value.trim()) {
    store.sendMessage(storeComm.value.id, newMessage.value.trim());
    newMessage.value = '';
  }
};

// Group messages by sender and time (within 1 minute)
const groupedMessages = computed(() => {
  const groups: Message[][] = [];
  let currentGroup: Message[] = [];

  messages.value.forEach((message, index) => {
    const prevMessage = messages.value[index - 1];
    
    const shouldStartNewGroup = () => {
      if (!prevMessage) return true;
      
      const timeDiff = message.timestamp.getTime() - prevMessage.timestamp.getTime();
      const isWithinTimeWindow = timeDiff <= 60000; // 1 minute
      const isSameSender = message.senderId === prevMessage.senderId;
      
      return !isWithinTimeWindow || !isSameSender;
    };

    if (shouldStartNewGroup()) {
      if (currentGroup.length > 0) {
        groups.push(currentGroup);
      }
      currentGroup = [message];
    } else {
      currentGroup.push(message);
    }
  });

  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  return groups;
});

const getSenderDetails = (senderId: string) => {
  return storeComm.value.participants.find(p => p.id === senderId);
};

const isCurrentUser = (senderId: string) => {
  return store.isCurrentUser(senderId);
};

const getAvatarUrl = (senderId: string) => {
  if (isCurrentUser(senderId)) {
    return store.appConfig?.serviceConfig.relay_avatar;
  }
  
  const participant = getSenderDetails(senderId);
  return participant?.avatar;
};

const getFilePreview = (message: Message) => {
  if (!message.metadata?.fileUrl) return null;
  
  const fileType = message.metadata.fileName?.split('.').pop()?.toLowerCase();
  
  if (['jpg', 'jpeg', 'png', 'gif'].includes(fileType || '')) {
    return {
      type: 'image',
      url: message.metadata.fileUrl
    };
  }
  
  return {
    type: 'file',
    name: message.metadata.fileName,
    size: message.metadata.fileSize
  };
};

const isConnected = computed(() => {
  return xmppComms.isRoomConnected(storeComm.value);
});

const connectionStatus = computed(() => {
  const commFromStore = store.communications.find(c => c.id === props.communication.id);
  const roomJid = commFromStore?.roomJid || storeComm.value.roomJid;
  
  console.log('🔍 ChatChannel: Connection status check:', {
    communicationId: props.communication.id,
    isInitialized: xmppComms.isInitialized.value,
    isConnected: xmppComms.isConnected.value,
    propsRoomJid: props.communication.roomJid,
    storeCommRoomJid: storeComm.value.roomJid,
    commFromStoreRoomJid: commFromStore?.roomJid,
    finalRoomJid: roomJid,
    storeCommCount: store.communications.length,
    isRoomConnected: roomJid ? xmppComms.isRoomConnected(storeComm.value) : 'no roomJid'
  });

  if (!xmppComms.isInitialized.value) {
    return { status: 'initializing', message: 'Initializing...' };
  }
  if (!xmppComms.isConnected.value) {
    return { status: 'disconnected', message: 'Disconnected' };
  }
  if (roomJid && !xmppComms.isRoomConnected(storeComm.value)) {
    return { status: 'connecting', message: 'Connecting...' };
  }
  return { status: 'connected', message: 'Connected' };
});

const endChatMessageSecret = CryptoJS.MD5('MessengerUserExit').toString();

interface ParsedMessage {
  message?: string;
  attachment?: {
    id: string;
    name: string;
    size: number;
    type: string;
    preview: string;
    url: string;
  };
  actions?: any[];
  kb?: any;
  secret?: string;
}

const tryParseMessage = (content: string): { isParsed: boolean; data: string | ParsedMessage } => {
  try {
    const parsed = JSON.parse(content);
    // Check if it matches our expected format
    if (typeof parsed === 'object' && parsed !== null) {
      // Check for secret
      if (parsed.secret === endChatMessageSecret) {
        console.log('End chat message received:', parsed);
      }
      return { isParsed: true, data: parsed as ParsedMessage };
    }
    return { isParsed: false, data: content };
  } catch {
    return { isParsed: false, data: content };
  }
};

// Helper functions for template
const getParsedMessageData = (content: string): ParsedMessage | null => {
  const parsed = tryParseMessage(content);
  return parsed.isParsed ? parsed.data as ParsedMessage : null;
};

const hasMessageText = (content: string): boolean => {
  const data = getParsedMessageData(content);
  return data?.message ? true : false;
};

const getMessageText = (content: string): string => {
  const data = getParsedMessageData(content);
  return data?.message || '';
};

const hasAttachment = (content: string): boolean => {
  const data = getParsedMessageData(content);
  return data?.attachment ? true : false;
};

const getAttachment = (content: string) => {
  const data = getParsedMessageData(content);
  return data?.attachment;
};

const hasActions = (content: string): boolean => {
  const data = getParsedMessageData(content);
  return data?.actions?.length ? true : false;
};

const hasKnowledgeBase = (content: string): boolean => {
  const data = getParsedMessageData(content);
  return data?.kb ? true : false;
};

const openImageInNewTab = (url: string) => {
  window.open(url, '_blank', 'noopener,noreferrer');
};
</script>

<template>
  <div class="chat-channel-container">
    <!-- Loading state -->
    <div 
      v-if="isLoading"
      class="flex-1 flex items-center justify-center bg-white"
    >
      <div class="text-center">
        <ProgressSpinner 
          style="width: 50px; height: 50px;" 
          strokeWidth="4" 
          animationDuration="1.5s"
        />
        <div class="mt-4 text-gray-600">
          Loading messages...
        </div>
      </div>
    </div>

    <!-- Content (only show when not loading) -->
    <template v-else>
      <!-- Messages area (scrollable) with space for composer -->
      <div class="messages-content-wrapper">
        <!-- Empty state -->
        <BravoZeroStateScreen
          v-if="!messages.length"
          title="Start New Chat"
          message="Start a new chat conversation by sending your first message."
          :showButton="false"
          :imageSrc="EmailZeroStateSvg"
          image-alt="Chat Messages"
          :action-handler="() => {}"
          class="flex items-center justify-center h-full"
        />

        <!-- Connection status -->
        <div
          v-if="connectionStatus.status !== 'connected' && messages.length"
          class="px-4 py-2 text-sm flex items-center space-x-2"
          :class="[
            connectionStatus.status === 'connected' ? 'bg-green-100 text-green-700' : 
            connectionStatus.status === 'connecting' ? 'bg-yellow-100 text-yellow-700' :
            'bg-red-100 text-red-700'
          ]"
        >
          <div 
            class="w-2 h-2 rounded-full"
            :class="[
              connectionStatus.status === 'connected' ? 'bg-green-500' : 
              connectionStatus.status === 'connecting' ? 'bg-yellow-500' :
              'bg-red-500'
            ]"
          ></div>
          <span>{{ connectionStatus.message }}</span>
        </div>

        <div 
          v-if="messages.length"
          ref="messagesContainer"
          class="flex-1 overflow-y-auto p-4 space-y-4"
        >
          <!-- Message groups -->
          <template v-for="(group, groupIndex) in groupedMessages" :key="groupIndex">
            <!-- System notifications -->
            <div 
              v-if="group[0].type === 'system'"
              class="flex justify-center"
            >
              <div class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm">
                {{ group[0].content }}
              </div>
            </div>

            <!-- Chat messages -->
            <div 
              v-else
              class="flex flex-col"
              :class="[isCurrentUser(group[0].senderId) ? 'items-end' : 'items-start']"
            >
              <!-- Sender info -->
              <div 
                class="flex items-center mb-1 w-full"
                :class="[isCurrentUser(group[0].senderId) ? 'justify-end' : 'justify-start']"
              >
                <span class="text-sm text-gray-600">
                  {{ group[0].senderName || getSenderDetails(group[0].senderId)?.name || 'Guest' }}
                </span>
                <span class="text-xs text-gray-400 ml-2">
                  {{ formatDistanceToNow(group[0].timestamp, { addSuffix: true }) }}
                </span>
              </div>

              <!-- Messages with Avatar -->
              <div 
                class="flex items-start space-x-2"
                :class="[isCurrentUser(group[0].senderId) ? 'flex-row-reverse space-x-reverse' : 'flex-row']"
              >
                <Avatar
                  :src="getAvatarUrl(group[0].senderId)"
                  :name="group[0].senderName || getSenderDetails(group[0].senderId)?.name || 'Guest'"
                  size="sm"
                />
                
                <div class="space-y-1 max-w-[80%]">
                  <div 
                    v-for="message in group" 
                    :key="message.id"
                    class="flex"
                    :class="[isCurrentUser(message.senderId) ? 'justify-end' : 'justify-start']"
                  >
                    <!-- Text message -->
                    <div 
                      v-if="!message.metadata?.fileUrl"
                      class="inline-block px-4 py-2 rounded-lg"
                      :class="[
                        isCurrentUser(message.senderId) 
                          ? 'bg-[#E4E5E7] text-gray-800 whitespace-pre-wrap' 
                          : 'bg-[#E5F3FF] text-gray-800 whitespace-pre-wrap'
                      ]"
                    >
                      <!-- Parse and display message content -->
                      <template v-if="getParsedMessageData(message.content)">
                        <div class="space-y-2">
                          <!-- Text message -->
                          <div v-if="hasMessageText(message.content)">
                            {{ getMessageText(message.content) }}
                          </div>

                          <!-- Attachment -->
                          <div 
                            v-if="hasAttachment(message.content)"
                            class="rounded overflow-hidden"
                          >
                            <img 
                              v-if="getAttachment(message.content)?.type.startsWith('image/')"
                              :src="getAttachment(message.content)?.preview"
                              :alt="getAttachment(message.content)?.name"
                              class="max-w-[300px] h-auto cursor-pointer"
                              @click="openImageInNewTab(getAttachment(message.content)?.url || '')"
                            />
                            <div 
                              v-else
                              class="p-2 bg-white bg-opacity-10 rounded flex items-center gap-2"
                            >
                              <i class="pi pi-file"></i>
                              <span>{{ getAttachment(message.content)?.name }}</span>
                            </div>
                          </div>

                          <!-- Actions placeholder -->
                          <div 
                            v-if="hasActions(message.content)"
                            class="text-sm italic"
                          >
                            Actions available (not implemented)
                          </div>

                          <!-- KB placeholder -->
                          <div 
                            v-if="hasKnowledgeBase(message.content)"
                            class="text-sm italic"
                          >
                            Knowledge Base content (not implemented)
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <!-- Regular text message -->
                        <span 
                          v-html="formatMessageContent(message.content)"
                          class="inline-block"
                        ></span>
                      </template>
                    </div>

                    <!-- File message -->
                    <div 
                      v-else
                      class="inline-block overflow-hidden rounded-lg"
                      :class="[
                        isCurrentUser(message.senderId) 
                          ? 'bg-[#E4E5E7] text-gray-800' 
                          : 'bg-[#E5F3FF] text-gray-800'
                      ]"
                    >
                      <!-- Image preview -->
                      <img 
                        v-if="getFilePreview(message)?.type === 'image'"
                        :src="message.metadata.fileUrl"
                        class="max-w-full h-auto max-h-48 object-contain"
                        alt="Image attachment"
                      >
                      
                      <!-- File preview -->
                      <div 
                        v-else
                        class="p-4 flex items-center space-x-3"
                      >
                        <i class="pi pi-file text-2xl"></i>
                        <div class="flex-1 min-w-0">
                          <div class="truncate">{{ message.metadata.fileName }}</div>
                          <div class="text-sm opacity-75">
                            {{ Math.round(message.metadata.fileSize! / 1024) }} KB
                          </div>
                        </div>
                        <a 
                          :href="message.metadata.fileUrl"
                          target="_blank"
                          rel="noopener noreferrer"
                          class="p-2 hover:bg-black/10 rounded"
                        >
                          <i class="pi pi-download"></i>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Typing indicator -->
          <div 
            v-if="communication.customerTyping"
            class="flex items-center space-x-2 text-gray-500"
          >
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
            </div>
            <span class="text-sm">Customer is typing...</span>
          </div>
        </div>
      </div>
      
      <!-- Chat composer - sticky to bottom with absolute positioning -->
      <div class="sticky-composer-container">
        <div class="max-w-4xl mx-auto px-4 pt-4 pb-2">
          <!-- Message composer -->
          <div class="bg-white">
            <textarea 
              ref="textareaRef"
              v-model="newMessage"
              class="w-full resize-none focus:outline-none min-h-[80px] p-3 text-[#282A2C] border border-gray-200 rounded-lg"
              placeholder="Type your message..."
              @input="handleInput"
              @keydown.exact="handleKeyDown"
              @keydown.tab.prevent="mentionDropdown.show && insertMention(filteredUsers[selectedMentionIndex])"
              @keydown.enter.exact.prevent="handleEnterKey"
              @keydown.shift.enter="(e: KeyboardEvent) => {
                const target = e.target as HTMLTextAreaElement;
                if (!target) return;
                const start = target.selectionStart;
                const end = target.selectionEnd;
                newMessage = newMessage.substring(0, start) + '\n' + newMessage.substring(end);
                nextTick(() => {
                  target.selectionStart = target.selectionEnd = start + 1;
                });
              }"
            ></textarea>
            
            <!-- Mention dropdown -->
            <div 
              v-if="mentionDropdown.show"
              class="absolute bg-white rounded-lg shadow-lg border border-gray-200 h-[200px] overflow-y-auto z-50 w-64"
              :style="{
                top: `${mentionDropdown.top}px`,
                left: `${mentionDropdown.left}px`
              }"
            >
              <div class="py-1 h-full">
                <div
                  v-for="user in filteredUsers"
                  :key="user.id"
                  class="px-4 py-2 cursor-pointer flex items-center gap-2"
                  :class="{
                    'bg-blue-50': selectedMentionIndex === filteredUsers.indexOf(user),
                    'hover:bg-gray-100': selectedMentionIndex !== filteredUsers.indexOf(user)
                  }"
                  @click="insertMention(user)"
                >
                  <Avatar
                    :name="user.name"
                    size="sm"
                    class="flex-shrink-0"
                  />
                  <span class="truncate">{{ user.name }}</span>
                </div>
              </div>
            </div>

            <!-- Attached files -->
            <div v-if="attachedFiles.length" class="mt-4">
              <div class="text-sm font-medium text-gray-500 mb-2">
                Attached Files
              </div>
              <div class="space-y-2">
                <div 
                  v-for="file in attachedFiles"
                  :key="file.id"
                  class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                >
                  <i class="pi pi-file text-gray-400 text-xl"></i>
                  <div class="flex-1 min-w-0">
                    <div class="font-medium truncate">{{ file.name }}</div>
                    <div class="text-sm text-gray-500">
                      {{ Math.round(file.size / 1024) }} KB
                    </div>
                  </div>
                  <button 
                    class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                    @click="attachedFiles = attachedFiles.filter(f => f.id !== file.id)"
                    title="Remove file"
                  >
                    <i class="pi pi-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- File upload progress -->
            <div v-if="uploadingFiles.length" class="mt-4 space-y-2">
              <FileUploadProgress
                v-for="file in uploadingFiles"
                :key="file.id"
                :file-name="file.name"
                :size="file.size"
                :progress="file.progress"
              />
            </div>

            <!-- Action buttons -->
            <div class="mt-4">
              <div class="flex justify-between items-center py-2">
              <div class="flex items-center space-x-2">
                <BravoButton
                  text
                  severity="secondary"
                  icon="pi pi-paperclip"
                  @click="fileInput?.click()"
                  aria-label="Attach File"
                />
                
                <!-- Quick Reply with dropdown -->
                <div class="relative">
                  <BravoButton
                    text
                    severity="secondary"
                    icon="pi pi-comments"
                    @click="showTemplates = !showTemplates"
                    aria-label="Quick Reply"
                  />
                  
                  <!-- Quick Reply Dropdown -->
                  <div 
                    v-if="showTemplates"
                    class="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[250px]"
                  >
                    <div class="p-2">
                      <div class="text-xs font-medium text-gray-500 mb-2 px-2">Quick Reply Templates</div>
                      <div 
                        v-for="template in templates" 
                        :key="template.value"
                        class="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer"
                        @click="() => { 
                          const selectedTemplate = template;
                          newMessage = selectedTemplate.content;
                          showTemplates = false;
                        }"
                      >
                        {{ template.label }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <BravoButton
                  label="Send"
                  icon="pi pi-send"
                  :disabled="!newMessage.trim()"
                  @click="sendMessage"
                  aria-label="Send Message"
                />
              </div>
              </div>
            </div>
          </div>
        </div>
        
        <input
          ref="fileInput"
          type="file"
          class="hidden"
          @change="handleFileUpload"
        >
      </div>
    </template>
  </div>
</template>

<style scoped>
/* Main container similar to EmailChannel */
.chat-channel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100%;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  background-color: white;
}

/* Messages content wrapper - scrollable area with space for composer */
.messages-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  min-height: 0; /* Allow flex child to shrink */
  margin-bottom: 200px; /* Space for sticky composer */
}

/* Sticky composer container - positioned absolutely at bottom */
.sticky-composer-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  z-index: 10;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, .1);
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

:deep(.p-dropdown) {
  @apply border border-gray-200 rounded-lg shadow-lg;
  min-width: 200px;
}

:deep(.p-dropdown-item) {
  @apply px-4 py-2;
}

.mention {
  display: inline;
  background-color: rgba(29, 155, 209, 0.1);
  border-radius: 3px;
  padding: 1px 2px;
  color: #1D9BD1;
  font-weight: 500;
}

/* Custom scrollbar for messages container */
.messages-content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.messages-content-wrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.messages-content-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.messages-content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>