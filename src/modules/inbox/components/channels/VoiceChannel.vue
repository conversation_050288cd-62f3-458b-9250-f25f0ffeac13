<script setup lang="ts">
import type { Communication } from '../../types';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';

defineProps<{
  communication: Communication;
}>();
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- Empty state -->
    <BravoZeroStateScreen
      v-if="!communication.messages.length"
      title="Start New Call"
      message="Start a new call by entering a phone number and clicking the call button."
      :showButton="false"
      :imageSrc="EmailZeroStateSvg"
      image-alt="Voice Call"
      :action-handler="() => {}"
      class="flex items-center justify-center h-full"
    />

    <div class="flex-1 overflow-y-auto p-4">
      <!-- Call transcription will go here -->
    </div>
    
    <div class="border-t border-gray-200 p-4">
      <div class="flex space-x-2">
        <input 
          type="tel" 
          class="flex-1 border border-gray-300 rounded px-2 py-1 text-[#282A2C]"
          placeholder="Enter phone number"
        >
        <button class="bg-green-500 text-white px-4 py-1 rounded hover:bg-green-600">
          Call
        </button>
      </div>
    </div>
  </div>
</template>