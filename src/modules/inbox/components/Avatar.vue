<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps<{
  src?: string;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}>();

const hasError = ref(false);

const sizeClasses = {
  sm: 'w-8 h-8 text-xs',
  md: 'w-8 h-8 text-sm',
  lg: 'w-12 h-12 text-base'
};

const initials = computed(() => {
  if (!props.name) return 'G';
  return props.name.charAt(0).toUpperCase();
});

const colors = [
  '#0085FF',
  '#3EC865',
  '#F0A202',
  '#F47536',
  '#D72638',
  '#6D6CC6'
];

const getBackgroundColor = computed(() => {
  // Use the name to consistently pick a color
  const index = props.name.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);
  const color = colors[index % colors.length];
  return { backgroundColor: color };
});

const handleError = () => {
  hasError.value = true;
};
</script>

<template>
  <div 
    :class="[
      'rounded-full flex items-center justify-center overflow-hidden',
      sizeClasses[size || 'sm'],
      className
    ]"
  >
    <img
      v-if="src && !hasError"
      :src="src"
      :alt="name"
      class="w-full h-full object-cover"
      @error="handleError"
    >
    <div
      v-else
      :class="[
        'w-full h-full flex items-center justify-center text-white font-medium'
      ]"
      :style="getBackgroundColor"
    >
      {{ initials }}
    </div>
  </div>
</template> 