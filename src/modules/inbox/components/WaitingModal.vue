<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoBodyBold from '@services/ui-component-library/components/BravoTypography/BravoBodyBold.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import Dropdown from 'primevue/dropdown'
import Checkbox from 'primevue/checkbox'
import RadioButton from 'primevue/radiobutton'
import type { Issue } from '../../../services/IssuesAPI'
import { useCasesStore } from '../../../stores/cases'
import type { EventPreset, EventPresetGroup } from '../../../composables/services/useIssuesAPI'

interface WaitingReasonOption {
  label: string
  value: string | number
}

interface Props {
  visible: boolean
  issue: Issue | null
  waitingReasonOptions: WaitingReasonOption[]
  onSubmit?: (waitingData: any) => Promise<void>
  onCancel?: () => void
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const casesStore = useCasesStore()

// Form state
const waitingReason = ref<string | number | null>(null)
const returnTo = ref<string | null>(null)
const waitingNotes = ref('')
const autoReturn = ref(false)
const returnType = ref<string>('now') // Use the group value instead of preset/specific
const presetDuration = ref<string | null>(null)
const specificDate = ref('')
const specificTime = ref('')
const isLoading = ref(false)
const error = ref<string | null>(null)

// Dynamic options from API
const presetGroups = ref<EventPresetGroup[]>([])
const presetOptions = ref<EventPreset[]>([])
const isLoadingOptions = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// Return to options
const returnToOptions = [
  { label: 'Me', value: 'me' },
  { label: 'Team (Unassigned)', value: 'team' }
]

// Computed properties for date/time validation
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0] // Format: YYYY-MM-DD
})

const minTime = computed(() => {
  const today = new Date()
  const selectedDate = specificDate.value
  
  // If selected date is today, minimum time is current time
  if (selectedDate === minDate.value) {
    const hours = today.getHours().toString().padStart(2, '0')
    const minutes = today.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }
  
  // If selected date is in the future, no time restriction
  return '00:00'
})

// Validation for selected date/time
const isValidDateTime = computed(() => {
  if (returnType.value !== 'custom' || !autoReturn.value) return true
  if (!specificDate.value || !specificTime.value) return false
  
  const selectedDateTime = new Date(`${specificDate.value}T${specificTime.value}`)
  const now = new Date()
  
  return selectedDateTime > now
})

// Computed options for the current return type
const currentPresetOptions = computed(() => {
  return presetOptions.value.filter(preset => {
    // return preset.reference === returnType.value
    return true
  })
})

// Load duration options from API
const loadDurationOptions = async () => {
  isLoadingOptions.value = true
  try {
    const { groups, presets } = await casesStore.getDurationOptions()
    presetGroups.value = groups
    presetOptions.value = presets
    
    // Set default return type to first group if available
    if (groups.length > 0) {
      returnType.value = groups[0].val
    }
  } catch (err) {
    console.error('Failed to load duration options:', err)
    error.value = 'Failed to load duration options'
  } finally {
    isLoadingOptions.value = false
  }
}

const handleConfirm = async () => {
  if (!waitingReason.value || !props.onSubmit || !props.issue?.id) return
  
  isLoading.value = true
  error.value = null

  try {
    const waitingData = {
      waiting_reason: waitingReason.value,
      waiting_return_to: returnTo.value,
      waiting_notes: waitingNotes.value.trim(),
      wait_until_preset: autoReturn.value && returnType.value !== 'custom' ? presetDuration.value : null,
      snooze_until_preset_group: autoReturn.value && returnType.value !== 'custom' ? returnType.value : null,
      wait_until: autoReturn.value && returnType.value === 'custom' && specificDate.value && specificTime.value 
        ? `${specificDate.value}T${specificTime.value}:00` 
        : null,
      id: props.issue.id
    }

    await props.onSubmit(waitingData)
    
    // Close modal and reset form on success
    emit('update:visible', false)
    resetForm()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to set case to waiting'
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  resetForm()
  if (props.onCancel) {
    props.onCancel()
  } else {
    emit('update:visible', false)
  }
}

const handleDialogHide = () => {
  if (isLoading.value) return // Prevent closing when loading
  resetForm()
  emit('update:visible', false)
}

const resetForm = () => {
  waitingReason.value = null
  returnTo.value = null
  waitingNotes.value = ''
  autoReturn.value = false
  returnType.value = presetGroups.value.length > 0 ? presetGroups.value[0].val : 'now'
  presetDuration.value = null
  specificDate.value = ''
  specificTime.value = ''
  error.value = null
  isLoading.value = false
}

// Watch for visible prop changes to reset form when modal opens
watch(() => props.visible, (newVisible, oldVisible) => {
  if (newVisible && !oldVisible) {
    // Modal is opening, reset form
    resetForm()
    // Load duration options when modal opens
    loadDurationOptions()
  }
})

// Load options on component mount
onMounted(() => {
  loadDurationOptions()
})
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    header="Set to Waiting"
    :style="{ width: '500px' }"
    :closable="true"
    :close-on-escape="!isLoading"
    @hide="handleDialogHide"
    :class="['waiting-modal', { loading: isLoading }]"
  >
    <div class="waiting-form">
      <div class="field-group">
        <label for="waiting-reason" class="field-label">
          <BravoBodyBold>Waiting Reason *</BravoBodyBold>
        </label>
        <Dropdown
          id="waiting-reason"
          v-model="waitingReason"
          :options="waitingReasonOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a reason..."
          :disabled="isLoading"
          class="w-full"
        />
      </div>

      <div class="field-group">
        <label for="return-to" class="field-label">
          <BravoBodyBold>Return To</BravoBodyBold>
        </label>
        <Dropdown
          id="return-to"
          v-model="returnTo"
          :options="returnToOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Select who to return to..."
          :disabled="isLoading"
          class="w-full"
        />
      </div>

      <div class="field-group">
        <label for="waiting-notes" class="field-label">
          <BravoBodyBold>Notes</BravoBodyBold>
        </label>
        <BravoTextarea 
          id="waiting-notes"
          v-model="waitingNotes" 
          rows="3" 
          placeholder="Additional notes about why this case is waiting..."
          class="w-full"
          autoResize
          :disabled="isLoading"
        />
      </div>

      <div class="field-group">
        <div class="checkbox-group">
          <Checkbox
            v-model="autoReturn"
            inputId="auto-return"
            binary
            :disabled="isLoading"
          />
          <label for="auto-return" class="checkbox-label">
            <BravoBody>Return at set time if no activity</BravoBody>
          </label>
        </div>
      </div>

      <div v-if="autoReturn" class="auto-return-fields">
        <div class="radio-options">
          <div 
            v-for="group in presetGroups" 
            :key="group.id" 
            class="radio-group"
          >
            <RadioButton
              v-model="returnType"
              :inputId="`return-type-${group.val}`"
              :value="group.val"
              :disabled="isLoading || isLoadingOptions"
            />
            <label :for="`return-type-${group.val}`" class="radio-label">
              <BravoBody>{{ group.lbl }}</BravoBody>
            </label>
          </div>
        </div>

        <div v-if="returnType !== 'custom'" class="field-group">
          <Dropdown
            v-model="presetDuration"
            :options="currentPresetOptions"
            optionLabel="lbl"
            optionValue="val"
            placeholder="Select duration..."
            :disabled="isLoading || isLoadingOptions"
            :loading="isLoadingOptions"
            class="w-full"
          />
        </div>

        <div v-if="returnType === 'custom'" class="specific-time-fields">
          <div class="field-group">
            <label for="specific-date" class="field-label">
              <BravoBody>Date</BravoBody>
            </label>
            <input
              id="specific-date"
              v-model="specificDate"
              type="date"
              :min="minDate"
              class="date-input"
              :class="{ 'invalid': specificDate && specificTime && !isValidDateTime }"
              :disabled="isLoading"
            />
          </div>
          <div class="field-group">
            <label for="specific-time-input" class="field-label">
              <BravoBody>Time</BravoBody>
            </label>
            <input
              id="specific-time-input"
              v-model="specificTime"
              type="time"
              :min="minTime"
              class="time-input"
              :class="{ 'invalid': specificDate && specificTime && !isValidDateTime }"
              :disabled="isLoading"
            />
          </div>
          <div v-if="specificDate && specificTime && !isValidDateTime" class="validation-error">
            <BravoBody style="color: var(--red-600); font-size: 0.875rem;">
              Please select a future date and time
            </BravoBody>
          </div>
        </div>
      </div>
      
      <div v-if="error" class="error-message">
        {{ error }}
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          :label="isLoading ? 'Setting to Waiting...' : 'Set to Waiting'"
          severity="primary"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!waitingReason || isLoading || !isValidDateTime"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
/* Fix modal border radius and prevent resizing */
:deep(.waiting-modal .p-dialog) {
  border-radius: 8px !important;
}

:deep(.waiting-modal .p-dialog-content) {
  border-radius: 0 0 8px 8px !important;
}

:deep(.waiting-modal .p-dialog-header) {
  border-radius: 8px 8px 0 0 !important;
}

/* Disable close button when loading */
:deep(.waiting-modal.loading .p-dialog-header-close) {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.waiting-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  margin-bottom: 0.25rem;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-label {
  cursor: pointer;
  user-select: none;
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.radio-options {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.radio-label {
  cursor: pointer;
}

.auto-return-fields {
  border-left: 3px solid var(--primary-color);
  padding-left: 1rem;
  margin-left: 0.5rem;
  background: var(--surface-50);
  border-radius: 0 4px 4px 0;
  padding: 1rem;
  margin-top: 1rem;
}

.specific-time-fields {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-input,
.time-input {
  padding: 0.5rem;
  border: 1px solid var(--surface-300);
  border-radius: 4px;
  font-size: 0.875rem;
}

.date-input:focus,
.time-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.date-input.invalid,
.time-input.invalid {
  border-color: var(--red-500);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.validation-error {
  margin-top: 0.5rem;
}

.error-message {
  color: var(--red-600);
  font-size: 0.875rem;
  padding: 0.75rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 6px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Fix button width consistency */
.modal-actions :deep(.p-button) {
  min-width: 120px;
  height: 40px;
}

.modal-actions :deep(.p-button-label) {
  white-space: nowrap;
}

/* Ensure checkbox labels are clickable */
:deep(.waiting-modal .p-checkbox-label) {
  cursor: pointer;
  user-select: none;
}

:deep(.waiting-modal .p-checkbox-box) {
  cursor: pointer;
}
</style> 