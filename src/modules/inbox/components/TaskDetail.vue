<script setup lang="ts">
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import BravoSelectField from '@services/ui-component-library/components/BravoSelectField.vue'
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { fetchTaskById, updateTaskStatus } from '@/composables/services/useTasksApi'
import type { Task } from '@/types/task'
import { usePartnerStore } from '@/stores/partner'
import { useMetaStore } from '@/stores/meta'
import { useUsersStore } from '@/stores/users'
import { getTaskTeamName, getTaskUserName, getTaskAssignments, getTaskCaseReference, getTaskCaseRoute, getTaskStatusSeverity } from '../utils/taskHelpers'

const route = useRoute()
const router = useRouter()
const partnerStore = usePartnerStore()
const metaStore = useMetaStore()
const usersStore = useUsersStore()

// Props - allow passing in a task directly or use route params
const props = defineProps<{
  task?: Task
}>()

const localTask = ref<Task | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)

// Edit mode state
const isEditMode = ref(false)
const isSaving = ref(false)
const saveError = ref<string | null>(null)

// Form data for editable fields
const editForm = ref({
  name: '',
  description: '',
  dueDate: '',
  teamId: '',
  userId: ''
})

// Computed task that uses either the prop or local task
const task = computed(() => props.task || localTask.value)

// Get team and user names using shared helpers
const teamName = computed(() => {
    if (!task.value) return '—'
    return getTaskTeamName(task.value, partnerStore.partnerTeams)
})

const userName = computed(() => {
    if (!task.value) return '—'
    return getTaskUserName(task.value, metaStore.partnerMetaData)
})

// Team options for dropdown
const teamOptions = computed(() => {
  return partnerStore.partnerTeams.map(team => ({
    label: team.lbl || team.val,
    value: team.val
  }))
})

// User options filtered by selected team (or current task's team)
const userOptions = computed(() => {
  return usersStore.getUsersForSelect
})

// Get current team and user IDs from task links
const currentTeamId = computed(() => {
  if (!task.value?.links) return null
  const teamLink = task.value.links.find(link => link.rel === 'team')
  return teamLink?.id || null
})

const currentUserId = computed(() => {
  if (!task.value?.links) return null
  const userLink = task.value.links.find(link => link.rel === 'user')
  return userLink?.id || null
})

const loadTask = async (id: string) => {
    loading.value = true
    error.value = null
    try {
        localTask.value = await fetchTaskById(id)
        // Load teams if not already loaded
        if (partnerStore.partnerTeams.length === 0) {
            await partnerStore.fetchPartnerTeams(false)
        }
        // Ensure we have partner metadata loaded
        if (!metaStore.partnerMetaData) {
            await metaStore.loadPartnerMetaData()
        }
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to fetch task'
    } finally {
        loading.value = false
    }
}

// Only watch route params if no task prop is provided
watch(
    () => route.params.id,
    (id) => {
        if (!props.task && id) {
            loadTask(id as string)
        }
    },
    { immediate: true }
)

// Load additional data when task prop is provided
watch(
    () => props.task,
    async (newTask) => {
        if (newTask) {
            // Load teams if not already loaded
            if (partnerStore.partnerTeams.length === 0) {
                await partnerStore.fetchPartnerTeams(false)
            }
            // Ensure we have partner metadata loaded
            if (!metaStore.partnerMetaData) {
                await metaStore.loadPartnerMetaData()
            }
        }
    },
    { immediate: true }
)

const isCompleted = computed(() => task.value?.status === 'Completed')

const taskStatusState = computed(() => {
    return task.value ? getTaskStatusSeverity(task.value) : undefined
})

const toggleComplete = async () => {
    if (!task.value) return
    const newStatus = isCompleted.value ? 'Not Started' : 'Completed'
    try {
        const updatedTask = await updateTaskStatus(task.value, newStatus)
        // Update the appropriate task reference
        if (props.task) {
            // If using prop, emit an event to update the parent
            emit('task-updated', updatedTask)
        } else {
            // If using local task, update it directly
            localTask.value = updatedTask
        }
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to update task status'
    }
}

const handleCaseClick = () => {
    if (!task.value) return
    const caseRoute = getTaskCaseRoute(task.value)
    if (caseRoute) {
        router.push(caseRoute)
    }
}

const emit = defineEmits(['back', 'task-updated'])

// Function to create a comprehensive task update
async function updateTask(taskToUpdate: Task, updates: Partial<Task>): Promise<Task> {
  const { id, name, description, dueDate, deleted, links, status } = taskToUpdate
  
  // Prepare the updated task data
  const updatedTask = {
    id,
    name: updates.name || name,
    description: updates.description || description,
    dueDate: updates.dueDate && updates.dueDate.trim() !== '' ? updates.dueDate : (dueDate || null),
    deleted,
    links: updates.links || links,
    status: updates.status || status,
  }
  
  const response = await fetch(`/service-tasks-stage/tasks/${encodeURIComponent(taskToUpdate.id)}`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:tasks',
    },
    method: 'PUT',
    credentials: 'omit',
    body: JSON.stringify(updatedTask),
  })
  
  if (!response.ok) throw new Error('Failed to update task')
  
  const responseJson = await response.json()
  return responseJson.createdTask || responseJson.task || responseJson
}

// Function to enter edit mode
function enterEditMode() {
  if (!task.value) return
  
  // Initialize form with current task data
  editForm.value = {
    name: task.value.name || '',
    description: task.value.description || '',
    dueDate: task.value.dueDate || '',
    teamId: currentTeamId.value || '',
    userId: currentUserId.value || ''
  }
  
  isEditMode.value = true
  saveError.value = null
}

// Function to cancel edit mode
function cancelEdit() {
  isEditMode.value = false
  saveError.value = null
  // Reset form data
  editForm.value = {
    name: '',
    description: '',
    dueDate: '',
    teamId: '',
    userId: ''
  }
}

// Function to save changes
async function saveChanges() {
  if (!task.value) return
  
  isSaving.value = true
  saveError.value = null
  
  try {
    // Build updated links array
    const updatedLinks = [...task.value.links]
    
    // Update team link
    const teamLinkIndex = updatedLinks.findIndex(link => link.rel === 'team')
    if (editForm.value.teamId) {
      if (teamLinkIndex >= 0) {
        updatedLinks[teamLinkIndex] = { rel: 'team', id: editForm.value.teamId, href: '' }
      } else {
        updatedLinks.push({ rel: 'team', id: editForm.value.teamId, href: '' })
      }
    } else if (teamLinkIndex >= 0) {
      updatedLinks.splice(teamLinkIndex, 1)
    }
    
    // Update user link
    const userLinkIndex = updatedLinks.findIndex(link => link.rel === 'user')
    if (editForm.value.userId) {
      if (userLinkIndex >= 0) {
        updatedLinks[userLinkIndex] = { rel: 'user', id: editForm.value.userId, href: '' }
      } else {
        updatedLinks.push({ rel: 'user', id: editForm.value.userId, href: '' })
      }
    } else if (userLinkIndex >= 0) {
      updatedLinks.splice(userLinkIndex, 1)
    }
    
    // Prepare updates
    const updates: Partial<Task> = {
      name: editForm.value.name.trim(),
      description: editForm.value.description.trim(),
      dueDate: editForm.value.dueDate || '',
      links: updatedLinks
    }
    
    // Update the task
    const updatedTask = await updateTask(task.value, updates)
    
    // Update the appropriate task reference
    if (props.task) {
      // If using prop, emit an event to update the parent
      emit('task-updated', updatedTask)
    } else {
      // If using local task, update it directly
      localTask.value = updatedTask
    }
    
    // Exit edit mode
    isEditMode.value = false
    
  } catch (err) {
    saveError.value = err instanceof Error ? err.message : 'Failed to save changes'
  } finally {
    isSaving.value = false
  }
}

// Watch for team changes to fetch users for that team
watch(() => editForm.value.teamId, async (newTeam, oldTeam) => {
  if (newTeam !== oldTeam && newTeam) {
    try {
      // Extract organization ID from team value (first 3 characters)
      const organizationId = newTeam.substring(0, 3)
      
      // Fetch users filtered by the selected team
      await usersStore.fetchUsersByTeam(newTeam, organizationId)
    } catch (error) {
      console.error('Error fetching team users:', error)
    }
  } else if (!newTeam) {
    // Clear users when no team is selected
    usersStore.clearUsers()
  }
})

// Load teams and current team's users when component mounts or task changes
watch(task, async (newTask) => {
  if (newTask) {
    // Load teams if not already loaded
    if (partnerStore.partnerTeams.length === 0) {
      await partnerStore.fetchPartnerTeams()
    }
    
    // Load users for current team if there is one
    const teamId = currentTeamId.value
    if (teamId) {
      try {
        const organizationId = teamId.substring(0, 3)
        await usersStore.fetchUsersByTeam(teamId, organizationId)
      } catch (error) {
        console.error('Error fetching team users:', error)
      }
    }
  }
}, { immediate: true })
</script>

<template>
    <div class="view-card">
        <div class="view-content">
            <div class="view-header">
                <h2>Task Details</h2>
                <div class="view-actions">
                    <BravoButton 
                        v-if="!isEditMode && task"
                        icon="pi pi-pencil" 
                        size="small" 
                        @click="enterEditMode"
                        aria-label="Edit Task"
                        tooltip="Edit Task"
                        class="mr-2"
                    />
                    <BravoButton 
                        icon="pi pi-arrow-left" 
                        size="small" 
                        @click="emit('back')"
                        aria-label="Back"
                        tooltip="Back"
                    />
                </div>
            </div>
            <div v-if="loading" class="empty-state">
                <p>Loading...</p>
            </div>
            <div v-else-if="error" class="empty-state">
                <p>{{ error }}</p>
            </div>
            <div v-else-if="!task" class="empty-state">
                <p>No task selected</p>
            </div>
            
            <!-- View Mode -->
            <div v-else-if="!isEditMode" class="task-detail-card">
                <BravoTitle1 class="task-title-header">{{ task.name }}</BravoTitle1>
                <BravoTag 
                    :value="task.status" 
                    :severity="taskStatusState"
                    class="task-status-tag"
                />
                
                <div class="task-field">
                    <strong>Title:</strong> {{ task.name }}
                </div>
                <div class="task-field">
                    <strong>Description:</strong> {{ task.description || '—' }}
                </div>
                <div class="task-field">
                    <strong>Due Date: </strong> 
                    <BravoTimestamp 
                        v-if="task.dueDate" 
                        :datetime="task.dueDate" 
                    />
                    <span v-else>—</span>
                </div>
                <div class="task-field">
                    <strong>Created Date: </strong> 
                    <BravoTimestamp 
                        v-if="task.createdAt" 
                        :datetime="task.createdAt" 
                    />
                    <span v-else>—</span>
                </div>
                <div class="task-field">
                    <strong>Status:</strong> {{ task.status }}
                </div>
                <div class="task-field">
                    <strong>Organization:</strong> {{ task.orgId }}
                </div>
                <div class="task-field">
                    <strong>Owner Team:</strong> {{ teamName }}
                </div>
                <div class="task-field">
                    <strong>Owner User:</strong> {{ userName }}
                </div>
                <div class="task-field">
                    <strong>Case Reference ID: </strong> 
                    <span 
                        v-if="getTaskCaseRoute(task)" 
                        class="case-reference-link"
                        @click="handleCaseClick"
                    >
                        {{ getTaskCaseReference(task) }}
                    </span>
                    <span v-else>{{ getTaskCaseReference(task) }}</span>
                </div>
                <BravoButton
                    :label="isCompleted ? 'Reopen' : 'Mark as Complete'"
                    :icon="isCompleted ? 'pi pi-undo' : 'pi pi-check'"
                    :severity="isCompleted ? 'secondary' : 'primary'"
                    size="small"
                    class="mt-2"
                    @click="toggleComplete"
                />
            </div>
            
            <!-- Edit Mode -->
            <div v-else class="task-detail-card">
                <div class="edit-form">
                    <div class="form-header">
                        <BravoTitle1 class="task-title-header">Edit Task</BravoTitle1>
                        <BravoTag 
                            :value="task.status" 
                            :severity="taskStatusState"
                            class="task-status-tag"
                        />
                    </div>
                    
                    <!-- Title Field -->
                    <div class="field">
                        <BravoLabel text="Title" isRequired />
                        <BravoInputText
                            v-model="editForm.name"
                            placeholder="Enter task title..."
                            :disabled="isSaving"
                        />
                    </div>

                    <!-- Description Field -->
                    <div class="field">
                        <BravoLabel text="Description" />
                        <BravoTextarea
                            v-model="editForm.description"
                            rows="4"
                            placeholder="Enter task description..."
                            :disabled="isSaving"
                            autoResize
                        />
                    </div>

                    <!-- Due Date Field -->
                    <div class="field">
                        <BravoLabel text="Due Date" />
                        <input
                            v-model="editForm.dueDate"
                            type="datetime-local"
                            class="date-time-input"
                            :disabled="isSaving"
                        />
                    </div>

                    <!-- Owner Team Field -->
                    <div class="field">
                        <BravoLabel text="Owner Team" />
                        <BravoSelectField
                            id="owner-team"
                            v-model="editForm.teamId"
                            :options="teamOptions"
                            placeholder="Select a team..."
                            dataTestId="owner-team"
                            :disabled="isSaving"
                        />
                    </div>

                    <!-- Owner User Field -->
                    <div class="field">
                        <BravoLabel text="Owner User" />
                        <BravoSelectField
                            id="owner-user"
                            v-model="editForm.userId"
                            :options="userOptions"
                            placeholder="Select a user..."
                            dataTestId="owner-user"
                            :disabled="isSaving || !editForm.teamId"
                        />
                    </div>
                    
                    <!-- Read-only fields for reference -->
                    <div class="readonly-section">
                        <h4>Read-Only Information</h4>
                        <div class="task-field">
                            <strong>Created Date: </strong> 
                            <BravoTimestamp 
                                v-if="task.createdAt" 
                                :datetime="task.createdAt" 
                            />
                            <span v-else>—</span>
                        </div>
                        <div class="task-field">
                            <strong>Status:</strong> {{ task.status }}
                        </div>
                        <div class="task-field">
                            <strong>Organization:</strong> {{ task.orgId }}
                        </div>
                        <div class="task-field">
                            <strong>Case Reference ID: </strong> 
                            <span 
                                v-if="getTaskCaseRoute(task)" 
                                class="case-reference-link"
                                @click="handleCaseClick"
                            >
                                {{ getTaskCaseReference(task) }}
                            </span>
                            <span v-else>{{ getTaskCaseReference(task) }}</span>
                        </div>
                    </div>
                    
                    <!-- Error Message -->
                    <div v-if="saveError" class="error-message">
                        {{ saveError }}
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="edit-actions">
                        <BravoButton
                            label="Cancel"
                            severity="secondary"
                            @click="cancelEdit"
                            :disabled="isSaving"
                        />
                        <BravoButton
                            :label="isSaving ? 'Saving...' : 'Save Changes'"
                            severity="primary"
                            @click="saveChanges"
                            :loading="isSaving"
                            :disabled="isSaving || !editForm.name.trim()"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.view-card {
  background: white;
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 0;
  padding-top: 0;
}

.view-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  padding-top: 0;
  overflow: hidden;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  height: 64px;
  border-bottom: 1px solid var(--border-color);
  background: #fff;
  flex-shrink: 0;
}

.view-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--surface-900);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--surface-600);
  font-style: italic;
  padding: 2rem;
}

.task-detail-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--surface-200);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin: 2rem;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 128px);
}

.task-field {
  margin-bottom: 1rem;
  font-size: 1rem;
}

.task-title-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-200);
}

.task-status-tag {
  margin-bottom: 1.5rem;
}

.case-reference-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.case-reference-link:hover {
  color: var(--primary-700);
  text-decoration: none;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-200);
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-time-input {
  padding: 0.5rem;
  border: 1px solid var(--surface-300);
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: inherit;
}

.date-time-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.date-time-input:disabled {
  background-color: var(--surface-100);
  color: var(--text-color-secondary);
  cursor: not-allowed;
}

.readonly-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-200);
}

.readonly-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.error-message {
  color: var(--red-600);
  font-size: 0.875rem;
  padding: 0.75rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 6px;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-200);
}

.mr-2 {
  margin-right: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}
</style> 