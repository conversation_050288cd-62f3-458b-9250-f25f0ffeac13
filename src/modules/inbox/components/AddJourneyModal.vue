<template>
  <BravoDialog
    v-model:visible="isVisible"
    modal
    header="Add Journey to Case"
    :style="{ width: '500px' }"
    :closable="true"
    :draggable="false"
    :closable-on-escape="!isSubmitting"
    :close-on-click="!isSubmitting"
    @hide="handleClose"
  >
    <div class="add-journey-content">
      <div v-if="isLoading" class="loading-state">
        <div class="loading-state-content flex space-around">
          <BravoProgressSpinner style="width: 1.5rem; height: 1.5rem;" />
          <p class="ml-2">Loading journeys...</p>
        </div>
      </div>

      <div v-else-if="journeysStore.error" class="error-state">
        <BravoMessage severity="error" :closable="false">
          Failed to load journeys: {{ journeysStore.error }}
        </BravoMessage>
        <BravoButton 
          label="Retry" 
          @click="loadJourneys"
          class="mt-3"
        />
      </div>

      <div v-else-if="journeysStore.journeys.length === 0" class="empty-state">
        <BravoMessage severity="info" :closable="false">
          No journeys available. Please create a journey first.
        </BravoMessage>
      </div>

      <div v-else class="journeys-form">
        <p class="description">
          Select a journey to add to this case. The journey will guide the case through predefined stages and workflows.
        </p>

        <div class="form-field">
          <label for="journey-select" class="field-label">Choose Journey</label>
          <Dropdown
            id="journey-select"
            v-model="selectedJourney"
            :options="journeysStore.journeys"
            option-label="title"
            option-value="id"
            placeholder="Select a journey..."
            filter
            :filter-placeholder="'Search journeys...'"
            class="journey-dropdown"
            :class="{ 'p-invalid': !selectedJourney && showValidation }"
          >
            <template #option="{ option }">
              <div class="journey-option">
                <div class="journey-option-header">
                  <span class="journey-option-title">{{ option.title }}</span>
                  <BravoBadge 
                    v-if="option.stages.length > 0" 
                    :value="option.stages.length + ' stages'"
                    severity="secondary"
                    size="small"
                  />
                </div>
                <div class="journey-option-description">
                  {{ truncateText(option.description || 'No description available', 72) }}
                </div>
                <div v-if="option.stages.length > 0" class="journey-option-stages">
                  <span class="stages-label">Stages:</span>
                  <span class="stages-list">
                    {{ option.stages.map((stage: JourneyStage) => stage.title).join(', ') }}
                  </span>
                </div>
              </div>
            </template>
            <template #value="{ value }">
              <div v-if="value" class="selected-journey">
                <span class="selected-journey-title">{{ getSelectedJourneyTitle(value) }}</span>
                <BravoBadge 
                  v-if="getSelectedJourneyStagesCount(value) > 0" 
                  :value="getSelectedJourneyStagesCount(value) + ' stages'"
                  severity="secondary"
                  size="small"
                  class="ml-2"
                />
              </div>
            </template>
          </Dropdown>
          <small v-if="!selectedJourney && showValidation" class="validation-message">
            Please select a journey to continue.
          </small>
        </div>

        <!-- Selected Journey Details -->
        <div v-if="selectedJourney" class="selected-journey-details">
          <h4 class="details-title">Journey Details</h4>
          <div class="journey-info">
            <div class="info-item">
              <strong>Title:</strong> {{ getSelectedJourneyDetails()?.title }}
            </div>
            <div class="info-item">
              <strong>Description:</strong> {{ getSelectedJourneyDetails()?.description || 'No description available' }}
            </div>
            <div v-if="getSelectedJourneyDetails()?.stages && getSelectedJourneyDetails()!.stages.length > 0" class="info-item">
              <strong>Stages ({{ getSelectedJourneyDetails()!.stages.length }}):</strong>
              <div class="stages-list-detailed">
                <BravoBadge 
                  v-for="stage in getSelectedJourneyDetails()!.stages"
                  :key="stage.id"
                  :value="stage.title"
                  severity="info"
                  size="small"
                  class="stage-badge"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton 
          label="Cancel" 
          severity="secondary" 
          @click="handleClose"
          :disabled="isSubmitting"
        />
        <BravoButton 
          label="Add Journey" 
          :disabled="!selectedJourney || isLoading || isSubmitting"
          :loading="isSubmitting"
          @click="handleAddJourney"
        />
      </div>
    </template>
  </BravoDialog>

  <BravoToast position="top-right" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoMessage from '@services/ui-component-library/components/BravoMessage.vue'
import Dropdown from 'primevue/dropdown'
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import { useJourneysStore } from '@/stores/journeys'
import type { Journey, JourneyStage } from '@/composables/services/useAdminJourneysAPI'
import BravoToast from '@services/ui-component-library/components/BravoToast.vue'
import { useToast } from 'primevue/usetoast'

const props = defineProps<{
  visible: boolean
  caseId?: string
  onAdd: (journey: Journey) => Promise<void>
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const journeysStore = useJourneysStore()
const selectedJourney = ref<string>('')
const isVisible = ref(props.visible)
const showValidation = ref(false)
const isSubmitting = ref(false)
const isLoading = ref(false)
const toast = useToast()

// Watch for visibility changes
watch(() => props.visible, async (newValue) => {
  isVisible.value = newValue
  if (newValue) {
    isLoading.value = true
    try {
      await loadJourneys()
    } finally {
      isLoading.value = false
    }
    selectedJourney.value = ''
    showValidation.value = false
  }
})

watch(isVisible, (newValue) => {
  emit('update:visible', newValue)
})

async function loadJourneys() {
  try {
    await journeysStore.fetchJourneys()
  } catch (error) {
    console.error('Failed to load journeys:', error)
  }
}

function handleClose() {
  isVisible.value = false
  selectedJourney.value = ''
  showValidation.value = false
}

async function handleAddJourney() {
  if (!selectedJourney.value) {
    showValidation.value = true
    return
  }

  isSubmitting.value = true

  try {
    const selectedJourneyObj = journeysStore.journeys.find(j => j.id === selectedJourney.value)
    if (selectedJourneyObj) {
      await props.onAdd(selectedJourneyObj)
      // delay to allow journey to create 
      // setTimeout(() => {
      //   toast.add({
      //     severity: 'success',
      //     summary: 'Journey Added',
      //     detail: `Successfully added journey "${selectedJourneyObj.title}"`,
      //     life: 3000
      //   })
      // }, 400)

      handleClose()
    }
  } catch (error: any) {
    console.error('Error adding journey:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error?.message || 'Failed to add journey. Please try again.',
      life: 5000
    })
  } finally {
    isSubmitting.value = false
  }
}

function getSelectedJourneyTitle(journeyId: string): string {
  const journey = journeysStore.journeys.find(j => j.id === journeyId)
  return journey?.title || ''
}

function getSelectedJourneyStagesCount(journeyId: string): number {
  const journey = journeysStore.journeys.find(j => j.id === journeyId)
  return journey?.stages.length || 0
}

function getSelectedJourneyDetails(): Journey | undefined {
  return journeysStore.journeys.find(j => j.id === selectedJourney.value)
}

function truncateText(text: string, maxLength: number): string {
  if (text.length > maxLength) {
    return text.slice(0, maxLength) + '...'
  }
  return text
}

onMounted(() => {
  if (props.visible) {
    loadJourneys()
  }
})
</script>

<style scoped>
.add-journey-content {
  min-height: 200px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.journeys-form {
  padding: 0.5rem 0;
}

.description {
  margin-bottom: 1.5rem;
  color: var(--surface-600);
  line-height: 1.5;
}

.form-field {
  margin-bottom: 1.5rem;
}

.field-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--surface-900);
}

.journey-dropdown {
  width: 100%;
  min-height: 2.5rem;
}

.journey-dropdown :deep(.p-dropdown) {
  width: 100%;
  min-height: 2.5rem;
}

.journey-dropdown :deep(.p-dropdown-label) {
  padding: 0.75rem;
  font-size: 1rem;
}

.journey-dropdown :deep(.p-dropdown-trigger) {
  width: 2.5rem;
}

.validation-message {
  color: var(--red-500);
  margin-top: 0.25rem;
  display: block;
}

/* Dropdown option styling */
.journey-option {
  padding: 0.5rem 0;
  max-height: 4rem;
  overflow: hidden;
}

.journey-option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.journey-option-title {
  font-weight: 600;
  color: var(--surface-900);
}

.journey-option-description {
  color: var(--surface-600);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.journey-option-stages {
  font-size: 0.8rem;
  color: var(--surface-500);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stages-label {
  font-weight: 500;
  margin-right: 0.5rem;
}

.stages-list {
  font-style: italic;
}

/* Selected value styling */
.selected-journey {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.selected-journey-title {
  font-weight: 500;
}

/* Selected journey details */
.selected-journey-details {
  background: var(--surface-50);
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.details-title {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--surface-900);
}

.journey-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  font-size: 0.9rem;
  line-height: 1.4;
}

.info-item strong {
  color: var(--surface-900);
}

.stages-list-detailed {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.stage-badge {
  font-size: 0.75rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style> 