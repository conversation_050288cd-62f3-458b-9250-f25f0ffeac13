<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import { useSummarizerStore } from '@/stores/summarizer'
import { useCasesStore } from '@/stores/cases'
import { useRouter } from 'vue-router'
import { useMemberStore } from '@/stores/member'
import { usePartnerStore } from '@/stores/partner'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTitle3 from '@services/ui-component-library/components/BravoTypography/BravoTitle3.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import BravoSubhead from '@services/ui-component-library/components/BravoTypography/BravoSubhead.vue'
import BravoCaption2 from '@services/ui-component-library/components/BravoTypography/BravoCaption2.vue'
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue'
import { getStatusInfo, getCaseIcon, getCaseIconLabel, getCaseTeamName } from '../utils/caseHelper'

const props = defineProps<{ issue: any }>()

const expandedIndex = ref<number>(0) // 0 = Customer section open by default

const accordions = [
  { key: 'customer', label: 'Customer' },
  { key: 'location', label: 'Location' },
  { key: 'products', label: 'Products' },
  { key: 'caseHistory', label: 'Case History' }
]

const summarizerStore = useSummarizerStore()
const casesStore = useCasesStore()
const router = useRouter()
const memberStore = useMemberStore()
const partnerStore = usePartnerStore()



const member = computed(() => props.issue?.member || {})
const memberUser = computed(() => props.issue?.memberUser || {})

const customerName = computed(() =>
  memberUser.value.full_name ||
  member.value.name_legal ||
  member.value.name ||
  '—'
)
const customerEmail = computed(() =>
  memberUser.value.email ||
  member.value.email ||
  '—'
)
const customerPhone = computed(() =>
  memberUser.value.sms_number ||
  member.value.phone ||
  member.value.c__phone ||
  '—'
)
const customerSince = computed(() =>
  member.value.created
    ? new Date(member.value.created).toLocaleDateString(undefined, { year: 'numeric', month: 'short' })
    : '—'
)

const location = computed(() => props.issue?.location || {})

const address = computed(() =>
  location.value.street_1 || location.value.globalValues?.street_1 || '—'
)
const city = computed(() =>
  location.value.city || location.value.globalValues?.city || '—'
)
const country = computed(() =>
  location.value.country || location.value.globalValues?.country || '—'
)
const timezone = computed(() =>
  location.value.time_zone || '—'
)

const locationPhone = computed(() => props.issue?.c__location_phone || '—')

const memberId = computed(() => props.issue?.member?.id)
const locationId = computed(() => props.issue?.location?.id)
const orgId = computed(() => props.issue?.member?.context_org_id)

const creatingSummary = computed(() => summarizerStore.createLoading)
const createSummaryError = computed(() => summarizerStore.createError)

const sentimentSeverity = computed(() => {
  const sentiment = summarizerStore.customerSummary?.sentiment?.toLowerCase()
  switch (sentiment) {
    case 'positive':
      return 'success'
    case 'neutral':
      return 'warn'
    case 'negative':
      return 'danger'
    case 'unknown':
      return 'info'
    default:
      return 'info'
  }
})

function getCustomerId() {
  return props.issue?.member.id
}

async function loadMemberCases() {
  if (memberId.value && locationId.value && orgId.value) {
    await casesStore.fetchMemberCases(memberId.value, locationId.value, orgId.value)
  }
}

async function loadMemberProducts() {
  if (memberId.value && locationId.value && orgId.value) {
    await memberStore.fetchMemberProducts(memberId.value, locationId.value, orgId.value)
  }
}

watch(memberId, () => {
  loadMemberCases()
  loadMemberProducts()
})

onMounted(async () => {
  const customerId = getCustomerId()
  if (customerId) {
    summarizerStore.loadCustomerSummary(customerId)
  }
  loadMemberCases()
  loadMemberProducts()
  
  // Load partner teams if not already loaded
  if (partnerStore.partnerTeams.length === 0) {
    await partnerStore.fetchPartnerTeams(false)
  }
  
  await nextTick()
  // Programmatically click the first accordion header if not open
  const firstHeader = document.querySelector('.p-accordionheader')
  if (firstHeader) {
    (firstHeader as HTMLElement).click()
  }
})
watch(() => getCustomerId(), (customerId) => {
  if (customerId) {
    summarizerStore.loadCustomerSummary(customerId)
  }
})

function navigateToCase(caseId: string) {
  router.push(`/inbox/cases/${caseId}`)
}



async function handleCreateSummary() {
  if (!memberId.value || !orgId.value || !customerName.value) return
  // Use a default date range (last 3 years)
  const endDate = new Date().toISOString().split('T')[0] + 'T00:00:00.00Z'
  const startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 3)).toISOString().split('T')[0] + 'T00:00:00.00Z'
  await summarizerStore.createCustomerSummary({
    orgId: orgId.value,
    customerId: memberId.value,
    customerName: customerName.value,
    startDate,
    endDate
  })
}
</script>
<template>
  <div class="case-customer-container" style="border-left-color: var(--border-color);">
    <!-- Fixed Header -->
    <div class="case-customer-header px-4 pt-5 pb-4 bg-white ">
      <BravoTitle1>Customer</BravoTitle1>
    </div>
    
    <!-- Scrollable Body -->
    <div class="case-customer-body">
      <div>
        <div v-if="summarizerStore.loading" class="text-slate-500 italic">Loading summary...</div>
        <template v-if="summarizerStore.error || !summarizerStore.customerSummary">
          <BravoZeroStateScreen
            title="No Customer Summary"
            message="No customer summary has been created yet. Generate a comprehensive summary of this customer's history and interactions."
            buttonLabel="Create Customer Summary"
            buttonIcon="pi pi-plus"
            imageIcon="pi pi-user"
            :actionHandler="handleCreateSummary"
          />
          <div v-if="creatingSummary" class="text-slate-500 italic mt-2">Creating summary. This could take a while...</div>
          <div v-if="createSummaryError" class="text-red-500 mt-2">{{ createSummaryError }}</div>
        </template>
        <template v-else>
          <div class="p-4">
            <!-- Summary -->
            <div class="mb-5">
              <BravoTitle3 class="section-title">Summary</BravoTitle3>
              <div>
                {{ summarizerStore.customerSummary.summary || summarizerStore.customerSummary.text || 'No summary available.' }}
              </div>
            </div>

            <!-- Sentiment -->
            <div class="mb-5">
              <BravoTitle3 class="section-title">Sentiment</BravoTitle3>
              <div class="flex items-center gap-2">
                <BravoTag 
                  :severity="sentimentSeverity"
                  :value="summarizerStore.customerSummary.sentiment"
                />
                <span v-if="summarizerStore.customerSummary.most_common_issue" class="text-slate-600 text-sm">
                  <span class="font-medium">Most Common Issue:</span> {{ summarizerStore.customerSummary.most_common_issue }}
                </span>
              </div>
            </div>

            <!-- Engagement Level -->
            <div v-if="summarizerStore.customerSummary.engagement_level" class="mb-5">
              <BravoTitle3 class="section-title">Engagement Level</BravoTitle3>
              <div>{{ summarizerStore.customerSummary.engagement_level }}</div>
            </div>

            <!-- Open Issues -->
            <div v-if="summarizerStore.customerSummary.open_issues" class="mb-5">
              <BravoTitle3 class="section-title">Open Issues</BravoTitle3>
              <div v-if="summarizerStore.customerSummary.open_issues.length" class="ml-2">
                <ul class="list-disc pl-4">
                  <li v-for="(issue, idx) in summarizerStore.customerSummary.open_issues" :key="idx">
                    {{ issue }}
                  </li>
                </ul>
              </div>
              <div v-else class="ml-2">No open issues</div>
            </div>

            <!-- Trending Topics -->
            <div v-if="summarizerStore.customerSummary.trending_topics" class="mb-5">
              <BravoTitle3 class="section-title">Trending Topics</BravoTitle3>
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="(topic, idx) in summarizerStore.customerSummary.trending_topics" 
                  :key="idx"
                  class="bg-blue-100 text-blue-600 rounded px-2 py-0.5 text-xs font-semibold"
                >
                  {{ topic }}
                </span>
              </div>
            </div>

            <!-- Suggestions -->
            <div v-if="summarizerStore.customerSummary.suggestions" class="mb-5">
              <BravoTitle3 class="section-title">Suggestions</BravoTitle3>
              <ul class="list-disc pl-4 ml-2">
                <li v-for="(suggestion, idx) in summarizerStore.customerSummary.suggestions" :key="idx">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </template>

        <!-- Accordions -->
        <Accordion v-model="expandedIndex" multiple class="mt-2 w-full max-w-full overflow-hidden">
          <AccordionPanel v-for="(section, idx) in accordions" :key="section.key" :value="idx">
            <AccordionHeader>
              <div v-if="section.key === 'summary'" class="case-pane-tab-header">{{ section.label }}</div>
              <span v-else class="font-semibold text-slate-700">{{ section.label }}</span>
            </AccordionHeader>
            <AccordionContent class="w-full max-w-full overflow-hidden">
              <template v-if="section.key === 'customer'">
                <div class="py-2">
                  <div class="flex flex-wrap gap-8 mb-2">
                    <div>
                      <div class="text-xs text-slate-400">Name</div>
                      <div class="font-medium text-slate-700">{{ customerName }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">Email</div>
                      <div class="font-medium text-slate-700">{{ customerEmail }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">Phone</div>
                      <div class="font-medium text-slate-700">{{ customerPhone }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">Customer Since</div>
                      <div class="font-medium text-slate-700">{{ customerSince }}</div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else-if="section.key === 'location'">
                <div class="py-2">
                  <div class="flex flex-wrap gap-8 mb-2">
                    <div>
                      <div class="text-xs text-slate-400">Address</div>
                      <div class="font-medium text-slate-700">{{ address }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">City</div>
                      <div class="font-medium text-slate-700">{{ city }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">Country</div>
                      <div class="font-medium text-slate-700">{{ country }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">Timezone</div>
                      <div class="font-medium text-slate-700">{{ timezone }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-slate-400">Phone</div>
                      <div class="font-medium text-slate-700">{{ locationPhone }}</div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else-if="section.key === 'products'">
                <div class="py-2 flex flex-col gap-2">
                  <div v-if="memberStore.loadingProducts" class="text-slate-500 italic">Loading products...</div>
                  <div v-else-if="memberStore.productsError" class="text-red-500">{{ memberStore.productsError }}</div>
                  <template v-else-if="memberStore.memberProducts.length">
                    <div 
                      v-for="product in memberStore.memberProducts" 
                      :key="product.id"
                      class="bg-white border border-slate-200 rounded p-3"
                    >
                      <div class="flex items-start justify-between">
                        <div>
                          <div class="font-medium text-slate-700">{{ product.label || product.lbl }}</div>
                          <div class="text-sm text-slate-500 mt-1">
                            <span v-if="product.make" class="mr-2">{{ product.make }}</span>
                            <span v-if="product.model">{{ product.model }}</span>
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <span 
                            :class="[
                              'rounded px-2 py-0.5 text-xs font-semibold',
                              product.c__d_status === 'Ok' ? 'bg-green-100 text-green-600' :
                              product.c__d_status === 'Unknown' ? 'bg-yellow-100 text-yellow-600' :
                              'bg-red-100 text-red-600'
                            ]"
                          >
                            {{ product.c__d_status || 'Unknown' }}
                          </span>
                          <span 
                            :class="[
                              'rounded px-2 py-0.5 text-xs font-semibold',
                              product.c__d_active === 'Yes' ? 'bg-green-100 text-green-600' : 'bg-slate-100 text-slate-600'
                            ]"
                          >
                            {{ product.c__d_active || 'No' }}
                          </span>
                        </div>
                      </div>
                      <div class="mt-2 flex flex-wrap gap-4 text-xs text-slate-500">
                        <div v-if="product.created">
                          <span class="font-medium">Created:</span> {{ new Date(product.created).toLocaleDateString() }}
                        </div>
                        <div v-if="product.updated">
                          <span class="font-medium">Updated:</span> {{ new Date(product.updated).toLocaleDateString() }}
                        </div>
                        <div v-if="product.c__d_type">
                          <span class="font-medium">Type:</span> {{ product.c__d_type }}
                        </div>
                      </div>
                    </div>
                  </template>
                  <div v-else class="text-slate-500 italic">No products available</div>
                </div>
              </template>
              <template v-else-if="section.key === 'caseHistory'">
                <div class="py-2 flex flex-col gap-2 w-full max-w-full overflow-hidden">
                  <div v-if="casesStore.loadingMemberCases" class="text-slate-500 italic">Loading case history...</div>
                  <div v-else-if="casesStore.memberCasesError" class="text-red-500">{{ casesStore.memberCasesError }}</div>
                  <template v-else-if="casesStore.memberCases.length">
                                        <div 
                      v-for="caseItem in casesStore.memberCases" 
                      :key="caseItem.id"
                      class="bg-white border border-slate-200 rounded p-3 cursor-pointer hover:bg-slate-50 transition-colors w-full max-w-full overflow-hidden min-w-0"
                      @click="navigateToCase(caseItem.id)"
                    >
                      <!-- Top row: Case name and status -->
                      <div class="flex items-start justify-between mb-1">
                        <div class="flex items-center gap-2 min-w-0 flex-1 mr-3">
                          <div class="w-5 flex justify-center flex-shrink-0">
                            <i 
                              :class="getCaseIcon(caseItem)" 
                              class="text-slate-500 text-lg"
                              :title="getCaseIconLabel(caseItem)"
                            ></i>
                          </div>
                          <div class="flex items-center gap-1 min-w-0 truncate">
                            <BravoSubhead v-if="caseItem.reference_num" class="flex-shrink-0">
                              {{ caseItem.reference_num }} |
                            </BravoSubhead>
                            <BravoSubhead class="truncate min-w-0">{{ caseItem.display_name }}</BravoSubhead>
                          </div>
                        </div>
                        <div class="flex-shrink-0">
                          <BravoTag 
                            :value="getStatusInfo(caseItem.status).label"
                            :state="getStatusInfo(caseItem.status).state"
                          />
                        </div>
                      </div>
                      <!-- Bottom row: Team info and timestamp -->
                      <div class="flex items-center justify-between">
                        <div v-if="(caseItem as any).owner_partners_teams_id" class="flex items-center gap-2">
                          <div class="w-5 flex justify-center flex-shrink-0">
                            <img 
                              v-if="(caseItem as any).c__partners_avatar" 
                              :src="(caseItem as any).c__partners_avatar" 
                              :alt="getCaseTeamName(caseItem, partnerStore.partnerTeams)"
                              class="w-5 h-5 rounded-sm object-cover object-center"
                            />
                          </div>
                          <BravoCaption2 :style="{ color: 'var(--text-color-secondary)' }">
                            {{ getCaseTeamName(caseItem, partnerStore.partnerTeams) }}
                          </BravoCaption2>
                        </div>
                        <div v-else class="w-5"></div>
                        <div class="flex-shrink-0">
                          <BravoTimestamp 
                            v-if="caseItem.updated" 
                            :datetime="caseItem.updated" 
                            length="long" 
                            :style="{ color: 'var(--text-color-secondary)' }"
                          />
                          <BravoCaption2 v-else :style="{ color: 'var(--text-color-secondary)' }">
                            —
                          </BravoCaption2>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div v-else class="text-slate-500 italic">No case history available</div>
                </div>
              </template>
            </AccordionContent>
          </AccordionPanel>
        </Accordion>
      </div>
    </div>
  </div>
</template>
<style scoped>
.case-customer-container {
  height: 100%;
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  border-left: 1px solid var(--border-color);
  overflow: hidden;
}
.case-customer-header {
  flex-shrink: 0;
  background: white;
  display: flex;
  align-items: center;
}
.case-customer-body {
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background: white;
  min-height: 0;
}
.min-h-full { min-height: 100%; }
.case-pane-tab-header {
  font-size: 1.125rem;
  font-weight: 600;
  color: #22223b;
  padding: 0.75rem 1rem 0.5rem 1rem;
  border-bottom: 2px solid #e5e7eb;
  background: #fff;
  margin-bottom: 0;
  letter-spacing: 0.01em;
}
.section-title {
  padding-bottom: 0.4rem;
}
</style> 