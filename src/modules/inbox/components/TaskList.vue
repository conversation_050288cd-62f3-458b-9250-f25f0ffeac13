<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTitle3 from '@services/ui-component-library/components/BravoTypography/BravoTitle3.vue'
import { useRouter, useRoute, RouterLink } from 'vue-router'
import { useTasksStore } from '@/stores/tasks'
import { useUserStore } from '@/stores/user'
import Tooltip from 'primevue/tooltip'
import { getTaskCaseReference, getTaskCaseRoute } from '../utils/taskHelpers'

// Register the tooltip directive
const vTooltip = Tooltip

interface Task {
  id: string
  title: string
  description: string
}

interface SidebarItem {
  id?: string
  label: string
}

interface Props {
  selectedView: SidebarItem | null
  sidebarWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
  sidebarWidth: 280
})

const router = useRouter()
const route = useRoute()
const tasksStore = useTasksStore()
const userStore = useUserStore()

const isLoading = computed(() => tasksStore.loading)
const error = computed(() => tasksStore.error)

const tasks = computed(() => {
  const allTasks = props.selectedView?.id === 'my-tasks' 
    ? tasksStore.userTasks 
    : props.selectedView?.id === 'team-tasks' 
    ? tasksStore.teamTasks 
    : props.selectedView?.id === 'all-tasks'
    ? tasksStore.allTasks
    : []
  
  // Filter to only show tasks with status "Not Started"
  return allTasks.filter(task => task.status === 'Not Started')
})

// Add expand functionality
const isExpanded = ref(false)

const expandedLeftPosition = computed(() => {
  return props.sidebarWidth + 56
})

const handleToggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

async function fetchTasksForView() {
  if (!props.selectedView) return
  if (props.selectedView.id === 'my-tasks') {
    const userId = userStore.userData?.id || userStore.userData?.value?.id || 'mock-user-id'
    await tasksStore.loadUserTasks(userId)
  } else if (props.selectedView.id === 'team-tasks') {
    await tasksStore.loadTeamTasks()
  } else if (props.selectedView.id === 'all-tasks') {
    await tasksStore.loadAllTasks()
  }
}

// Fetch tasks on mount and when selectedView changes
watch(
  () => props.selectedView?.id,
  () => {
    fetchTasksForView()
  },
  { immediate: true }
)

function formatDate(dateStr: string) {
  if (!dateStr) return '—'
  const date = new Date(dateStr)
  return date.toLocaleString(undefined, { year: 'numeric', month: 'numeric', day: 'numeric', hour: '2-digit', minute: '2-digit' })
}

const handleCaseClick = (event: Event, task: any) => {
  event.stopPropagation() // Prevent the task item click from firing
  const caseRoute = getTaskCaseRoute(task)
  if (caseRoute) {
    router.push(caseRoute)
  }
}
</script>

<template>
  <div :class="['view-card', { expanded: isExpanded }]" :style="{ '--expanded-left': `${expandedLeftPosition}px` }">
    <div class="view-content">
      <div class="view-header">
        <BravoTitle1>{{ props.selectedView?.label || 'Tasks' }}</BravoTitle1>
        <div class="view-actions">
          <BravoButton 
            icon="pi pi-refresh" 
            size="small" 
            @click="fetchTasksForView"
            :disabled="isLoading"
            aria-label="Refresh Tasks"
            tooltip="Refresh"
          />
          <BravoButton 
            :icon="isExpanded ? 'pi pi-chevron-left' : 'pi pi-chevron-right'"
            severity="secondary"
            text
            @click="handleToggleExpand"
            :aria-label="isExpanded ? 'Collapse view' : 'Expand view'"
            v-tooltip.top="{
              value: isExpanded ? 'Collapse view' : 'Expand view',
              showDelay: 400
            }"
          />
        </div>
      </div>
      <div v-if="isLoading" class="loading-state">
        <BravoProgressSpinner />
        <span>Loading tasks...</span>
      </div>
      <div v-else-if="error" class="error-state">
        <p>{{ error }}</p>
        <BravoButton 
          label="Retry" 
          size="small" 
          @click="fetchTasksForView"
        />
      </div>
      <div v-else-if="tasks.length === 0" class="no-tasks">
        <p>No tasks found</p>
      </div>
      <ul v-else class="tasks-list">
        <RouterLink
          v-for="task in tasks"
          :key="task.id"
          :to="{ name: 'inbox-task-detail', params: { id: task.id }, query: route.query }"
          custom
          v-slot="{ navigate, href, isActive }"
        >
          <li
            :class="['task-item', { 'selected-task': route.params.id === task.id || isActive }]"
            :tabindex="0"
            @click="navigate"
            :href="href"
          >
            <div class="task-header">
              <BravoTitle3>{{ task.name }}</BravoTitle3>
            </div>
            <div class="task-details">
              <div><span class="label">Description:</span> <span>{{ task.description || '—' }}</span></div>
              <div><span class="label">Due Date:</span> <span>{{ formatDate(task.dueDate) }}</span></div>
              <div>
                <span class="label">Case Reference ID:</span> 
                <span 
                  v-if="getTaskCaseRoute(task)" 
                  class="case-reference-link"
                  @click="(event) => handleCaseClick(event, task)"
                >
                  {{ getTaskCaseReference(task) }}
                </span>
                <span v-else>{{ getTaskCaseReference(task) }}</span>
              </div>
            </div>
          </li>
        </RouterLink>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.view-card {
  background: var(--surface-0);
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 0;
  padding-top: 0;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), z-index 0.3s ease;
  position: relative;
  z-index: 1;
}

.view-card.expanded {
  position: fixed;
  top: 0;
  left: var(--expanded-left);
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.view-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  padding-top: 0;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  height: 64px;
  border-bottom: 1px solid var(--border-color);
}

.view-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  flex: 1;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  background-color: var(--red-50);
  color: var(--red-600);
  margin: 1rem;
  border-radius: 4px;
}

.no-tasks {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--surface-600);
  flex: 1;
}

.tasks-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.task-item {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-100);
  transition: background-color 0.2s;
  cursor: pointer;
}

.task-header {
  margin-bottom: 0.5rem;
}

.task-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}

.task-details {
  font-size: 0.95rem;
  color: var(--surface-900);
}

.label {
  font-weight: 500;
  color: var(--surface-700);
  margin-right: 0.25rem;
}

.selected-task {
  background: #e8f1fb;
  border-left: 4px solid #2563eb;
}

.case-reference-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.case-reference-link:hover {
  color: var(--primary-700);
  text-decoration: none;
}
</style> 