<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useCasesStore } from '../../../stores/cases'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import draggable from 'vuedraggable'
import { v4 as uuidv4 } from 'uuid'
import type { View } from '@/composables/services/useSettingsAPI'

// Export the interface so it can be imported by other components
export interface SidebarItem {
  id: string
  label: string
  count: number
  icon: string
  type: 'item' | 'folder'
  isOpen?: boolean
  children?: SidebarItem[]
}

interface ContextMenu {
  visible: boolean
  x: number
  y: number
  targetId: string | null
}

const casesStore = useCasesStore()
const isLoading = ref(false)
const loadError = ref<string | null>(null)

// Store sidebar items in a reactive reference
const sidebarItems = ref<SidebarItem[]>([
  {
    id: 'inbox',
    label: 'Your Inbox',
    count: 5,
    icon: '📥',
    type: 'item'
  },
  {
    id: 'mentions',
    label: 'Mentions',
    count: 2,
    icon: '@',
    type: 'item'
  },
  {
    id: 'created',
    label: 'Created by you',
    count: 6,
    icon: '👤',
    type: 'item'
  },
  {
    id: 'all',
    label: 'All',
    count: 91,
    icon: '📋',
    type: 'item'
  },
  {
    id: 'unassigned',
    label: 'Unassigned',
    count: 8,
    icon: '❓',
    type: 'item'
  },
  {
    id: 'custom-views',
    label: 'Custom Views',
    count: 0,
    icon: '📁',
    type: 'folder',
    isOpen: true,
    children: []
  }
])

// Get the CustomViews folder
const customViewsFolder = computed(() => {
  return sidebarItems.value.find(item => item.id === 'custom-views')
})

// Function to fetch custom views from the API - Defined BEFORE onMounted uses it
const fetchCustomViews = async () => {
  isLoading.value = true
  loadError.value = null
  
  try {
    // Call the fetchViews function from the store
    console.log('Calling fetchViews from store')
    await casesStore.fetchViews()
    
    // Now we can access views directly since we've exposed it in the store
    // TypeScript might need a type assertion here if the type definitions haven't been updated
    const fetchedViews = (casesStore as any).views || []
    console.log('Fetched views:', fetchedViews)
    
    // Find custom views folder
    const customViews = customViewsFolder.value
    console.log('Custom views folder before adding items:', customViews)
    
    if (customViews && customViews.children) {
      // Clear existing children
      customViews.children = []
      
      // Add each view as a child item
      fetchedViews.forEach((view: View) => {
        customViews.children?.push({
          id: view.id,
          label: view.label,
          count: 0, // Default count
          icon: '⚙️', // Default icon
          type: 'item'
        })
      })
      
      console.log('Added views to custom views folder:', customViews.children)
    }
  } catch (err) {
    console.error('Error fetching views:', err)
    loadError.value = err instanceof Error ? err.message : 'Failed to load views'
  } finally {
    isLoading.value = false
  }
}

// Fetch views on component mount
onMounted(async () => {
  await fetchCustomViews()
})

const contextMenu = ref<ContextMenu>({
  visible: false,
  x: 0,
  y: 0,
  targetId: null
})

const toggleFolder = (folder: SidebarItem) => {
  if (folder.type === 'folder') {
    folder.isOpen = !folder.isOpen
  }
}

const addNewFolder = () => {
  sidebarItems.value.push({
    id: uuidv4(),
    label: 'New Folder',
    count: 0,
    icon: '📁',
    type: 'folder',
    isOpen: true,
    children: []
  })
}

const showContextMenu = (event: MouseEvent, item: SidebarItem) => {
  if (item.type === 'folder') {
    event.preventDefault()
    contextMenu.value = {
      visible: true,
      x: event.clientX,
      y: event.clientY,
      targetId: item.id
    }
  }
}

const hideContextMenu = () => {
  contextMenu.value.visible = false
}

const deleteFolder = () => {
  if (contextMenu.value.targetId) {
    // Don't allow deletion of the CustomViews folder
    if (contextMenu.value.targetId === 'custom-views') {
      hideContextMenu()
      return
    }
    
    const index = sidebarItems.value.findIndex(item => item.id === contextMenu.value.targetId)
    if (index !== -1) {
      sidebarItems.value.splice(index, 1)
    }
  }
  hideContextMenu()
}

// Close context menu when clicking elsewhere
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.context-menu') && contextMenu.value.visible) {
    hideContextMenu()
  }
}

// Add event listener for clicks outside the context menu
document.addEventListener('click', handleClickOutside)
document.addEventListener('contextmenu', (e) => {
  if (!e.target || !(e.target as HTMLElement).closest('.sidebar-item.is-folder')) {
    hideContextMenu()
  }
})

// Remove event listener when component is unmounted
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('contextmenu', hideContextMenu)
})

// Drag options to prevent dragging outside of the same list
const dragOptions = {
  animation: 200,
  group: 'sidebar-items',
  disabled: false,
  ghostClass: 'ghost'
}

// Function to emit when a sidebar item is selected
const selectItem = (item: SidebarItem) => {
  if (item.type === 'folder') {
    toggleFolder(item)
  } else {
    // Emit the selected item
    emit('select-item', item)
  }
}

// Define emits
const emit = defineEmits<{
  (e: 'select-item', item: SidebarItem): void
}>()
</script>

<template>
  <div class="inbox-sidebar">
    <div class="sidebar-header">
      <h2>Inbox</h2>
      <BravoButton 
        icon="pi pi-plus" 
        class="add-folder-btn" 
        size="small" 
        @click="addNewFolder"
        aria-label="Add Folder"
        tooltip="Add New Folder"
      />
    </div>
    
    <div v-if="isLoading" class="loading-state">
      <span class="loading-spinner"></span>
      <span>Loading views...</span>
    </div>
    
    <div v-if="loadError" class="error-message">
      Failed to load views: {{ loadError }}
      <BravoButton
        label="Retry"
        size="small"
        @click="fetchCustomViews"
        class="retry-button"
      />
    </div>
    
    <draggable 
      v-model="sidebarItems" 
      class="sidebar-sections"
      item-key="id"
      v-bind="dragOptions"
    >
      <template #item="{ element }">
        <div class="sidebar-item-wrapper">
          <!-- Item or folder header -->
          <div 
            :class="['sidebar-item', { 'is-folder': element.type === 'folder' }]"
            @click="selectItem(element)"
            @contextmenu="showContextMenu($event, element)"
          >
            <span class="item-icon">{{ element.icon }}</span>
            <span class="item-label">{{ element.label }}</span>
            <span v-if="element.count > 0" class="item-count">{{ element.count }}</span>
            <span v-if="element.type === 'folder'" class="folder-toggle">
              {{ element.isOpen ? '▼' : '▶' }}
            </span>
          </div>
          
          <!-- Nested draggable for folder children -->
          <div v-if="element.type === 'folder' && element.isOpen && element.children" class="folder-children">
            <draggable 
              v-model="element.children" 
              class="nested-items"
              item-key="id"
              v-bind="dragOptions"
            >
              <template #item="{ element: child }">
                <div 
                  class="sidebar-item nested-item" 
                  @click.stop="emit('select-item', child)"
                >
                  <span class="item-icon">{{ child.icon }}</span>
                  <span class="item-label">{{ child.label }}</span>
                  <span class="item-count" v-if="child.count > 0">{{ child.count }}</span>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </template>
    </draggable>
    
    <!-- Context Menu -->
    <div 
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ top: `${contextMenu.y}px`, left: `${contextMenu.x}px` }"
    >
      <div class="context-menu-item delete-option" @click="deleteFolder">
        <span class="menu-icon">🗑️</span>
        <span>Delete Folder</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.inbox-sidebar {
  width: 250px;
  background: white;
  border-right: 1px solid var(--surface-200);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%; /* Use 100% height instead of viewport height to adapt to container */
  min-height: 100vh; /* Ensure at least full viewport height */
  overflow-y: auto; /* Add vertical scrollbar when content overflows */
  position: relative; /* For proper scrolling behavior */
  box-sizing: border-box; /* Include padding in width/height calculations */
}

/* Ensure the sidebar header stays at the top */
.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  padding-bottom: 0.5rem;
}

/* Make sure the scrollable content takes available space */
.sidebar-sections {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  overflow-y: auto;
}

.sidebar-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--surface-900);
  margin: 0;
}

.add-folder-btn {
  min-width: 2rem;
  height: 2rem;
}

.loading-state {
  display: flex;
  align-items: center;
  color: var(--surface-600);
  font-size: 0.875rem;
  padding: 0.5rem;
  gap: 0.5rem;
}

.loading-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid var(--surface-200);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  background-color: var(--red-50);
  color: var(--red-600);
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.retry-button {
  align-self: flex-end;
}

.sidebar-item-wrapper {
  display: flex;
  flex-direction: column;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 0.75rem;
}

.sidebar-item:hover {
  background-color: var(--surface-100);
}

.is-folder {
  font-weight: 500;
  color: var(--surface-800);
}

.folder-toggle {
  font-size: 0.75rem;
  margin-left: auto;
  color: var(--surface-600);
}

.folder-children {
  margin-left: 1.5rem;
  margin-top: 0.25rem;
}

.nested-items {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nested-item {
  font-size: 0.8125rem;
  padding: 0.375rem 0.5rem;
}

.item-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.item-label {
  flex: 1;
  font-size: 0.875rem;
  color: var(--surface-900);
}

.item-count {
  background: var(--surface-100);
  padding: 0.125rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  color: var(--surface-600);
  font-weight: 500;
}

.ghost {
  opacity: 0.5;
  background: var(--surface-200);
}

.context-menu {
  position: fixed;
  background: white;
  border: 1px solid var(--surface-200);
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  min-width: 150px;
  padding: 0.25rem;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-radius: 4px;
  gap: 0.5rem;
}

.context-menu-item:hover {
  background-color: var(--surface-100);
}

.delete-option {
  color: var(--red-600, #dc2626);
}

.delete-option:hover {
  background-color: var(--red-50, #fef2f2);
}

.menu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 