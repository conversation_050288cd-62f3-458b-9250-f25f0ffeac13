<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoBodyBold from '@services/ui-component-library/components/BravoTypography/BravoBodyBold.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import type { Issue } from '../../../services/IssuesAPI'

interface Props {
  visible: boolean
  issue: Issue | null
  resolutionType?: 'resolve' | 'cancel' | 'unresolved'
  onSubmit?: (resolutionNotes: string, resolutionType: 'resolve' | 'cancel' | 'unresolved') => Promise<void>
  onCancel?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  resolutionType: 'resolve'
})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const resolutionNotes = ref('')
const isLoading = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const modalConfig = computed(() => {
  if (props.resolutionType === 'cancel') {
    return {
      title: 'Cancel Case',
      confirmationText: 'Are you sure you want to cancel this case?',
      statusText: 'Cancelled',
      statusColor: 'red',
      buttonText: 'Cancel Case'
    }
  } else if (props.resolutionType === 'unresolved') {
    return {
      title: 'Set Case to Unresolved',
      confirmationText: 'Are you sure you want to set this case to unresolved?',
      statusText: 'Unresolved',
      statusColor: 'orange',
      buttonText: 'Set to Unresolved'
    }
  } else {
    return {
      title: 'Resolve Case',
      confirmationText: 'Are you sure you want to resolve this case?',
      statusText: 'Completed',
      statusColor: 'green',
      buttonText: 'Resolve Case'
    }
  }
})

const handleConfirm = async () => {
  if (!props.onSubmit) return
  
  isLoading.value = true
  
  try {
    await props.onSubmit(resolutionNotes.value, props.resolutionType)
    // Close modal on success
    emit('update:visible', false)
    resolutionNotes.value = ''
  } catch (error) {
    console.error('Error submitting resolution:', error)
    // Keep modal open on error so user can retry
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  resolutionNotes.value = ''
  if (props.onCancel) {
    props.onCancel()
  } else {
    emit('update:visible', false)
  }
}

const handleDialogHide = () => {
  if (isLoading.value) return // Prevent closing when loading
  resolutionNotes.value = ''
  emit('update:visible', false)
}

// Watch for visible prop changes to clear notes when modal opens
watch(() => props.visible, (newVisible, oldVisible) => {
  if (newVisible && !oldVisible) {
    // Modal is opening, clear any previous notes
    resolutionNotes.value = ''
    isLoading.value = false
  }
})
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    :header="modalConfig.title"
    :style="{ width: '450px' }"
    :closable="true"
    :close-on-escape="!isLoading"
    @hide="handleDialogHide"
    :class="['resolve-case-modal', { loading: isLoading }]"
  >
    <div class="resolve-modal-content">
      <BravoBody class="confirmation-text">
        {{ modalConfig.confirmationText }}
      </BravoBody>
      
      <div class="status-info" :class="`status-${modalConfig.statusColor}`">
        <BravoBodyBold>Status:</BravoBodyBold>
        <span class="status-value">{{ modalConfig.statusText }}</span>
      </div>
      
      <div class="resolution-notes-section">
        <label for="resolution-notes" class="notes-label">
          <BravoBodyBold>Resolution Notes</BravoBodyBold>
        </label>
        <BravoTextarea
          id="resolution-notes"
          v-model="resolutionNotes"
          placeholder="Enter resolution notes..."
          :rows="4"
          :disabled="isLoading"
          class="w-full"
        />
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          :label="modalConfig.buttonText"
          severity="primary"
          @click="handleConfirm"
          :loading="isLoading"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
/* Fix modal border radius and prevent resizing */
:deep(.resolve-case-modal .p-dialog) {
  border-radius: 8px !important;
}

:deep(.resolve-case-modal .p-dialog-content) {
  border-radius: 0 0 8px 8px !important;
}

:deep(.resolve-case-modal .p-dialog-header) {
  border-radius: 8px 8px 0 0 !important;
}

/* Disable close button when loading instead of hiding it */
:deep(.resolve-case-modal.loading .p-dialog-header-close) {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.resolve-modal-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.confirmation-text {
  margin-bottom: 0.5rem;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--surface-50);
  border-radius: 6px;
}

.status-info.status-green {
  border-left: 4px solid var(--green-500);
}

.status-info.status-green .status-value {
  color: var(--green-600);
  font-weight: 500;
}

.status-info.status-red {
  border-left: 4px solid var(--red-500);
}

.status-info.status-red .status-value {
  color: var(--red-600);
  font-weight: 500;
}

.resolution-notes-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.case-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--surface-50);
  border-radius: 6px;
  border-left: 4px solid var(--surface-300);
}

.case-name {
  color: var(--surface-700);
  font-weight: 500;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}



.modal-actions :deep(.p-button-label) {
  white-space: nowrap;
}
</style> 