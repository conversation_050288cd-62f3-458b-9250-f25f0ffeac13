<script setup lang="ts">
import { onMounted, watch, ref, computed } from 'vue'
import { useConnectorsStore } from '@/stores/connectors'
import ConnectorMFE from '../../../components/ConnectorMFE.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoBlock from '@services/ui-component-library/components/BravoBlock.vue'

const props = defineProps<{ issue: any }>()
const connectorsStore = useConnectorsStore()
const localLoading = ref(false)
const expandedIndex = ref<number | null>(null)

async function loadConnectorsForOrg() {
  if (props.issue && props.issue.context_org_id) {
    localLoading.value = true
    try {
      await connectorsStore.loadConnectors(props.issue.context_org_id)
    } finally {
      localLoading.value = false
    }
  }
}

onMounted(() => {
  loadConnectorsForOrg()
})

watch(() => props.issue?.context_org_id, (newOrgId) => {
  if (newOrgId) {
    loadConnectorsForOrg()
  }
})

const embeddedAppConnectors = computed(() => {
  const filtered = connectorsStore.connectors.filter(connector =>
    connector.features && connector.features['cases.embeddedApp.v1']
  )
  console.log('CaseApplications: All connectors:', connectorsStore.connectors)
  console.log('CaseApplications: Filtered embedded app connectors:', filtered)
  return filtered
})

function getConnectorIcon(connector: any) {
  const icon = connector.features['cases.embeddedApp.v1'].icon
  if (icon && icon.length > 0) {
    return icon
  }
  // fallback: colored dot
  return null
}

function togglePanel(idx: number) {
  expandedIndex.value = expandedIndex.value === idx ? null : idx
}
</script>
<template>
  <div class="case-apps-container" style="border-left-color: var(--border-color);">
    <!-- Fixed Header -->
    <div class="case-apps-header px-4 pt-5 pb-2 bg-white">
      <BravoTitle1>Apps</BravoTitle1>
    </div>
    
    <!-- Scrollable Body -->
    <div class="case-apps-body">
      <div class="px-4 pb-4 pt-4">
        <div v-if="localLoading">
          <!-- Skeleton loader that mimics the card structure -->
          <div v-for="i in 3" :key="'skeleton-' + i" class="mb-2">
            <div class="w-full flex items-center gap-2 px-4 py-3 rounded bg-gray-50 animate-pulse">
              <div class="flex items-center gap-2 flex-1">
                <!-- Icon skeleton -->
                <div class="w-6 h-6 rounded-full bg-gray-300"></div>
                <!-- Text skeleton -->
                <div class="h-4 bg-gray-300 rounded" :style="{ width: Math.random() * 60 + 80 + 'px' }"></div>
              </div>
              <!-- Chevron skeleton -->
              <div class="w-5 h-5 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
        <div v-else-if="connectorsStore.error" class="text-red-500 mb-4">{{ connectorsStore.error }}</div>
        <div v-else>
          <div v-if="embeddedAppConnectors.length === 0" class="text-slate-500 italic mb-4">No applications found.</div>
          <div v-else>
            <div v-for="(connector, idx) in embeddedAppConnectors" :key="connector.id" class="mb-2">
              <button
                class="w-full flex items-center gap-2 px-4 py-3 rounded bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 transition justify-between"
                :aria-expanded="expandedIndex === idx"
                :aria-controls="'panel-content-' + idx"
                @click="togglePanel(idx)"
              >
                <span class="flex items-center gap-2">
                  <span v-if="getConnectorIcon(connector)">
                    <img :src="getConnectorIcon(connector)" alt="icon" class="w-6 h-6" />
                  </span>
                  <span v-else class="w-6 h-6 rounded-full bg-orange-500 inline-block"></span>
                  <span class="font-semibold text-gray-700">
                    {{ connector.name || connector.features['cases.embeddedApp.v1'].displayName }}
                  </span>
                </span>
                <span class="ml-auto text-gray-400">
                  <svg v-if="expandedIndex === idx" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" /></svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                </span>
              </button>
              <div
                v-if="expandedIndex === idx"
                :id="'panel-content-' + idx"
                class="p-4 border border-t-0 border-gray-200 rounded-b bg-white"
              >
                <ConnectorMFE
                  :orgId="connector.orgId"
                  :connectorId="connector.connectorId"
                  :instanceId="connector.instanceId"
                  :staticResources="connector.features['cases.embeddedApp.v1'].staticResources"
                  :componentName="connector.features['cases.embeddedApp.v1'].componentName"
                  :context="{ 
                    issueId: issue.id,
                    orgId: connector.orgId,
                    connectorId: connector.connectorId,
                    instanceId: connector.instanceId,
                    componentName: connector.features['cases.embeddedApp.v1'].componentName
                  }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.case-apps-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  border-left: 1px solid var(--border-color);
}
.case-apps-header {
  flex-shrink: 0;
  background: white;
  height: 48px;
  display: flex;
  align-items: center;
}
.case-apps-body {
  flex: 1;
  overflow-y: auto;
  background: white;
  min-height: 0;
}
</style> 