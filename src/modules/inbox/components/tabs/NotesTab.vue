<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useCasesStore } from '../../../../stores/cases'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import { useInteractionEventsAPI, type InteractionEvent } from '@/composables/services/useInteractionEventsAPI'

// Extend the InteractionEvent interface to include properties that might be present in the actual API response
interface ExtendedInteractionEvent extends InteractionEvent {
  log_id?: string
  type?: {
    id: string
    label: string
  }
  refs?: {
    user?: {
      user_avatar?: string
      users_full_name?: string
      users_nickname?: string
    }
  }
}

interface Props {
  issueId?: string
}

const props = defineProps<Props>()

const casesStore = useCasesStore()
const interactionEventsAPI = useInteractionEventsAPI()
const route = useRoute()

// Local state for interaction events
const interactionEvents = ref<InteractionEvent[]>([])
const loadingEvents = ref(false)
const eventsError = ref<string | null>(null)
const newNote = ref('')

// Get case ID from route parameters or props
const caseId = computed(() => props.issueId || route.params.id as string)

// Use current issue from the store
const issue = computed(() => casesStore.currentIssue)

// Filter notes from the interaction events
const notes = computed(() => {
  return interactionEvents.value.filter(ev => (ev as any).type?.id === 'note-added') as ExtendedInteractionEvent[]
})

// Fetch interaction events for the current issue
async function fetchInteractionEvents() {
  const currentCaseId = caseId.value
  if (!currentCaseId) {
    console.warn('⚠️ NotesTab: Cannot fetch events: No case ID available');
    return;
  }

  loadingEvents.value = true;
  eventsError.value = null;

  try {
    // Ensure the current issue is loaded if it's not already
    if (!casesStore.currentIssue || casesStore.currentIssue.id !== currentCaseId) {
      await casesStore.fetchCurrentIssue(currentCaseId)
    }

    console.log('📞 NotesTab: Fetching interaction events for case:', currentCaseId);
    const filters = [
      {"property":"source_object_id","value": currentCaseId},
      {"property":"source_object","value":"issues"}
    ];
    
    interactionEvents.value = await interactionEventsAPI.fetchInteractionEvents({
      issueId: currentCaseId,
      filter: filters
    });
    
    console.log('✅ NotesTab: Interaction events loaded:', interactionEvents.value.length);
  } catch (err) {
    console.error('❌ NotesTab: Error fetching interaction events:', err);
    eventsError.value = err instanceof Error ? err.message : 'An error occurred';
  } finally {
    loadingEvents.value = false;
  }
}

async function submitNote() {
  const currentCaseId = caseId.value
  if (!newNote.value.trim() || !currentCaseId) return
  try {
    await casesStore.addNote(currentCaseId, newNote.value)
    newNote.value = ''
    // Refresh events to show the new note
    await fetchInteractionEvents()
  } catch (err) {
    console.error('Failed to add note:', err)
  }
}

// Watch for case ID changes and reload activity
watch(caseId, (newCaseId, oldCaseId) => {
  console.log('🔍 NotesTab: Case ID changed from', oldCaseId, 'to', newCaseId);
  if (newCaseId && newCaseId !== oldCaseId) {
    console.log('🔄 NotesTab: Reloading activity due to case ID change');
    fetchInteractionEvents();
  }
}, { immediate: true })

// Watch for case updates (like resolve, escalate, etc.) and refresh events
watch(() => issue.value?.updated, (newUpdated, oldUpdated) => {
  console.log('🔄 NotesTab: Issue updated timestamp changed from', oldUpdated, 'to', newUpdated);
  if (newUpdated && oldUpdated && newUpdated !== oldUpdated) {
    console.log('🔄 NotesTab: Refreshing events due to case update');
    fetchInteractionEvents();
  }
})

// Listen for case activity refresh events from the store
function handleCaseActivityRefresh(event: CustomEvent) {
  const { caseId: refreshCaseId } = event.detail;
  console.log('🔄 NotesTab: Received activity refresh event for case:', refreshCaseId);
  
  // Only refresh if this is for the current case
  if (refreshCaseId === caseId.value) {
    console.log('🔄 NotesTab: Refreshing events due to store activity refresh');
    fetchInteractionEvents();
  }
}

onMounted(() => {
  fetchInteractionEvents()
  
  // Add event listener for case activity refresh
  window.addEventListener('case-activity-refresh', handleCaseActivityRefresh as EventListener)
})

// Clean up event listener on unmount
onUnmounted(() => {
  window.removeEventListener('case-activity-refresh', handleCaseActivityRefresh as EventListener)
})
</script>

<template>
  <div class="notes-content">
    <div v-if="loadingEvents" class="notes-skeleton">
      <div v-for="i in 3" :key="'note-skeleton-' + i" class="note-skeleton-item">
        <BravoSkeleton width="100%" height="60px" border-radius="6px" class="mb-3" />
      </div>
    </div>
    <div v-else-if="eventsError">{{ eventsError }}</div>
    <div v-else-if="notes.length === 0">No notes found.</div>
    <div v-else class="notes-list">
      <div
        v-for="note in notes"
        :key="note.created + '-' + note.body.data"
        class="note-card"
      >
        <div class="note-body" v-html="note.body.data"></div>
        <div class="note-meta">{{ note.created }}</div>
      </div>
    </div>
    <div class="add-note-section">
      <BravoTextarea v-model="newNote" rows="3" placeholder="Add a new note..." />
      <BravoButton @click="submitNote" :disabled="!newNote.trim()" label="Save Note"></BravoButton>
    </div>
  </div>
</template>

<style scoped>
.notes-content {
  color: var(--surface-600);
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
  flex: 1;
  overflow-y: auto;
}

.note-card {
  background: #f5f7fa;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
  border-left: 4px solid #60a5fa;
}

.note-body {
  color: #374151;
  font-size: 1.05em;
  margin-bottom: 0.25rem;
  word-break: break-word;
}

.note-meta {
  font-size: 0.9em;
  color: #64748b;
  text-align: right;
}

.add-note-section {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex-shrink: 0;
}

.notes-skeleton {
  padding: 1rem 0;
}

.note-skeleton-item {
  background: var(--surface-100);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.5rem;
}
</style> 