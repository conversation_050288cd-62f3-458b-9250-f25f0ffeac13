<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import type { Issue } from '../../../services/IssuesAPI'

interface Props {
  visible: boolean
  issue: Issue | null
  onSubmit?: (readyData: any) => Promise<void>
  onCancel?: () => void
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// Form state
const readyReason = ref('')
const isLoading = ref(false)
const error = ref<string | null>(null)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleConfirm = async () => {
  if (!props.onSubmit || !props.issue?.id) return
  
  isLoading.value = true
  error.value = null

  try {
    const readyData = {
      waiting_reason: null,
      waiting_return_to: null,
      waiting_notes: readyReason.value.trim() || '', // Use empty string if no reason provided
      wait_until_preset: null,
      snooze_until_preset_group: null,
      wait_until: null,
      id: props.issue.id
    }

    await props.onSubmit(readyData)
    
    // Close modal and reset form on success
    emit('update:visible', false)
    resetForm()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to set case to ready'
  } finally {
    isLoading.value = false
  }
}

const handleCancel = () => {
  resetForm()
  if (props.onCancel) {
    props.onCancel()
  } else {
    emit('update:visible', false)
  }
}

const handleDialogHide = () => {
  if (isLoading.value) return // Prevent closing when loading
  resetForm()
  emit('update:visible', false)
}

const resetForm = () => {
  readyReason.value = ''
  error.value = null
  isLoading.value = false
}

// Watch for visible prop changes to reset form when modal opens
watch(() => props.visible, (newVisible, oldVisible) => {
  if (newVisible && !oldVisible) {
    // Modal is opening, reset form
    resetForm()
  }
})
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    modal
    header="Set to Ready"
    :style="{ width: '450px' }"
    :closable="true"
    :close-on-escape="!isLoading"
    @hide="handleDialogHide"
    :class="['ready-modal', { loading: isLoading }]"
  >
    <div class="ready-form">
      <div>Are you sure you want to set this case back to ready?</div>
      <!-- Hide the textarea for now since we don't need to collect a reason -->
      <!-- <BravoTextarea 
        v-model="readyReason" 
        rows="4" 
        placeholder="Why are you setting this case back to ready?"
        class="w-full mb-3"
        autoResize
        :disabled="isLoading"
      /> -->
      
      <div v-if="error" class="error-message mb-3">
        {{ error }}
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          :label="isLoading ? 'Setting to Ready...' : 'Set to Ready'"
          severity="primary"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="isLoading"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
/* Fix modal border radius and prevent resizing */
:deep(.ready-modal .p-dialog) {
  border-radius: 8px !important;
}

:deep(.ready-modal .p-dialog-content) {
  border-radius: 0 0 8px 8px !important;
}

:deep(.ready-modal .p-dialog-header) {
  border-radius: 8px 8px 0 0 !important;
}

/* Disable close button when loading */
:deep(.ready-modal.loading .p-dialog-header-close) {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.ready-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 0;
}

.error-message {
  color: var(--red-600);
  font-size: 0.875rem;
  padding: 0.75rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 6px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

/* Fix button width consistency */
.modal-actions :deep(.p-button) {
  min-width: 120px;
  height: 40px;
}

.modal-actions :deep(.p-button-label) {
  white-space: nowrap;
}
</style> 