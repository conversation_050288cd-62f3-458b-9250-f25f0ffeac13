<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import BravoSelectField from '@services/ui-component-library/components/BravoSelectField.vue'
import BravoRelativeDateTime from '@services/ui-component-library/components/BravoRelativeDateTime.vue'
import Dropdown from 'primevue/dropdown'
import Checkbox from 'primevue/checkbox'
import RadioButton from 'primevue/radiobutton'
import type { Issue } from '../../../services/IssuesAPI'
import type { Task } from '../../../types/task'
import { usePartnerStore } from '../../../stores/partner'
import { useUsersStore } from '../../../stores/users'
import { useUserStore } from '../../../stores/user'
import { useCasesStore } from '../../../stores/cases'
import { createTask } from '../../../composables/services/useTasksApi'
import type { EventPreset, EventPresetGroup } from '../../../composables/services/useIssuesAPI'

// Interface for form data when creating a task
interface CreateTaskData {
  name: string
  description: string
  dueDate: string | null // Will be converted to ISO string
  orgId: string | null
  teamId: string | null
  userId: string | null
}

interface TeamOption {
  label: string
  value: string
}

interface UserOption {
  label: string
  value: string
  teamId: string
}

const props = defineProps<{
  visible: boolean
  issue: Issue | null
  onSubmit?: (taskData: CreateTaskData) => Promise<void>
  onCancel?: () => void
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// Initialize partner store
const partnerStore = usePartnerStore()

// Initialize users store
const usersStore = useUsersStore()

// Initialize user store
const userStore = useUserStore()

// Initialize cases store
const casesStore = useCasesStore()

// Form state
const taskName = ref('')
const description = ref('')
const hasDueDate = ref(false)
const dateType = ref<string>('now') // 'now' or 'custom'
const presetDuration = ref<string | null>(null)
const specificDate = ref('')
const specificTime = ref('')
const assignedTeam = ref<string | null>(null)
const assignedUser = ref<string | null>(null)
const isLoading = ref(false)
const isLoadingTeams = ref(false)
const isLoadingUsers = ref(false)
const isLoadingDurationOptions = ref(false)

// Duration options from API
const presetGroups = ref<EventPresetGroup[]>([])
const presetOptions = ref<EventPreset[]>([])
const error = ref<string | null>(null)

// Add ref for the task name input to handle focus
const taskNameInputRef = ref()

// Validation error state
const validationErrors = ref({
  taskName: false,
  assignedTeam: false
})

// Computed property for team options from partner store
const teamOptions = computed(() => {
  return partnerStore.partnerTeams.map(team => ({
    label: team.lbl || team.val, // Use lbl for human-readable name, fallback to val
    value: team.val
  }))
})

// Computed property for user options filtered by selected team
const filteredUserOptions = computed(() => {
  // Use the users from the store (which will be filtered by team when team is selected)
  return usersStore.getUsersForSelect
})

// Computed properties
const isFormValid = computed(() => {
  return taskName.value.trim() !== '' && assignedTeam.value !== null
})

// Computed properties for date/time validation
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0] // Format: YYYY-MM-DD
})

const minTime = computed(() => {
  const today = new Date()
  const selectedDate = specificDate.value
  
  // If selected date is today, minimum time is current time
  if (selectedDate === minDate.value) {
    const hours = today.getHours().toString().padStart(2, '0')
    const minutes = today.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }
  
  // If selected date is in the future, no time restriction
  return '00:00'
})

// Validation for selected date/time
const isValidDateTime = computed(() => {
  if (!hasDueDate.value || dateType.value !== 'custom') return true
  if (!specificDate.value || !specificTime.value) return false
  
  const selectedDateTime = new Date(`${specificDate.value}T${specificTime.value}`)
  const now = new Date()
  
  return selectedDateTime > now
})

// Computed options for the current date type
const currentPresetOptions = computed(() => {
  return presetOptions.value.filter(preset => {
    return true // Include all presets for now
  })
})

// Load duration options from API
const loadDurationOptions = async () => {
  isLoadingDurationOptions.value = true
  try {
    const { groups, presets } = await casesStore.getDurationOptions()
    presetGroups.value = groups
    presetOptions.value = presets
    
    // Set default date type to first group if available
    if (groups.length > 0) {
      dateType.value = groups[0].val
    }
  } catch (err) {
    console.error('Failed to load duration options:', err)
    error.value = 'Failed to load duration options'
  } finally {
    isLoadingDurationOptions.value = false
  }
}

// Function to focus the task name input
function focusTaskNameInput() {
  // Target the specific input by ID
  const input = document.getElementById('task-name-input')
  if (input) {
    input.focus()
    return true
  }
  return false
}

// Watch for modal visibility changes
watch(() => props.visible, async (newValue) => {
  if (newValue) {
    resetForm()
    // Fetch teams when modal opens
    isLoadingTeams.value = true
    
    try {
      await partnerStore.fetchPartnerTeams()
      // Load duration options
      await loadDurationOptions()
    } catch (error) {
      console.error('Error fetching teams:', error)
    } finally {
      isLoadingTeams.value = false
    }
    
    // Focus the task name input after a short delay to ensure the modal is fully rendered
    await nextTick()
    setTimeout(() => focusTaskNameInput(), 100)
    setTimeout(() => focusTaskNameInput(), 200)
    setTimeout(() => focusTaskNameInput(), 300)
  }
})

// Watch for team changes to fetch users for that team
watch(assignedTeam, async (newTeam, oldTeam) => {
  // Reset user selection when team changes
  if (newTeam !== oldTeam) {
    assignedUser.value = null
  }
  
  // Clear team validation error when user selects a team
  if (newTeam) {
    validationErrors.value.assignedTeam = false
  }
  
  if (newTeam) {
    isLoadingUsers.value = true
    try {
      // Extract organization ID from team value (first 3 characters)
      const organizationId = newTeam.substring(0, 3)
      
      // Fetch users filtered by the selected team
      await usersStore.fetchUsersByTeam(newTeam, organizationId)
    } catch (error) {
      console.error('Error fetching team users:', error)
    } finally {
      isLoadingUsers.value = false
    }
  } else {
    // Clear users when no team is selected
    usersStore.clearUsers()
  }
})

// Watch for task name changes to clear validation error
watch(taskName, (newValue) => {
  if (newValue.trim() !== '') {
    validationErrors.value.taskName = false
  }
})

// Methods
function resetForm() {
  taskName.value = ''
  description.value = ''
  hasDueDate.value = false
  dateType.value = 'now'
  presetDuration.value = null
  specificDate.value = ''
  specificTime.value = ''
  assignedTeam.value = null
  assignedUser.value = null
  error.value = null
  // Reset validation errors
  validationErrors.value = {
    taskName: false,
    assignedTeam: false
  }
}

function validateForm() {
  const errors = {
    taskName: taskName.value.trim() === '',
    assignedTeam: assignedTeam.value === null
  }
  
  validationErrors.value = errors
  
  // Return true if no errors
  return !Object.values(errors).some(error => error)
}

// Utility function to parse duration from preset value
function parseDuration(presetValue: string): number {
  console.log('parseDuration called with value:', presetValue, 'type:', typeof presetValue)
  
  if (!presetValue) {
    console.log('No preset value provided')
    return 0
  }
  
  // Handle the actual system format: "now_p5", "later_p60", etc.
  const systemPatterns = [
    { regex: /^now_p(\d+)$/i, multiplier: 60 * 1000 }, // now_p5 = 5 minutes
    { regex: /^later_p(\d+)$/i, multiplier: 60 * 1000 }, // later_p60 = 60 minutes
    { regex: /^scheduled_p(\d+)$/i, multiplier: 60 * 1000 }, // scheduled_p120 = 120 minutes
  ]
  
  // Check system patterns first
  for (const pattern of systemPatterns) {
    const match = presetValue.match(pattern.regex)
    if (match) {
      const amount = parseInt(match[1], 10)
      const result = amount * pattern.multiplier
      console.log(`Matched system pattern ${pattern.regex} - amount: ${amount}, multiplier: ${pattern.multiplier}, result: ${result}ms`)
      return result
    }
  }
  
  // Common preset patterns: "5min", "1hour", "2hours", "1day", "2days", etc.
  const patterns = [
    // Minutes
    { regex: /^(\d+)min$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)mins$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)minute$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)minutes$/i, multiplier: 60 * 1000 },
    // Hours
    { regex: /^(\d+)h$/i, multiplier: 60 * 60 * 1000 },
    { regex: /^(\d+)hr$/i, multiplier: 60 * 60 * 1000 },
    { regex: /^(\d+)hrs$/i, multiplier: 60 * 60 * 1000 },
    { regex: /^(\d+)hour$/i, multiplier: 60 * 60 * 1000 },
    { regex: /^(\d+)hours$/i, multiplier: 60 * 60 * 1000 },
    // Days
    { regex: /^(\d+)d$/i, multiplier: 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)day$/i, multiplier: 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)days$/i, multiplier: 24 * 60 * 60 * 1000 },
    // Weeks
    { regex: /^(\d+)w$/i, multiplier: 7 * 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)week$/i, multiplier: 7 * 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)weeks$/i, multiplier: 7 * 24 * 60 * 60 * 1000 },
    // Space-separated formats
    { regex: /^(\d+)\s+min$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)\s+mins$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)\s+minute$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)\s+minutes$/i, multiplier: 60 * 1000 },
    { regex: /^(\d+)\s+hour$/i, multiplier: 60 * 60 * 1000 },
    { regex: /^(\d+)\s+hours$/i, multiplier: 60 * 60 * 1000 },
    { regex: /^(\d+)\s+day$/i, multiplier: 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)\s+days$/i, multiplier: 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)\s+week$/i, multiplier: 7 * 24 * 60 * 60 * 1000 },
    { regex: /^(\d+)\s+weeks$/i, multiplier: 7 * 24 * 60 * 60 * 1000 },
  ]
  
  for (const pattern of patterns) {
    const match = presetValue.match(pattern.regex)
    if (match) {
      const amount = parseInt(match[1], 10)
      const result = amount * pattern.multiplier
      console.log(`Matched pattern ${pattern.regex} - amount: ${amount}, multiplier: ${pattern.multiplier}, result: ${result}ms`)
      return result
    }
  }
  
  console.log('No pattern matched, trying numeric fallback')
  
  // If no pattern matches, try to parse as minutes (fallback)
  const numericValue = parseInt(presetValue, 10)
  if (!isNaN(numericValue)) {
    const result = numericValue * 60 * 1000 // Assume minutes
    console.log(`Numeric fallback - value: ${numericValue}, result: ${result}ms`)
    return result
  }
  
  console.log('Could not parse preset value:', presetValue)
  return 0
}

// Calculate due date from the form inputs
function calculateDueDate(): string | null {
  console.log('calculateDueDate called - hasDueDate:', hasDueDate.value, 'dateType:', dateType.value, 'presetDuration:', presetDuration.value)
  
  if (!hasDueDate.value) {
    console.log('No due date selected, returning null')
    return null
  }
  
  if (dateType.value === 'custom') {
    if (specificDate.value && specificTime.value) {
      const customDate = `${specificDate.value}T${specificTime.value}:00`
      console.log('Custom date calculated:', customDate)
      return customDate
    }
    console.log('Custom date selected but missing date/time values')
    return null
  }
  
  // For preset durations, calculate the actual future date
  if (presetDuration.value) {
    console.log('Parsing preset duration:', presetDuration.value)
    const durationMs = parseDuration(presetDuration.value)
    console.log('Parsed duration (ms):', durationMs)
    
    if (durationMs > 0) {
      const now = new Date()
      const futureDate = new Date(now.getTime() + durationMs)
      const isoString = futureDate.toISOString()
      console.log('Current time:', now.toISOString())
      console.log('Calculated due date:', isoString)
      return isoString
    } else {
      console.log('Failed to parse duration, got 0 ms')
    }
  } else {
    console.log('No preset duration selected')
  }
  
  return null
}

async function handleSubmit() {
  // Validate form first
  if (!validateForm()) {
    return // Stop submission if validation fails
  }
  
  if (!props.onSubmit) return
  
  // Debug user data
  console.log('Full userData:', userStore.userData)
  console.log('userData.object_id:', userStore.userData?.object_id)
  console.log('userData keys:', userStore.userData ? Object.keys(userStore.userData) : 'userData is null')
  
  const taskData: CreateTaskData = {
    name: taskName.value.trim(),
    description: description.value.trim(),
    dueDate: calculateDueDate(),
    orgId: userStore.userData?.object_id || null,
    teamId: assignedTeam.value,
    userId: assignedUser.value
  }
  
  console.log('Task data with orgId:', taskData)
  
  isLoading.value = true
  try {
    await props.onSubmit(taskData)
    emit('update:visible', false)
  } catch (error) {
    console.error('Error creating task:', error)
    // Error handling is done in the parent component
  } finally {
    isLoading.value = false
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel()
  }
  emit('update:visible', false)
}
</script>

<template>
  <BravoDialog
    :visible="visible"
    @update:visible="(value) => emit('update:visible', value)"
    header="Add Task"
    :modal="true"
    :style="{ width: '500px', minHeight: '400px' }"
    :closable="true"
    :closeOnEscape="!isLoading"
    :dismissableMask="!isLoading"
  >
    <div class="add-task-content">
      <div class="form-fields">
        <!-- Task Name -->
        <div class="field">
          <BravoLabel text="Task Name" isRequired />
          <BravoInputText
            id="task-name-input"
            v-model="taskName"
            placeholder="Enter task name..."
            :disabled="isLoading"
            :invalid="validationErrors.taskName"
            ref="taskNameInputRef"
            data-testid="task-name-input"
          />
        </div>

        <!-- Description -->
        <div class="field">
          <BravoLabel text="Description" />
          <BravoTextarea
            v-model="description"
            rows="4"
            placeholder="Enter task description..."
            :disabled="isLoading"
            autoResize
          />
        </div>

        <!-- Due Date -->
        <div class="field">
          <BravoLabel text="Due Date" />
          <div class="due-date-section">
            <div class="checkbox-group">
              <Checkbox
                v-model="hasDueDate"
                inputId="has-due-date"
                binary
                :disabled="isLoading"
              />
              <label for="has-due-date" class="checkbox-label">
                Set due date for this task
              </label>
            </div>

            <div v-if="hasDueDate" class="date-options">
              <div class="radio-options">
                <div 
                  v-for="group in presetGroups" 
                  :key="group.id" 
                  class="radio-group"
                >
                  <RadioButton
                    v-model="dateType"
                    :inputId="`date-type-${group.val}`"
                    :value="group.val"
                    :disabled="isLoading || isLoadingDurationOptions"
                  />
                  <label :for="`date-type-${group.val}`" class="radio-label">
                    {{ group.lbl }}
                  </label>
                </div>
              </div>

              <div v-if="dateType !== 'custom'" class="preset-dropdown">
                <Dropdown
                  v-model="presetDuration"
                  :options="currentPresetOptions"
                  optionLabel="lbl"
                  optionValue="val"
                  placeholder="Select duration..."
                  :disabled="isLoading || isLoadingDurationOptions"
                  :loading="isLoadingDurationOptions"
                  class="w-full"
                />
              </div>

              <div v-if="dateType === 'custom'" class="custom-date-fields">
                <div class="date-time-row">
                  <div class="date-input-group">
                    <label for="specific-date" class="input-label">Date</label>
                    <input
                      id="specific-date"
                      v-model="specificDate"
                      type="date"
                      :min="minDate"
                      class="date-input"
                      :class="{ 'invalid': specificDate && specificTime && !isValidDateTime }"
                      :disabled="isLoading"
                    />
                  </div>
                  <div class="time-input-group">
                    <label for="specific-time" class="input-label">Time</label>
                    <input
                      id="specific-time"
                      v-model="specificTime"
                      type="time"
                      :min="minTime"
                      class="time-input"
                      :class="{ 'invalid': specificDate && specificTime && !isValidDateTime }"
                      :disabled="isLoading"
                    />
                  </div>
                </div>
                <div v-if="specificDate && specificTime && !isValidDateTime" class="validation-error">
                  Please select a future date and time
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Assigned Team -->
        <div class="field">
          <BravoLabel text="Assigned Team" isRequired />
          <BravoSelectField
            id="assigned-team"
            v-model="assignedTeam"
            :options="teamOptions"
            placeholder="Select a team..."
            dataTestId="assigned-team"
            :disabled="isLoading || isLoadingTeams"
            :invalid="validationErrors.assignedTeam"
          />
        </div>

        <!-- Assigned User -->
        <div class="field">
          <BravoLabel text="Assigned User" />
          <BravoSelectField
            id="assigned-user"
            v-model="assignedUser"
            :options="filteredUserOptions"
            placeholder="Select a user..."
            dataTestId="assigned-user"
            :disabled="isLoading || isLoadingUsers || !assignedTeam"
          />
        </div>
      </div>
      
      <div v-if="error" class="error-message">
        {{ error }}
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
        />
        <BravoButton
          :label="isLoading ? 'Creating...' : 'Create Task'"
          severity="primary"
          @click="handleSubmit"
          :loading="isLoading"
          :disabled="isLoading"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
.add-task-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-error {
  font-size: 0.875rem;
  color: var(--red-500);
  margin-top: 0.25rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

/* Ensure consistent button sizing */
.modal-footer .p-button {
  min-height: 40px;
}

/* Remove shadow and padding from BravoRelativeDateTime wrapper */
.add-task-content :deep(.bravo-relative-datetime-input) {
  box-shadow: none !important;
}

.add-task-content :deep(.p-inputwrapper) {
  padding: 0 !important;
  box-shadow: none !important;
}

.add-task-content :deep(.p-datepicker) {
  box-shadow: none !important;
}

.add-task-content :deep(.p-inputgroup) {
  box-shadow: none !important;
}

.due-date-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-label {
  cursor: pointer;
  user-select: none;
  font-size: 0.9rem;
}

.date-options {
  border-left: 3px solid var(--primary-color);
  padding-left: 1rem;
  margin-left: 0.5rem;
  background: var(--surface-50);
  border-radius: 0 4px 4px 0;
  padding: 1rem;
}

.radio-options {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.radio-label {
  cursor: pointer;
  font-size: 0.9rem;
}

.preset-dropdown {
  width: 100%;
}

.custom-date-fields {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-time-row {
  display: flex;
  gap: 1rem;
}

.date-input-group,
.time-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--surface-700);
}

.date-input,
.time-input {
  padding: 0.5rem;
  border: 1px solid var(--surface-300);
  border-radius: 4px;
  font-size: 0.875rem;
}

.date-input:focus,
.time-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.date-input.invalid,
.time-input.invalid {
  border-color: var(--red-500);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.validation-error {
  font-size: 0.875rem;
  color: var(--red-600);
  margin-top: 0.5rem;
}

.error-message {
  color: var(--red-600);
  font-size: 0.875rem;
  padding: 0.75rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 6px;
}
</style>