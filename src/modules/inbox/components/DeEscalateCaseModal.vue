<template>
  <BravoDialog
    :visible="visible"
    @update:visible="(value) => emit('update:visible', value)"
    header="De-escalate Case"
    :modal="true"
    :style="{ width: '500px', minHeight: '400px' }"
    :closable="true"
    :closeOnEscape="!isLoading"
    :dismissableMask="!isLoading"
  >
    <div class="deescalate-case-content">
      <div class="current-status">
        <h4>Current Escalation Status</h4>
        <div class="status-info">
          <div class="info-row">
            <span class="label">Status:</span>
            <span class="value escalated">Escalated</span>
          </div>
          <div class="info-row" v-if="issue?.c__d_escalation_reasons">
            <span class="label">Reason:</span>
            <span class="value">{{ issue.c__d_escalation_reasons }}</span>
          </div>
        </div>
      </div>
      
      <div class="form-fields">
        <!-- De-escalation Team -->
        <div class="field">
          <BravoLabel text="Assign to Team" required />
          <Dropdown
            v-model="deEscalationTeamId"
            :options="teams"
            optionLabel="lbl"
            optionValue="val"
            placeholder="Select team to assign case to"
            :filter="true"
            :loading="loadingTeams"
            class="w-full"
            :disabled="isLoading"
          />
        </div>

        <!-- Owner User -->
        <div class="field">
          <BravoLabel text="Assign to User" />
          <Dropdown
            v-model="deEscalationUserId"
            :options="filteredUsers"
            optionLabel="lbl"
            optionValue="val"
            placeholder="Select user to assign case to"
            :filter="true"
            :loading="loadingUsers"
            class="w-full"
            :disabled="isLoading"
          />
        </div>

        <!-- De-escalation Notes -->
        <div class="field">
          <BravoLabel text="De-escalation Notes" />
          <BravoTextarea
            v-model="deEscalationNotes"
            rows="4"
            placeholder="Enter de-escalation notes..."
            class="w-full"
            :disabled="isLoading"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <BravoButton
          label="Cancel"
          severity="secondary"
          @click="handleCancel"
          :disabled="isLoading"
          style="min-width: 100px;"
        />
        <BravoButton
          :label="isLoading ? 'De-escalating...' : 'De-escalate'"
          severity="primary"
          @click="handleSubmit"
          :loading="isLoading"
          :disabled="isLoading || !isFormValid"
          style="min-width: 120px;"
        />
      </div>
    </template>
  </BravoDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import Dropdown from 'primevue/dropdown'
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue'
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue'
import type { Issue } from '../../../services/IssuesAPI'
import { useMetaStore } from '@/stores/meta'
import { useCasesStore } from '@/stores/cases'

const props = defineProps<{
  visible: boolean
  issue: Issue | null
  onSubmit?: (params: {
    deEscalationTeamId: string
    deEscalationUserId?: string
    deEscalationNotes?: string
  }) => Promise<void>
  onCancel?: () => void
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const metaStore = useMetaStore()
const casesStore = useCasesStore()

// Form state
const deEscalationTeamId = ref('')
const deEscalationUserId = ref('')
const deEscalationNotes = ref('')
const isLoading = ref(false)

// Data loading states
const loadingTeams = ref(false)
const loadingUsers = ref(false)

// Computed properties
const teams = computed(() => {
  return casesStore.issuePartnerTeams?.pl__issues_partners_teams || []
})

const filteredUsers = computed(() => {
  if (!deEscalationTeamId.value || !metaStore.partnerMetaData?.pl__partners_users) {
    return []
  }
  
  // Find the selected team to get its partners_id
  const selectedTeam = teams.value.find(team => team.val === deEscalationTeamId.value)
  if (!selectedTeam) {
    return []
  }
  
  // Filter users by the selected team's partners_id
  return metaStore.partnerMetaData.pl__partners_users.filter((user: any) => 
    user.partners_id === selectedTeam.partners_id
  ) || []
})

const isFormValid = computed(() => {
  return deEscalationTeamId.value
})

// Watch for modal visibility changes
watch(() => props.visible, (newValue) => {
  if (newValue) {
    resetForm()
    loadData()
  }
})

// Watch for team selection changes to clear user selection
watch(deEscalationTeamId, () => {
  deEscalationUserId.value = ''
})

// Methods
function resetForm() {
  deEscalationTeamId.value = ''
  deEscalationUserId.value = ''
  deEscalationNotes.value = ''
}

async function loadData() {
  await Promise.all([
    loadTeams(),
    loadUsers()
  ])
}

async function loadTeams() {
  if (!props.issue?.members_locations_id) {
    console.warn('No members_locations_id available for issue')
    return
  }
  
  loadingTeams.value = true
  try {
    await casesStore.fetchIssuePartnerTeams(props.issue.members_locations_id, props.issue.id)
  } catch (error) {
    console.error('Error loading teams:', error)
  } finally {
    loadingTeams.value = false
  }
}

async function loadUsers() {
  loadingUsers.value = true
  try {
    // Ensure partner metadata is loaded
    if (!metaStore.partnerMetaData) {
      await metaStore.loadPartnerMetaData()
    }
  } catch (error) {
    console.error('Error loading users:', error)
  } finally {
    loadingUsers.value = false
  }
}

async function handleSubmit() {
  if (!isFormValid.value || !props.onSubmit) return
  
  isLoading.value = true
  try {
    await props.onSubmit({
      deEscalationTeamId: deEscalationTeamId.value,
      deEscalationUserId: deEscalationUserId.value || undefined,
      deEscalationNotes: deEscalationNotes.value || undefined
    })
    
    emit('update:visible', false)
  } catch (error) {
    console.error('Error de-escalating case:', error)
    // Error handling is done in the parent component
  } finally {
    isLoading.value = false
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel()
  }
  emit('update:visible', false)
}
</script>

<style scoped>
.deescalate-case-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.current-status h4 {
  margin: 0 0 1rem 0;
  color: var(--surface-700);
  font-size: 1rem;
  font-weight: 600;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
}

.info-row {
  display: flex;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #92400e;
  min-width: 60px;
}

.value {
  color: #92400e;
}

.value.escalated {
  font-weight: 700;
  color: #d97706;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
}

/* Ensure consistent button sizing */
.modal-footer .p-button {
  min-height: 40px;
}
</style> 