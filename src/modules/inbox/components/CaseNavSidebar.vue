<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'
import Tooltip from 'primevue/tooltip'

const vTooltip = Tooltip

const route = useRoute()
const router = useRouter()

const menuItems = [
  { key: 'details', icon: 'pi pi-briefcase', tooltip: 'Case Details' },
  { key: 'customer', icon: 'pi pi-user', tooltip: 'Customer Details' },
  { key: 'knowledge', icon: 'pi pi-book', tooltip: 'Knowledge' },
  { key: 'applications', icon: 'pi pi-th-large', tooltip: 'Apps' },
]

function handleClick(key: string) {
  const query = { ...route.query }
  if (key === 'details') {
    delete query.panel
  } else {
    query.panel = key
  }
  router.replace({ query })
}

const activeKey = computed(() => route.query.panel || 'details')
</script>
<template>
  <nav class="case-details-menu flex flex-col items-center py-4 h-full bg-white border-l border-slate-200">
    <button
      v-for="item in menuItems"
      :key="item.key"
      :class="['menu-icon', { active: activeKey === item.key } ]"
      @click="handleClick(item.key)"
      v-tooltip.right="{ value: item.tooltip, showDelay: 200 }"
      type="button"
    >
      <i :class="item.icon"></i>
    </button>
  </nav>
</template>
<style scoped>
.case-details-menu {
  width: 48px;
  min-width: 48px;
  background: var(--surface-50);
  border-left: 1px solid var(--border-color);
  height: 100%;
  padding-top: 1rem;
  padding-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.menu-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  border-radius: 8px;
  color: var(--icon-color-primary);
  background: none;
  border: none;
  transition: background 0.15s, color 0.15s;
  cursor: pointer;
}
.menu-icon.active {
  background: var(--surface-200);
  color: var(--icon-color-secondary);
}
.menu-icon:hover {
  background: var(--surface-100);
  color: var(--icon-color-secondary);
}
.menu-icon.active:hover {
  background: var(--surface-200);
  color: var(--icon-color-secondary);
}
.menu-icon:last-child {
  margin-bottom: 0;
}
.menu-icon i {
  font-size: 16px;
}
</style> 