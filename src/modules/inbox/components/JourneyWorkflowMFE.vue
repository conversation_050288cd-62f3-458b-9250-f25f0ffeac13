<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { mfeLoader } from '../../../services/MFELoaderService'
import { mfeAdapter } from '../../../services/MFEAdapterService'
import { getHttpClient } from '../../../services/httpClientProvider'
import { useAuthStore } from '../../../stores/auth'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import AdminHttpClient from '../../../services/AdminHttpClient'

interface EnvConfig {
  journeysUrl: string;
  journeysUrlNLO: string;
  tasksUrl: string;
  notificationsUrl: string;
  adminUrl: string;
}

const envConfigs: Record<string, EnvConfig> = {
  local: {
    journeysUrl: 'https://s-journeys-bf16bf5a-5xeedb3yvq-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-5xeedb3yvq-uc.a.run.app',
    tasksUrl: import.meta.env.VITE_TASKS_SERVICE_URL || 'https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage',
    notificationsUrl: 'https://notify-svc-5xeedb3yvq-uc.a.run.app',
    adminUrl: 'http://localhost:5173/api',
  },
  stage: {
    journeysUrl: 'https://s-journeys-bf16bf5a-5xeedb3yvq-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-5xeedb3yvq-uc.a.run.app',
    tasksUrl: import.meta.env.VITE_TASKS_SERVICE_URL || 'https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage',
    notificationsUrl: 'https://notify-svc-5xeedb3yvq-uc.a.run.app',
    adminUrl: 'https://app.stage.goboomtown.com/api',
  },
  preprod: {
    journeysUrl: 'https://s-journeys-627397ad-nfznudrdwa-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-nfznudrdwa-uc.a.run.app',
    tasksUrl: import.meta.env.VITE_TASKS_SERVICE_URL || 'https://us-central1-preprod-microservices-9a06.cloudfunctions.net/service-tasks-preprod',
    notificationsUrl: 'https://notify-svc-nfznudrdwa-uc.a.run.app',
    adminUrl: 'https://app.preprod.goboomtown.com/api',
  },
  prod: {
    journeysUrl: 'https://s-journeys-8f5419eb-7hyimkhqqq-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-7hyimkhqqq-uc.a.run.app',
    tasksUrl: import.meta.env.VITE_TASKS_SERVICE_URL || 'https://us-central1-prod-microservices-d798.cloudfunctions.net/service-tasks-prod',
    notificationsUrl: 'https://notify-svc-7hyimkhqqq-uc.a.run.app',
    adminUrl: 'https://app.goboomtown.com/api',
  },
};

function getEnvConfig(): EnvConfig {
  const host = window.location.host;
  if (host.includes('local-env') || host.includes('localhost')) {
    return envConfigs.local;
  } else if (host.includes('stage')) {
    return envConfigs.stage;
  } else if (host.includes('preprod')) {
    return envConfigs.preprod;
  }
  return envConfigs.prod;
}

const props = defineProps<{
  journeyId: string
  workflowId: string
}>()

const isLoading = ref(true)
const error = ref<string | null>(null)

async function loadMFE() {
  isLoading.value = true
  try {
    await mfeLoader.loadMFE('journey-workflow')
    const config = mfeLoader.getMFEConfig('journey-workflow')
    const context: any = {
      orgId: 'H3F', // TODO: Get from config/store if needed
      userId: null,
      userPermissions: 'journey_manage',
      useNLO: true
    }
    // For journey-workflow, context.journey = { journeyId, workflowId }
    context.journey = {
      journeyId: props.journeyId,
      workflowId: props.workflowId
    }
    // Log the context for debugging
    console.log('[JourneyWorkflowIframe] MFE context:', context)
    const envConfig = getEnvConfig()
    const adminHttpClient = new AdminHttpClient(envConfig.adminUrl)
    await mfeAdapter.mountWidget(
      config.key,
      '#journey-workflow-iframe',
      {
        context,
        config: envConfig,
        adminHttpClient
      }
    )
  } catch (e) {
    console.error('Failed to load journey-workflow:', e)
    error.value = 'Failed to load Journey Workflow. Please try refreshing the page.'
  } finally {
    isLoading.value = false
  }
}

onMounted(loadMFE)
watch(() => [props.journeyId, props.workflowId], loadMFE)
</script>

<template>
  <div class="journey-workflow-view relative">
    <BravoProgressSpinner v-show="isLoading" />
    <div v-show="!isLoading" id="journey-workflow-iframe"></div>
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<style scoped lang="scss">
.journey-workflow-view {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  height: 100%;
  width: 100%;
  min-height: 400px;
  overflow: auto;
}
.error-message {
  color: var(--red-500);
  text-align: center;
  padding: 2rem;
}
#journey-workflow-iframe {
  flex: 1;
  min-height: 0;
  width: 100%;
}

.journey-workflow-view {
    &:deep(input:not(#\#)) {
        appearance: auto;
    }
}
</style> 