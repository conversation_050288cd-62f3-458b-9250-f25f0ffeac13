<script setup lang="ts">
import { computed, ref, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useCasesStore } from '../../../stores/cases'
import { useTasksStore } from '../../../stores/tasks'
import { useUserStore } from '@/stores/user'
import BravoPanelMenuNav from '@services/ui-component-library/components/BravoPanelMenuNav.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoBadge from '@services/ui-component-library/components/BravoBadge.vue'
import CreateCaseModal from './CreateCaseModal.vue'
import { useMemberStore } from '../../../stores/member'
import { useMetaStore } from '../../../stores/meta'
import { usePartnerStore } from '../../../stores/partner'
import InboxCasesList from './InboxCasesList.vue'
import InboxDashboard from './InboxDashboard.vue'
import TaskList from './TaskList.vue'
import Tooltip from 'primevue/tooltip'
import type { View } from '@/composables/services/useSettingsAPI'
import type { Issue } from '../../../services/IssuesAPI'
import type { MemberLocation } from '../../../composables/services/useMemberAPI'

// Register the tooltip directive
const vTooltip = Tooltip

// Export the interface so it can be imported by other components
export interface SidebarItem {
  key?: string
  id?: string
  label: string
  count?: number
  icon?: string
  type?: 'item' | 'folder' | 'task'
  isOpen?: boolean
  children?: SidebarItem[]
  badge?: number | string
  items?: SidebarItem[]
  command?: () => void
  active?: boolean
  to?: any
}

// Define emits
const emit = defineEmits<{
  'dashboard-selected': [isDashboard: boolean]
}>()

const casesStore = useCasesStore()
const tasksStore = useTasksStore()
const userStore = useUserStore()
const memberStore = useMemberStore()
const metaStore = useMetaStore()
const partnerStore = usePartnerStore()
const isLoading = ref(false)
const initialLoading = ref(false)
const loadError = ref<string | null>(null)
const yourInboxCount = ref(0)
const router = useRouter()
const route = useRoute()

// Add ref to store view counts
const viewCounts = ref<{ [viewId: string]: number }>({})

// Add collapsible functionality
const isMinimized = ref(false)
const panelWidth = ref(280) // Set starting width to 280px

// Add Case dialog state
const showAddCaseDialog = ref(false)

const yourInboxBadge = computed(() => yourInboxCount.value || '—')

// Add computed properties for task counts
const myTasksCount = computed(() => tasksStore.userTasks?.filter(task => task.status === 'Not Started')?.length || 0)
const teamTasksCount = computed(() => tasksStore.teamTasks?.filter(task => task.status === 'Not Started')?.length || 0)
const allTasksCount = computed(() => tasksStore.allTasks?.filter(task => task.status === 'Not Started')?.length || 0)

const model = ref<SidebarItem[]>([
  {
    key: 'dashboard',
    label: 'Dashboard',
    icon: 'pi pi-chart-bar',
    id: 'dashboard',
    type: 'item',
    command: () => selectItem({ label: 'Dashboard', id: 'dashboard', type: 'item' })
  },
  {
    key: 'cases',
    label: 'Cases',
    type: 'folder',
    id: 'cases-panel',
    items: [
      {
        key: 'inbox',
        label: 'Your Inbox',
        icon: 'pi pi-inbox',
        get badge() { return yourInboxBadge.value },
        id: 'inbox',
        type: 'item',
        command: () => selectItem({ label: 'Your Inbox', id: 'inbox', type: 'item' })
        // to: { path: '/inbox', query: { view: 'inbox' } }

      },
    ]
  },
  {
    key: 'tasks',
    label: 'Tasks',
    type: 'folder',
    id: 'tasks-panel',
    items: [
      {
        key: 'my-tasks',
        label: 'My Open Tasks',
        icon: 'pi pi-list',
        get badge() { return myTasksCount.value || '—' },
        id: 'my-tasks',
        type: 'item',
        command: () => selectItem({ label: 'My Open Tasks', id: 'my-tasks', type: 'task' })
      },
      {
        key: 'team-tasks',
        label: 'My Team\'s Open Tasks',
        icon: 'pi pi-list',
        get badge() { return teamTasksCount.value || '—' },
        id: 'team-tasks',
        type: 'item',
        command: () => selectItem({ label: 'My Team\'s Open Tasks', id: 'team-tasks', type: 'task' })
      },
      {
        key: 'all-tasks',
        label: 'All Open Tasks',
        icon: 'pi pi-list',
        get badge() { return allTasksCount.value || '—' },
        id: 'all-tasks',
        type: 'item',
        command: () => selectItem({ label: 'All Open Tasks', id: 'all-tasks', type: 'task' })
      },
    ]
  }
])

const selectedView = ref<SidebarItem | null>(null)

// Expanded keys for PanelMenu: start with both Cases and Tasks expanded
const expandedKeys = ref<{ [key: string]: boolean }>({
  cases: true,
  tasks: true
})

const objectType = computed(() => route.query.object || '')

const taskType = computed(() => {
  if (objectType.value === 'tasks') {
    if (selectedView.value?.id === 'my-tasks') return 'user'
    if (selectedView.value?.id === 'team-tasks') return 'team'
    if (selectedView.value?.id === 'all-tasks') return 'all'
  }
  return 'user'
})

// Watch for route query changes to set the correct view
watch(
  () => [route.query.view, route.query.object],
  ([view, newObject]) => {
    // Handle dashboard view immediately when present in route
    if (view === 'dashboard' && route.path === '/inbox') {
      const dashboardItem = model.value.find(item => item.id === 'dashboard')
      if (dashboardItem) {
        selectedView.value = dashboardItem
      }
      return
    }
    
    // Only default to dashboard if there is no view in the query and we're on the inbox route
    if (!view && route.path === '/inbox') {
      if (!newObject || newObject === '') {
        // Default to dashboard view if no specific object is requested
        const dashboardItem = model.value.find(item => item.id === 'dashboard')
        if (dashboardItem) {
          selectedView.value = dashboardItem
        }
      } else if (newObject === 'tasks') {
        // Default to first task view
        const taskItem = model.value[2].items?.[0]
        if (taskItem) {
          selectedView.value = taskItem
        }
      }
    }
  },
  { immediate: true }
)

// Add a new watch for views loading
watch(
  () => casesStore.views,
  async (newViews) => {
    if (newViews && newViews.length > 0) {
      // If we have a view in the query, try to find and select it
      if (route.query.view) {
        const viewId = route.query.view as string
        
        // Check if it's the dashboard view first
        if (viewId === 'dashboard') {
          const dashboardItem = model.value.find(item => item.id === 'dashboard')
          if (dashboardItem) {
            selectedView.value = dashboardItem
          }
        } else {
          // First check in cases panel (now index 1)
          const casesPanel = model.value.find(item => item.label === 'Cases')
          const matchingView = casesPanel?.items?.find(item => item.id === viewId)
          if (matchingView) {
            selectedView.value = matchingView
          }
        }
      }
      await nextTick()
      highlightActiveSidebarItemAfterModelLoaded()
    }
  },
  { immediate: true }
)

const SPECIAL_VIEWS_ORDER = [
  'All Cases',
  'Open Cases',
  'All Unassigned Cases',
  "My Team's Unassigned Cases"
];
const SPECIAL_VIEWS_ICONS = [
  'pi pi-star',
  'pi pi-star',
  'pi pi-star',
  'pi pi-star'
];

// Function to fetch custom views from the API
const fetchCustomViews = async () => {
  isLoading.value = true
  loadError.value = null
  try {
    // Only add views to the model, don't fetch them
    const fetchedViews = (casesStore as any).views || []
    // Sort special views to the top
    const specialViews: View[] = []
    const otherViews: View[] = []
    
    // Filter out views with type = 1 and sort the remaining ones
    fetchedViews
      .filter((view: View) => view.type !== 1)
      .forEach((view: View) => {
        const idx = SPECIAL_VIEWS_ORDER.indexOf(view.label)
        if (idx !== -1) {
          specialViews[idx] = view
        } else {
          otherViews.push(view)
        }
      })

    // Remove any undefined (if a special view is missing)
    const sortedSpecialViews = specialViews.filter(Boolean)
    const sortedViews = [...sortedSpecialViews, ...otherViews]
    const casesPanel = model.value.find(item => item.label === 'Cases')
    if (casesPanel && casesPanel.items) {
      sortedViews.forEach((view: View) => {
        let icon = 'pi pi-cog'
        const idx = SPECIAL_VIEWS_ORDER.indexOf(view.label)
        if (idx !== -1) {
          icon = SPECIAL_VIEWS_ICONS[idx]
        }
        casesPanel.items?.push({
          label: view.label,
          icon,
          get badge() { return viewCounts.value[view.id] || '—' },
          id: view.id, // Add id for routing
          command: () => selectItem({ id: view.id, label: view.label })
        })
      })
    }
    // After loading custom views, select the correct view from query
    if (route.query.view) {
      const viewId = route.query.view as string
      
      // Check if it's the dashboard view first
      if (viewId === 'dashboard') {
        const dashboardItem = model.value.find(item => item.id === 'dashboard')
        if (dashboardItem) {
          selectedView.value = dashboardItem
        }
      } else {
        const casesPanel = model.value.find(item => item.label === 'Cases')
        const matchingView = casesPanel?.items?.find(item => item.id === viewId)
        if (matchingView) {
          selectedView.value = matchingView
        }
      }
    }

    // After fetching and building the sidebar model, highlight the correct item
    highlightActiveSidebarItemAfterModelLoaded()

  } catch (err) {
    console.error('Error fetching views:', err)
    loadError.value = err instanceof Error ? err.message : 'Failed to load views'
  } finally {
    isLoading.value = false
  }
}

// Fetch views and my work issues on component mount
onMounted(async () => {
  initialLoading.value = true
  loadError.value = null
  try {
    if (route.query.object === 'tasks') {
      expandedKeys.value = { cases: true, tasks: true }
      const view = route.query.view as string | undefined
      const userId = userStore.userData?.id || ''
      if (view === 'team-tasks') {
        await tasksStore.loadTeamTasks()
        const teamTasksItem = model.value[2].items?.find(item => item.id === 'team-tasks')
        if (teamTasksItem) selectedView.value = teamTasksItem
      } else if (view === 'all-tasks') {
        await tasksStore.loadAllTasks()
        const allTasksItem = model.value[2].items?.find(item => item.id === 'all-tasks')
        if (allTasksItem) selectedView.value = allTasksItem
      } else {
        await tasksStore.loadUserTasks(userId)
        const myTasksItem = model.value[2].items?.find(item => item.id === 'my-tasks')
        if (myTasksItem) selectedView.value = myTasksItem
      }
      
      // Fetch both task counts for accurate badges, plus other data
      await Promise.all([
        casesStore.fetchViews(),
        fetchYourInboxCount(),
        fetchTaskCounts() // This will load both user and team tasks
      ])
      await fetchCustomViews()
      // Fetch case view counts after views are loaded
      await fetchCaseViewCounts()
      await nextTick()
      highlightActiveSidebarItemAfterModelLoaded()
    } else {
      await Promise.all([
        casesStore.fetchViews(),
        fetchYourInboxCount(),
        fetchTaskCounts()
      ])
      await fetchCustomViews()
      // Fetch case view counts after views are loaded
      await fetchCaseViewCounts()
      await nextTick()
      highlightActiveSidebarItemAfterModelLoaded()
      await nextTick();
      // Expand the Cases panel to show the case views
      expandPanelById('cases-panel')
      // Only set selected view to 'Dashboard' and update the route if there is no view in the query
      // AND we're on the exact /inbox route (not child routes like /inbox/cases/123)
      // Child routes will have paths like /inbox/cases/123 or /inbox/tasks/456
      if (!route.query.view && route.path === '/inbox') {
        const dashboardItem = model.value.find(item => item.id === 'dashboard')
        if (dashboardItem) {
          selectedView.value = dashboardItem
          router.replace({ path: '/inbox', query: { view: 'dashboard' } })
        }
      }
    }
  } catch (err) {
    loadError.value = err instanceof Error ? err.message : 'Failed to load data'
  } finally {
    initialLoading.value = false
  }
})

const isYourInboxSelected = computed(() => selectedView.value?.id === 'inbox')

const selectItem = (item: any) => {
  selectedView.value = item
  
  // Emit dashboard selection event
  emit('dashboard-selected', item.id === 'dashboard')
  
  // Auto-expand when selecting an item in minimized mode
  if (isMinimized.value) {
    isMinimized.value = false
  }
  
  if (item.type === 'task') {
    router.push({
      path: '/inbox',
      query: { view: item.id, object: 'tasks' }
    })
  } else {
    router.push({
      path: '/inbox',
      query: { view: item.id }
    })
  }
}

const handleSelectIssue = (issue: Issue) => {
  const type = selectedView.value?.key === 'tasks' ? 'tasks' : 'cases'
  router.push({
    path: `/inbox/${type}/${issue.id}`,
    query: route.query.view ? { view: route.query.view } : undefined
  })
}

const handleCreateCase = () => {
  showAddCaseDialog.value = true
}

const handleCaseCreated = (newCase: any) => {
  console.log('Case created successfully:', newCase)
  // Optionally refresh the cases list or navigate to the new case
  // You could emit an event here to refresh the parent component
}

const handleSearch = () => {
  // Implement search logic
  console.log('Search clicked')
}

const handleManageViews = () => {
  // Implement manage views logic
  console.log('Manage Views clicked')
}

const handleTogglePanel = () => {
  isMinimized.value = !isMinimized.value
}

// Update active state based on current route, not just selectedView
watch(
  () => [route.query.view, route.query.object, selectedView.value],
  ([view, object, selected]) => {
    const selectedItem = selected as SidebarItem | null
    model.value.forEach(panel => {
      // Handle top-level items (like Dashboard)
      if (panel.type === 'item') {
        panel.active = false
        // Dashboard is active when it's the selected view
        if (panel.id === 'dashboard' && selectedItem?.id === 'dashboard') {
          panel.active = true
        }
      }
      // Handle nested items in folders
      panel.items?.forEach(item => {
        item.active = false
        if (item.id === view && route.path === '/inbox') {
          item.active = true
        }
        // Default to inbox if no view param on inbox route
        if (!view && item.id === 'inbox' && route.path === '/inbox' && (!object || object === 'cases')) {
          item.active = true
        }
      })
    })
  },
  { immediate: true }
)

// Remove isActive from every model item, and set it only on this item
function updateActiveSidebarItem(selected: SidebarItem | null) {
  model.value.forEach(panel => {
    // Handle top-level items
    if (panel.type === 'item') {
      panel.active = false
    }
    // Handle nested items
    panel.items?.forEach(item => {
      item.active = false
    })
  })
  if (selected) {
    model.value.forEach(panel => {
      // Check top-level items
      if (panel.type === 'item' && panel.id === selected.id) {
        panel.active = true
      }
      // Check nested items
      panel.items?.forEach(item => {
        if (item.id === selected.id) {
          item.active = true
        }
      })
    })
  }
}

// After fetching and building the sidebar model, highlight the correct item
function highlightActiveSidebarItemAfterModelLoaded() {
  const view = route.query.view as string | undefined
  const object = route.query.object as string | undefined
  const path = route.path
  let activeItem: SidebarItem | null = null
  
  model.value.forEach(panel => {
    // Check top-level items (like Dashboard)
    if (panel.type === 'item' && panel.id === 'dashboard' && path === '/dashboard') {
      activeItem = panel
    }
    // Check nested items
    panel.items?.forEach(item => {
      // Match by id and type for inbox routes
      const isTask = object === 'tasks'
      if (item.id === view && path === '/inbox' && ((isTask && item.type === 'task') || (!isTask && item.type === 'item'))) {
        activeItem = item
      }
      // Default to inbox if no view param on inbox route
      if (!view && item.id === 'inbox' && path === '/inbox' && (!object || object === 'cases')) {
        activeItem = item
      }
    })
  })
  
  updateActiveSidebarItem(activeItem)
}

// Add after existing computed properties
const sidebarWidth = computed(() => {
  return isMinimized.value ? 68 : 280
})

// Fetch count for "Your Inbox" using the same filter as InboxCasesList
const fetchYourInboxCount = async () => {
  try {
    // Create the same filter as used in InboxCasesList
    const params: any = {
      filter: [
        {
          property: 'owner_users_id',
          value: userStore.userData?.id || '',
          operator: 'eq'
        },
        {
          property: 'status',
          value: ['2', '1'], // Ready and New
          operator: 'in'
        }
      ],
      sort: [
        {
          property: 'updated',
          direction: 'DESC'
        }
      ]
    }
    
    // Fetch cases with the filter to get the count
    await casesStore.fetchCases(params as any)
    
    // Update the count from the totalCount in the store response
    yourInboxCount.value = casesStore.totalCount || 0
  } catch (err) {
    console.error('Error fetching Your Inbox count:', err)
    yourInboxCount.value = 0
  }
}

// Fetch task counts for badge display
const fetchTaskCounts = async () => {
  try {
    const userId = userStore.userData?.id || ''
    if (userId && !tasksStore.loading) {
      // Load user, team, and all tasks to get accurate counts
      await Promise.all([
        tasksStore.loadUserTasks(userId),
        tasksStore.loadTeamTasks(),
        tasksStore.loadAllTasks()
      ])
    }
  } catch (err) {
    console.error('Error fetching task counts:', err)
  }
}

// Fetch case counts for all views
const fetchCaseViewCounts = async () => {
  try {
    const views = casesStore.views || []
    const userId = userStore.userData?.id || ''
    
    if (!userId || views.length === 0) return
    
    // Use the issues API directly to avoid affecting main store state
    const { useIssuesAPI } = await import('@/composables/services')
    const issuesAPI = useIssuesAPI()
    
    // Fetch counts for each view in parallel
    const countPromises = views
      .filter(view => view.type !== 1) // Skip views with type = 1
      .map(async (view) => {
        try {
          // Create params object based on view filters
          const params: any = {
            page: 1,
            limit: 1, // We only need the count, so minimal data
            start: 0
          }
          
          if (view.filters && view.filters.length > 0) {
            params.filter = []
            for (const filterParam of view.filters) {
              params.filter.push({ 
                property: filterParam.filter_field, 
                value: filterParam.filter_compare_field, 
                operator: filterParam.filter_operator 
              })
            }
          }
          
          // Fetch cases with the filter to get the count
          const result = await issuesAPI.fetchCases(params)
          
          return { viewId: view.id, count: result.totalCount || 0 }
        } catch (err) {
          console.error(`Error fetching count for view ${view.label}:`, err)
          return { viewId: view.id, count: 0 }
        }
      })
    
    // Execute all count fetches in parallel
    const results = await Promise.all(countPromises)
    
    // Update viewCounts with the results
    results.forEach(({ viewId, count }) => {
      viewCounts.value[viewId] = count
    })
  } catch (err) {
    console.error('Error fetching case view counts:', err)
  }
}



// Watch for user data changes to refresh the count
const currentUserId = ref<string | null>(userStore.userData?.id || null)

watch(
  () => userStore.userData?.id,
  (newUserId) => {
    if (newUserId && newUserId !== currentUserId.value) {
      currentUserId.value = newUserId
      fetchYourInboxCount()
      fetchTaskCounts() // Also refresh task counts when user changes
      fetchCaseViewCounts() // Also refresh case view counts when user changes
    }
  }
)

// Add computed property to determine which component to render
const currentComponent = computed(() => {
  if (selectedView.value?.id === 'dashboard') {
    return InboxDashboard
  }
  return objectType.value === 'tasks' ? TaskList : InboxCasesList
})

// Function to expand a specific panel by ID
const expandPanelById = (panelId: string) => {
  // Set the panel as expanded in the expandedKeys
  expandedKeys.value[panelId.replace('-panel', '')] = true
  
  // Wait for DOM update and then find the panel header by data attribute
  nextTick(() => {
    const panelHeader = document.querySelector(`[data-panel-id="${panelId}"]`)
    if (panelHeader && panelHeader instanceof HTMLElement) {
      // Check if the panel is already expanded
      const isExpanded = panelHeader.getAttribute('aria-expanded') === 'true'
      if (!isExpanded) {
        panelHeader.click()
      }
    }
  })
}
</script>

<template>
  <div 
    class="inbox-views"
    :class="{ minimized: isMinimized }"
  >
    <div v-if="loadError" class="error-message">
      Failed to load views: {{ loadError }}
      <BravoButton
        label="Retry"
        size="small"
        @click="fetchCustomViews"
        class="retry-button"
      />
    </div>
    <div class="views-container">
      <div class="panel-menu-wrapper">
        <div class="panel-header">
          <div class="header-content">
            <BravoTitlePage v-if="!isMinimized">Inbox</BravoTitlePage>
            <div v-if="!isMinimized" class="header-actions">
              <BravoButton
                icon="pi pi-plus"
                severity="secondary"
                text
                @click="handleCreateCase"
                aria-label="Create new case"
              />
              <BravoButton
                icon="pi pi-search"
                severity="secondary"
                text
                @click="handleSearch"
                aria-label="Search"
              />
            </div>
          </div>
        </div>
        
        <!-- Panel content when not minimized -->
        <div v-if="!isMinimized" class="panel-content">
          <BravoPanelMenuNav
            :model="model"
            :expandedKeys="expandedKeys"
            multiple
          />
        </div>
        
        <!-- Minimized icons view -->
        <div v-if="isMinimized" class="panel-content minimized-icons">
          <!-- Top-level items (like Dashboard) -->
          <div 
            v-for="item in model.filter(item => item.type === 'item')" 
            :key="item.key" 
            class="minimized-icon" 
            :class="{ active: item.active }"
            @click="item.command"
            v-tooltip.right="{
              value: item.label,
              showDelay: 400
            }"
          >
            <i :class="item.icon || 'pi pi-folder'"></i>
          </div>
          
          <!-- Folder items -->
          <div v-for="panel in model.filter(item => item.type === 'folder')" :key="panel.key" class="minimized-panel">
            <div 
              v-for="item in panel.items" 
              :key="item.key" 
              class="minimized-icon" 
              :class="{ active: item.active }"
              @click="selectItem(item)"
              v-tooltip.right="{
                value: item.label,
                showDelay: 400
              }"
            >
              <i :class="item.icon || 'pi pi-folder'"></i>
              <BravoBadge 
                v-if="item.badge && item.badge !== '—'" 
                :value="item.badge" 
                class="icon-badge"
              />
            </div>
          </div>
        </div>
        
        <div class="panel-footer">
          <BravoButton
            v-if="!isMinimized"
            label="Manage Views"
            icon="pi pi-cog"
            severity="secondary"
            text
            @click="handleManageViews"
            aria-label="Manage Views"
          />
          <BravoButton
            :icon="isMinimized ? 'pi pi-chevron-right' : 'pi pi-chevron-left'"
            text
            severity="secondary"
            @click="handleTogglePanel"
            :aria-label="isMinimized ? 'Expand panel' : 'Collapse panel'"
            v-tooltip.top="{
              value: isMinimized ? 'Expand panel' : 'Collapse panel',
              showDelay: 400
            }"
          />
        </div>
      </div>
      <div class="view-content" v-if="selectedView?.id !== 'dashboard'">
        <component
          :is="currentComponent"
          :selectedView="selectedView"
          :isYourInbox="isYourInboxSelected"
          :initialLoading="initialLoading"
          :taskType="taskType"
          :sidebarWidth="sidebarWidth"
          @select-issue="handleSelectIssue"
          @create-case="handleCreateCase"
        />
      </div>
    </div>

    <!-- Create Case Modal -->
    <CreateCaseModal
      v-model:visible="showAddCaseDialog"
      @case-created="handleCaseCreated"
    />
  </div>
</template>

<style scoped>
.inbox-title {
  padding: 1rem;
  padding-top:.5rem;
  margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--surface-900);

}
.inbox-views {
  display: flex;
  background: var(--surface-50);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  box-sizing: border-box;
  height: 100%;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:deep(.p-panelmenu-content) {
    background: none !important;
  }
}

.views-container {
  display: flex;
  flex: 1;
  gap: 0;
  min-height: 0;
  overflow-x: hidden;
}

.panel-menu-wrapper {
  width: 280px;
  flex-shrink: 0;
  background: var(--surface-50);
  padding: 0;
  border-radius: 0;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.inbox-views.minimized .panel-menu-wrapper {
  width: 68px;
}

.panel-header {
  padding: 1rem 1.5rem;
  height: 64px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-right: 1px solid var(--border-color);
}

.inbox-views.minimized .panel-header {
  padding: 0.5rem;
  justify-content: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  overflow: hidden;
  transition: opacity 0.2s ease-in-out;
}

.inbox-views.minimized .header-content {
  justify-content: center;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: opacity 0.2s ease-in-out;
}

.panel-content {
  padding: 0.5rem 3px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  border-right: 1px solid var(--border-color);
  transition: opacity 0.2s ease-in-out;
  background: var(--surface-50);

  /* Override global scrollbar styles to match panel background */
  &::-webkit-scrollbar {
    background: var(--surface-50) !important;
  }

  &::-webkit-scrollbar-track {
    background: var(--surface-50) !important;
  }

  &::-webkit-scrollbar-thumb {
    border: 1px solid var(--surface-50) !important;
  }

  &::-webkit-scrollbar-corner {
    background: var(--surface-50) !important;
  }

  /* Firefox scrollbar override */
  scrollbar-color: var(--surface-300) var(--surface-50) !important;
}

.minimized-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  padding: 0.5rem 0;
  width: 100%;
  opacity: 0;
  animation: fadeInIcons 0.3s ease-in-out 0.2s forwards;
}

@keyframes fadeInIcons {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.minimized-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0 0.25rem;
}

.minimized-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--surface-100);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
}

.minimized-icon:hover {
  background-color: var(--surface-200);
  transform: scale(1.05);
}

.minimized-icon.active {
  background-color: var(--primary-100);
  border: 2px solid var(--primary-500);
}

.minimized-icon.active i {
  color: var(--primary-600);
}

.minimized-icon i {
  font-size: 1.2rem;
  color: var(--surface-600);
}

.icon-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 10;
}

.view-content {
  width: 320px;
  flex-shrink: 0;
  overflow-y: auto;
  overflow-x: hidden;
  background: #fff;
  margin-top: 0;
  padding-top: 0;
  border-radius: 0;
  box-sizing: border-box;
}

.error-message {
  background-color: var(--red-50);
  color: var(--red-600);
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.retry-button {
  align-self: flex-end;
}

.panel-footer {
  padding: 0.5rem;
  display: flex;
  justify-content: space-between;
  border-right: 1px solid var(--border-color);
  position: sticky;
  bottom: 0;
  background: var(--surface-50);
  z-index: 10;
}

.inbox-views.minimized .panel-footer {
  justify-content: center;
  padding: 0.5rem 0;
}


</style> 