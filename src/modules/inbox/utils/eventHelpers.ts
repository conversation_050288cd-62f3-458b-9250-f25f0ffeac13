/**
 * Helper functions for handling interaction events and activity
 */

import type { Task } from '../../../types/task'

/**
 * Get appropriate icon for event type
 * @param eventType - The event type identifier
 * @returns PrimeVue icon class string
 */
export function getEventIcon(eventType?: string): string {
  const iconMap: Record<string, string> = {
    'note-added': 'pi pi-file-edit',
    'case-update': 'pi pi-refresh',
    'task-action': 'pi pi-check-circle',
    'survey': 'pi pi-chart-bar',
    'status-update': 'pi pi-info-circle',
    'fields-updated': 'pi pi-pencil',
    'case-created': 'pi pi-plus-circle',
    'task-completed': 'pi pi-check',
    'survey-sent': 'pi pi-send',
    'reopened': 'pi pi-replay',
    'file-added': 'pi pi-file-plus',
    'ready': 'pi pi-check-circle',
    'waiting': 'pi pi-clock',
    'reassigned': 'pi pi-user-edit',
    'resolved': 'pi pi-check-circle',
    'assigned-to-team': 'pi pi-users',
    'case-escalated': 'pi pi-exclamation-triangle',
    'task-added': 'pi pi-plus',
    'task-edited': 'pi pi-pencil',
    'job-completed': 'pi pi-check-circle',
    'form-completed': 'pi pi-list',
    'case-de-escalated': 'pi pi-exclamation-triangle',
    'customer-changed': 'pi pi-id-card',
    'cases-merged': 'pi pi-arrow-down-left-and-arrow-up-right-to-center',
    'case-deleted': 'pi pi-trash',
    
  }
  
  return iconMap[eventType || ''] || 'pi pi-circle-fill'
}

/**
 * Convert UTC timestamp to local timezone
 * @param utcTimestamp - UTC timestamp string
 * @returns Formatted local date/time string
 */
export function formatUtcTimestamp(utcTimestamp: string): string {
  // Add 'Z' to explicitly mark as UTC if not already present
  const utcString = utcTimestamp.endsWith('Z') ? utcTimestamp : utcTimestamp + 'Z'
  const date = new Date(utcString)
  return date.toLocaleString()
}

/**
 * Get team ID from task links
 * @param task - Task object
 * @returns Team ID or null if not found
 */
export function getTaskTeam(task: Task): string | null {
  const teamLink = task.links?.find(link => link.rel === 'team')
  return teamLink?.id || null
}

/**
 * Get user ID from task links
 * @param task - Task object
 * @returns User ID or null if not found
 */
export function getTaskUser(task: Task): string | null {
  const userLink = task.links?.find(link => link.rel === 'user')
  return userLink?.id || null
}

/**
 * Format task due date
 * @param dueDate - Due date string
 * @returns Formatted due date string or 'No due date' if empty
 */
export function formatTaskDueDate(dueDate: string): string {
  if (!dueDate) return 'No due date'
  return formatUtcTimestamp(dueDate)
} 