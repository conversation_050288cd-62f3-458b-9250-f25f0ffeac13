/**
 * Helper functions for task-related operations
 */

import type { Task } from '../../../types/task'
import { getTaskTeam, getTaskUser } from './eventHelpers'

/**
 * Get case ID from task links
 * @param task - Task object
 * @returns Case ID or null if not found
 */
export function getTaskCase(task: Task): string | null {
  const caseLink = task.links?.find(link => link.rel === 'case')
  return caseLink?.id || null
}

/**
 * Get team display name from task
 * @param task - Task object
 * @param partnerTeams - Array of partner teams from store
 * @returns Team display name or fallback
 */
export function getTaskTeamName(task: Task, partnerTeams: any[]): string {
  const teamId = getTaskTeam(task)
  if (!teamId) return '—'
  
  const team = partnerTeams.find(t => t.val === teamId)
  return team?.lbl || teamId
}

/**
 * Get user display name from task
 * @param task - Task object
 * @param partnerMetaData - Partner metadata from store
 * @returns User display name or fallback
 */
export function getTaskUserName(task: Task, partnerMetaData: any): string {
  const userId = getTaskUser(task)
  if (!userId) return '—'
  
  const user = partnerMetaData?.pl__partners_users?.find(
    (u: any) => u.id === userId
  )
  return user?.lbl || userId
}

/**
 * Get formatted case reference ID from task
 * @param task - Task object
 * @returns Formatted case reference ID (last 6 characters, uppercase) or '—'
 */
export function getTaskCaseReference(task: Task): string {
  const caseId = getTaskCase(task)
  return caseId ? caseId.slice(-6).toUpperCase() : '—'
}

/**
 * Get case route path for navigation
 * @param task - Task object
 * @returns Case route path or null if no case ID
 */
export function getTaskCaseRoute(task: Task): string | null {
  const caseId = getTaskCase(task)
  return caseId ? `/inbox/cases/${caseId}` : null
}

/**
 * Get team and user IDs from task for easier access
 * @param task - Task object
 * @returns Object with teamId and userId
 */
export function getTaskAssignments(task: Task): { teamId: string | null; userId: string | null } {
  return {
    teamId: getTaskTeam(task),
    userId: getTaskUser(task)
  }
}

/**
 * Get task status severity for BravoTag component
 * @param task - Task object
 * @returns Severity level for BravoTag styling
 */
export function getTaskStatusSeverity(task: Task): 'success' | 'info' | 'warn' | 'danger' | undefined {
  if (!task?.status) return undefined
  const status = task.status.toLowerCase()
  if (status === 'completed') return 'success'
  if (status === 'in progress' || status === 'active') return 'info'
  if (status === 'not started') return 'warn'
  if (status === 'waiting') return 'warn'
  if (status === 'closed') return 'info'
  return 'info'
} 