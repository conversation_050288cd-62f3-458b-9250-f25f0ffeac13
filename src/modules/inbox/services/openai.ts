import type { Message } from '../types';

class OpenAIService {
  private static instance: OpenAIService | null = null;
  private isInitialized: boolean = false;

  private constructor() {
    // Simulate initialization
    this.isInitialized = true;
    console.log('🤖 OpenAI Service initialized (fake mode)');
  }

  static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService();
    }
    return OpenAIService.instance;
  }

  private async simulateApiCall(delay: number = 1000): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  async summarizeConversation(messages: Message[]): Promise<string> {
    console.log('🤖 Fake OpenAI: Summarizing conversation with', messages.length, 'messages');
    
    await this.simulateApiCall(1500);

    // Generate a fake summary based on message count and content
    const messageCount = messages.length;
    const hasFiles = messages.some(msg => msg.type === 'file');
    const recentMessages = messages.slice(-3);
    
    const fakeSummaries = [
      `Customer inquiry regarding account issues. ${messageCount} messages exchanged. ${hasFiles ? 'File attachments included.' : 'Text-based conversation.'} Resolution pending.`,
      `Support conversation about technical difficulties. Customer provided details across ${messageCount} messages. ${hasFiles ? 'Supporting documents shared.' : 'Verbal description provided.'} Investigation in progress.`,
      `Service request discussion. ${messageCount} message thread covering customer concerns. ${hasFiles ? 'Relevant files attached for review.' : 'Detailed text communication.'} Follow-up required.`,
      `Customer communication regarding billing inquiry. ${messageCount} messages in conversation. ${hasFiles ? 'Documentation provided by customer.' : 'Information shared via text.'} Review needed.`
    ];

    const randomSummary = fakeSummaries[Math.floor(Math.random() * fakeSummaries.length)];
    console.log('🤖 Fake OpenAI: Generated summary:', randomSummary);
    
    return randomSummary;
  }

  async generateReply(messages: Message[]): Promise<string> {
    console.log('🤖 Fake OpenAI: Generating reply for conversation with', messages.length, 'messages');
    
    await this.simulateApiCall(2000);

    const lastMessage = messages[messages.length - 1];
    const isQuestion = lastMessage?.content.includes('?');
    const hasUrgentKeywords = lastMessage?.content.toLowerCase().includes('urgent') || 
                             lastMessage?.content.toLowerCase().includes('asap') ||
                             lastMessage?.content.toLowerCase().includes('emergency');

    const fakeReplies = [
      "Thank you for reaching out to us. I understand your concern and I'm here to help resolve this issue for you. Let me review the details you've provided and get back to you with a solution as soon as possible.",
      
      "I appreciate you taking the time to contact us about this matter. Based on the information you've shared, I can see why this would be frustrating. Let me investigate this further and provide you with an update within the next 24 hours.",
      
      "Thank you for your patience while we work on resolving this issue. I've reviewed your case and I'm escalating this to our specialized team who can provide the most appropriate assistance. You should expect to hear back from us shortly.",
      
      "I understand the importance of getting this resolved quickly for you. Thank you for providing the additional details - this will help us address your concern more effectively. I'm working on this now and will update you as soon as I have more information."
    ];

    let selectedReply = fakeReplies[Math.floor(Math.random() * fakeReplies.length)];

    // Modify reply based on context
    if (hasUrgentKeywords) {
      selectedReply = "I understand this is urgent for you. " + selectedReply + " I'm prioritizing your case and will ensure you receive a prompt response.";
    }

    if (isQuestion) {
      selectedReply = selectedReply + " To answer your specific question, I'll need to gather some additional information and will provide you with a comprehensive response shortly.";
    }

    console.log('🤖 Fake OpenAI: Generated reply:', selectedReply);
    return selectedReply;
  }

  async reformatResponse(content: string): Promise<string> {
    console.log('🤖 Fake OpenAI: Reformatting response:', content.substring(0, 50) + '...');
    
    await this.simulateApiCall(1200);

    // Simulate expanding and improving the content
    const words = content.split(' ');
    const isShort = words.length < 10;
    
    if (isShort) {
      // Expand short responses
      const expandedContent = `Thank you for your inquiry. ${content} 

I want to ensure that we address your concerns thoroughly and provide you with the best possible service. Our team is committed to resolving this matter efficiently while maintaining the highest standards of customer care.

Please don't hesitate to reach out if you have any additional questions or if there's anything else I can assist you with. We value your business and appreciate your patience as we work together to find the best solution for your needs.`;
      
      console.log('🤖 Fake OpenAI: Expanded short response');
      return expandedContent;
    } else {
      // Improve longer responses
      const improvedContent = content
        .replace(/\b(ok|okay)\b/gi, 'certainly')
        .replace(/\b(thanks|thx)\b/gi, 'thank you')
        .replace(/\b(asap)\b/gi, 'as soon as possible')
        .replace(/\b(info)\b/gi, 'information');
      
      const professionalizedContent = `${improvedContent}

I hope this information is helpful to you. If you need any clarification or have additional questions, please feel free to contact me directly. I'm here to ensure that your experience with our service exceeds your expectations.

Thank you for choosing our services, and I look forward to assisting you further.`;
      
      console.log('🤖 Fake OpenAI: Professionalized response');
      return professionalizedContent;
    }
  }

  // Utility method to check if service is available
  isAvailable(): boolean {
    return this.isInitialized;
  }

  // Method to simulate different response types for testing
  async generateTestResponse(type: 'summary' | 'reply' | 'reformat', input: string | Message[]): Promise<string> {
    console.log(`🤖 Fake OpenAI: Generating test ${type} response`);
    
    switch (type) {
      case 'summary':
        return this.summarizeConversation(input as Message[]);
      case 'reply':
        return this.generateReply(input as Message[]);
      case 'reformat':
        return this.reformatResponse(input as string);
      default:
        return 'Test response generated successfully.';
    }
  }
}

export const openAiService = OpenAIService.getInstance(); 