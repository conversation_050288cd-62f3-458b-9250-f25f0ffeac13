// import OpenAI from 'openai';
import type { Message } from '../types';

class OpenAIService {
  private client: any | null = null;
  private static instance: OpenAIService | null = null;

  private constructor() {
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    if (!apiKey) {
      console.warn('OpenAI API key not found in environment variables');
      return;
    }

    this.client = {};
  }

  static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService();
    }
    return OpenAIService.instance;
  }

  private async ensureClient() {
    if (!this.client) {
      throw new Error('OpenAI client not initialized. Please check your API key.');
    }
  }

  async summarizeConversation(messages: Message[]): Promise<string> {
    await this.ensureClient();

    const formattedMessages = messages.map(msg => {
      let content = msg.content;
      try {
        const parsed = JSON.parse(content);
        content = parsed.email?.body || parsed.message || content;
      } catch {
        // Use content as is if it's not JSO<PERSON>
      }
      return `${msg.senderId}: ${content}`;
    }).join('\n\n');

    const response = await this.client!.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a helpful assistant that summarizes email conversations. Create concise, clear summaries that capture the key points and any action items."
        },
        {
          role: "user",
          content: `Please summarize this conversation:\n\n${formattedMessages}`
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    });

    return response.choices[0]?.message?.content || 'Unable to generate summary';
  }

  async generateReply(messages: Message[]): Promise<string> {
    await this.ensureClient();

    const formattedMessages = messages.map(msg => {
      let content = msg.content;
      try {
        const parsed = JSON.parse(content);
        content = parsed.email?.body || parsed.message || content;
      } catch {
        // Use content as is if it's not JSON
      }
      return `${msg.senderId}: ${content}`;
    }).join('\n\n');

    const response = await this.client!.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a helpful customer service representative. Generate professional, helpful, and empathetic responses."
        },
        {
          role: "user",
          content: `Based on this conversation, generate an appropriate reply:\n\n${formattedMessages}`
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    });

    return response.choices[0]?.message?.content || 'Unable to generate reply';
  }

  async reformatResponse(content: string): Promise<string> {
    await this.ensureClient();

    const response = await this.client!.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a professional editor. Expand and improve the given text by adding more detail, context, and professional language. Make the response longer with multiple well-structured paragraphs. Ensure the tone is friendly and professional while maintaining the original meaning. The response should be at least 3-4 sentences long with proper transitions between ideas."
        },
        {
          role: "user",
          content: `Please expand and improve this response with more detail and professional language:\n\n${content}`
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });

    return response.choices[0]?.message?.content || 'Unable to reformat response';
  }
}

export const openAiService = OpenAIService.getInstance();