import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { AppConfig, Communication, CommunicationType, Message, AvailableComm, Participant, TypingUser } from '../types';
import { xmppService as xmppServiceInstance } from '../services/xmpp';
import { useCommsAPI } from '@/composables/services/useCommsAPI';

export type UseCommunicationsStore = ReturnType<typeof useCommunicationsStore>;

export const useCommunicationsStore = defineStore('casesComms', () => {
  const activeComms = ref<Communication[]>([]);
  const selectedCommId = ref<string | null>(null);
  const currentlyViewedCommId = ref<string | null>(null);
  const draggedCommId = ref<string | null>(null);
  const appConfig = ref<AppConfig | null>(null);
  const currentUserId = ref<string>('users:3ET;ctofhv'); // Hardcoded for now
  const isXmppInitialized = ref(false);
  const initializationPromise = ref<Promise<void> | null>(null);
  const isLoading = ref(true);

  // Typing indicators
  const userTyping = ref<TypingUser | null>(null);
  const botTyping = ref<TypingUser | null>(null);
  const clearTypingTimeout = ref<NodeJS.Timeout | null>(null);

  // Audio for message notifications
  const messageAudio = ref<HTMLAudioElement | null>(null);

  // Initialize audio
  if (typeof window !== 'undefined') {
    messageAudio.value = new Audio('/message.mp3');
    messageAudio.value.preload = 'auto';
    messageAudio.value.volume = 0.7; // Set a reasonable volume
    
    // Add error handling for audio loading
    messageAudio.value.addEventListener('error', (e) => {
      console.error('🔊 Audio loading error:', e);
    });
    
    messageAudio.value.addEventListener('canplaythrough', () => {
      console.log('🔊 Audio loaded and ready to play');
    });
    
    // Try to load the audio file
    messageAudio.value.load();
  }

  const selectedComm = computed(() => 
    activeComms.value.find(comm => comm.id === selectedCommId.value)
  );

  const totalUnreadCount = computed(() => {
    return activeComms.value.reduce((total, comm) => {
      return total + (comm.unreadCount || 0);
    }, 0);
  });

  function getUserBaseId(id: string): string {
    return id.split(';')[0];
  }

  const isCurrentUser = (senderId: string) => {
    return getUserBaseId(senderId) === getUserBaseId(currentUserId.value);
  };

  function playMessageSound() {
    try {
      console.log('🔊 Attempting to play message sound:', messageAudio.value);
      if (!messageAudio.value) {
        console.warn('🔊 No audio element available');
        return;
      }
      
      // Reset the audio to the beginning in case it was already played
      messageAudio.value.currentTime = 0;
      
      messageAudio.value.play().catch(error => {
        console.warn('🔊 Could not play message sound:', error);
        // Try to handle autoplay policy issues
        if (error.name === 'NotAllowedError') {
          console.warn('🔊 Audio autoplay blocked by browser. User interaction required.');
        }
      });
    } catch (error) {
      console.warn('🔊 Could not play message sound:', error);
    }
  }

  // Test function to manually trigger audio (for debugging)
  function testMessageSound() {
    console.log('🔊 Testing message sound manually');
    playMessageSound();
  }

  function incrementUnreadCount(commId: string) {
    const comm = activeComms.value.find(c => c.id === commId);
  
    if (comm && comm.id !== currentlyViewedCommId.value) {
      comm.unreadCount = (comm.unreadCount || 0) + 1;
      console.log('📈 Communications Store: Incremented unread count for comm:', commId, 'new count:', comm.unreadCount);
    } else {
      console.log('📈 Communications Store: Not incrementing unread count for comm:', commId, 'reason:', comm ? 'comm is currently being viewed' : 'comm not found');
    }
  }

  function clearUnreadCount(commId: string) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (comm) {
      comm.unreadCount = 0;
    }
  }

  function setUserTyping(user: TypingUser) {
    userTyping.value = user;
    
    // Clear any existing timeout
    if (clearTypingTimeout.value) {
      clearTimeout(clearTypingTimeout.value);
    }
    
    // Set timeout to clear typing indicator after 10 seconds
    clearTypingTimeout.value = setTimeout(() => {
      clearUserTyping();
    }, 10000);
  }

  function setBotTyping(user: TypingUser) {
    botTyping.value = user;
  }

  function clearUserTyping() {
    userTyping.value = null;
    if (clearTypingTimeout.value) {
      clearTimeout(clearTypingTimeout.value);
      clearTypingTimeout.value = null;
    }
  }

  function clearBotTyping() {
    botTyping.value = null;
  }

  function setCustomerTyping(commId: string, isTyping: boolean) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (comm) {
      comm.customerTyping = isTyping;
    }
  }

  function setBotTypingForComm(commId: string, isTyping: boolean) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (comm) {
      comm.botTyping = isTyping;
    }
  }

  async function initialize(config: AppConfig) {
    if (initializationPromise.value) {
      return initializationPromise.value;
    }

    initializationPromise.value = (async () => {
      try {
        appConfig.value = config;
        currentUserId.value = config.serviceConfig.resource;
        
        await xmppServiceInstance.initialize(config);
        isXmppInitialized.value = true;
        isLoading.value = false;
      } catch (error) {
        console.error('Failed to initialize XMPP:', error);
        isLoading.value = false;
        throw error;
      } finally {
        initializationPromise.value = null;
      }
    })();

    return initializationPromise.value;
  }

  function findCommByRoomJid(roomJid: string) {
    return activeComms.value.find(comm => comm.roomJid === roomJid);
  }

  async function openExistingComm(availableComm: AvailableComm) {
    console.log('XMPP: openExistingComm', availableComm)
    // Check if we already have this communication open
    const existingComm = activeComms.value.find(comm => comm.id === availableComm.id);
    if (existingComm) {
      selectedCommId.value = existingComm.id;
      // Clear unread count when selecting
      clearUnreadCount(existingComm.id);
      return;
    }

    // Map comm_type to our CommunicationType
    const typeMap: Record<number, CommunicationType> = {
      0: 'chat',
      1: 'email',
      2: 'sms',
      3: 'voice'
    };

    let commType = typeMap[availableComm.comm_type] || 'chat';

    // For comm_type 0 (chat), check the title for additional clues
    if (availableComm.comm_type === 0 && availableComm.title) {
      const titleLower = availableComm.title.toLowerCase();
      
      if (titleLower.includes('email')) {
        commType = 'email';
      } else if (titleLower.includes('sms')) {
        commType = 'sms';
      } else if (titleLower.includes('voice')) {
        commType = 'voice';
      }
      // Otherwise keep it as 'chat'
    }

    // Create participants from the API response with proper typing
    const participants: Participant[] = availableComm.participants?.map(p => ({
      id: p.id,
      name: p.name,
      alias: p.alias,
      type: p.object === 'relay' ? 'internal' : 'external' as const,
      external_id: p.external_id,
      presence: p.presence
    })) || [];

    // Add the current user as a participant if not already present
    if (!participants.find(p => p.id === currentUserId.value)) {
      participants.push({
        id: currentUserId.value,
        name: 'Support Agent',
        type: 'internal',
        presence: true,
        avatar: appConfig.value?.serviceConfig.relay_avatar
      });
    }
    const newComm: Communication = {
      id: availableComm.id,
      type: commType,
      title: availableComm.title || `New ${commType.charAt(0).toUpperCase() + commType.slice(1)}`,
      participants,
      messages: [],
      availableComm,
      roomJid: availableComm.external_id,
      unreadCount: availableComm.unread_count || 0
    };

    activeComms.value.push(newComm);
    selectedCommId.value = newComm.id;
    // Clear unread count when selecting
    clearUnreadCount(newComm.id);

    // Join the XMPP room to get historical messages
    await xmppServiceInstance.joinRoom(newComm);
  }

  async function openExistingCommWithoutSelection(availableComm: AvailableComm) {
    console.log('XMPP: openExistingCommWithoutSelection', availableComm)
    // Check if we already have this communication open
    const existingComm = activeComms.value.find(comm => comm.id === availableComm.id);
    if (existingComm) {
      // Don't auto-select, just return the existing comm
      return existingComm;
    }

    // Map comm_type to our CommunicationType
    const typeMap: Record<number, CommunicationType> = {
      0: 'chat',
      1: 'email',
      2: 'sms',
      3: 'voice'
    };

    let commType = typeMap[availableComm.comm_type] || 'chat';

    // For comm_type 0 (chat), check the title for additional clues
    if (availableComm.comm_type === 0 && availableComm.title) {
      const titleLower = availableComm.title.toLowerCase();
      
      if (titleLower.includes('email')) {
        commType = 'email';
      } else if (titleLower.includes('sms')) {
        commType = 'sms';
      } else if (titleLower.includes('voice')) {
        commType = 'voice';
      }
      // Otherwise keep it as 'chat'
    }

    // Create participants from the API response with proper typing
    const participants: Participant[] = availableComm.participants?.map(p => ({
      id: p.id,
      name: p.name,
      alias: p.alias,
      type: p.object === 'relay' ? 'internal' : 'external' as const,
      external_id: p.external_id,
      presence: p.presence
    })) || [];

    // Add the current user as a participant if not already present
    if (!participants.find(p => p.id === currentUserId.value)) {
      participants.push({
        id: currentUserId.value,
        name: 'Support Agent',
        type: 'internal',
        presence: true,
        avatar: appConfig.value?.serviceConfig.relay_avatar
      });
    }
    const newComm: Communication = {
      id: availableComm.id,
      type: commType,
      title: availableComm.title || `New ${commType.charAt(0).toUpperCase() + commType.slice(1)}`,
      participants,
      messages: [],
      availableComm,
      roomJid: availableComm.external_id,
      unreadCount: availableComm.unread_count || 0
    };

    activeComms.value.push(newComm);
    // Don't auto-select: selectedCommId.value = newComm.id;

    // Join the XMPP room to get historical messages
    await xmppServiceInstance.joinRoom(newComm);
    
    return newComm;
  }

  async function addNewCommunication(type: CommunicationType) {
    if (!appConfig.value) {
      throw new Error('App config not initialized');
    }

    // Use different room JIDs based on communication type
    const roomJid = type === 'email' 
      ? '<EMAIL>'
      : '<EMAIL>';
    // TODO: get the roomJid from the API
    
    const newComm: Communication = {
      id: crypto.randomUUID(),
      type,
      title: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      participants: [{
        id: appConfig.value.serviceConfig.resource,
        name: 'Support Agent',
        type: 'internal',
        presence: true,
        avatar: appConfig.value.serviceConfig.relay_avatar
      }],
      messages: [], // Initialize with empty array
      roomJid,
      unreadCount: 0
    };

    activeComms.value.push(newComm);
    selectedCommId.value = newComm.id;
    // Clear unread count when selecting
    clearUnreadCount(newComm.id);
    await xmppServiceInstance.joinRoom(newComm);
  }

  async function closeComm(id: string) {
    const comm = activeComms.value.find(c => c.id === id);
    if (comm && comm.roomJid) {
      await xmppServiceInstance.exitRoom(comm.roomJid);
    }

    const index = activeComms.value.findIndex(comm => comm.id === id);
    if (index !== -1) {
      activeComms.value.splice(index, 1);
      if (selectedCommId.value === id) {
        selectedCommId.value = activeComms.value[activeComms.value.length - 1]?.id || null;
      }
    }
  }

  async function selectComm(id: string) {
    const previousId = selectedCommId.value;
    
    // Don't do anything if selecting the same comm
    if (previousId === id) return;
    
    // Note: We no longer exit rooms when switching between communications
    // This allows us to receive messages for all communications simultaneously
    // which is necessary for proper unread count functionality
    
    selectedCommId.value = id;
    
    // Note: Unread count clearing is now handled by setCurrentlyViewedComm()
    // clearUnreadCount(id); // Removed to avoid conflicts with currentlyViewedCommId logic
    
    // Join the new room if XMPP is initialized and we're not already in it
    if (isXmppInitialized.value) {
      const comm = activeComms.value.find(c => c.id === id);
      if (comm?.roomJid && !xmppServiceInstance.isRoomConnected(comm.roomJid)) {
        await xmppServiceInstance.joinRoom(comm);
      }
    }
  }

  function reorderComms(fromIndex: number, toIndex: number) {
    const comms = [...activeComms.value];
    const [removed] = comms.splice(fromIndex, 1);
    comms.splice(toIndex, 0, removed);
    activeComms.value = comms;
  }

  function setDraggedComm(id: string | null) {
    draggedCommId.value = id;
  }

  function renameComm(id: string, newTitle: string) {
    const comm = activeComms.value.find(c => c.id === id);
    if (comm) {
      comm.title = newTitle;
    }
  }

  async function sendMessage(commId: string, content: string) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (!comm) return;

    // Send message through XMPP
    await xmppServiceInstance.sendMessage(comm, content);
  }

  function setCurrentlyViewedComm(commId: string | null) {
    const previousCommId = currentlyViewedCommId.value;
    currentlyViewedCommId.value = commId;
    
    console.log('👁️ Communications Store: Currently viewed comm changed:', {
      from: previousCommId,
      to: commId,
      willClearUnreadCount: !!commId
    });
    
    // If we're viewing a communication, clear its unread count
    if (commId) {
      clearUnreadCount(commId);
    }
  }

  function addMessage(commId: string, message: Message) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (!comm) return;

    // Add the message directly since duplicates should now be prevented at the source
    comm.messages.push(message);

    console.log('📨 Adding message - DETAILED DEBUG:', {
      messageCommId: commId,
      messageId: message.id,
      isMine: message.isMine,
      selectedCommId: selectedCommId.value,
      currentlyViewedCommId: currentlyViewedCommId.value,
      shouldIncrementUnread: commId !== currentlyViewedCommId.value && !message.isMine,
      commTitle: comm.title,
      currentUnreadCount: comm.unreadCount || 0,
      totalMessagesInComm: comm.messages.length
    });

    // Play sound and increment unread count if this comm is not currently being viewed
    if (commId !== currentlyViewedCommId.value && !message.isMine) {
      console.log('🔊 Playing message sound for new message - comm not currently viewed');
      playMessageSound();
      incrementUnreadCount(commId);
    } else {
      console.log('🔇 Not playing sound:', {
        reason: commId === currentlyViewedCommId.value ? 'comm is currently being viewed' : 'message is from current user'
      });
    }

    // Clear typing indicators if this message is from the typing user
    if (userTyping.value && message.senderId === userTyping.value.name) {
      clearUserTyping();
    }
    if (botTyping.value && message.senderId === botTyping.value.name) {
      clearBotTyping();
    }
  }

  function sendSystemMessage(commId: string, content: string) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (!comm) return;

    const newMessage: Message = {
      id: crypto.randomUUID(),
      senderId: 'system',
      content,
      timestamp: new Date(),
      type: 'system'
    };

    comm.messages.push(newMessage);
  }

  function getDefaultComm() {
    // loop thorugh all comms and print them out 
    console.log('XMPP: getDefaultComm: activeComms', activeComms.value)
    const objectSource = activeComms.value[0].availableComm?.object_source;
    console.log('XMPP: getDefaultComm: objectSource', objectSource)
    let publicComm: Communication | null = null;

    if (objectSource === 'web_connect') {
      activeComms.value.forEach(comm => {
        publicComm = activeComms.value.find(comm => comm.availableComm?.object_scope === 'public') || null;
      })
    } else if (objectSource === 'email') {
      activeComms.value.forEach(comm => {
        publicComm = activeComms.value.find(comm => comm.availableComm?.object_scope === 'email') || null;
      })
    }

    return publicComm;
  }

  function updateCommTitle(commId: string, newTitle: string) {
    const comm = activeComms.value.find(c => c.id === commId);
    if (comm) {
      comm.title = newTitle;
    }
  }

  async function changeCommName(commId: string, newLabel: string) {
    try {
      const commsAPI = useCommsAPI();
      
      // Call the API to update the comm label
      const updatedComm = await commsAPI.updateCommLabel({
        id: commId,
        comm_label: newLabel
      });
      
      console.log('✅ Communications Store: Successfully updated comm label:', updatedComm);
      
      // Update the local communication data
      const comm = activeComms.value.find(c => c.id === commId);
      if (comm) {
        // Update the title to reflect the new label
        comm.title = newLabel;
        
        // Update the availableComm data if it exists
        if (comm.availableComm) {
          comm.availableComm.comm_label = newLabel;
          comm.availableComm.title = updatedComm.title; // Use the title from the API response
        }
        
        console.log('✅ Communications Store: Updated local comm data for:', commId);
      }
      
      return updatedComm;
    } catch (error) {
      console.error('❌ Communications Store: Failed to change comm name:', error);
      throw error;
    }
  }

  return {
    activeComms,
    selectedComm,
    currentUserId,
    appConfig,
    isCurrentUser,
    isXmppInitialized,
    isLoading,
    totalUnreadCount,
    userTyping,
    botTyping,
    currentlyViewedCommId,
    initialize,
    openExistingComm,
    openExistingCommWithoutSelection,
    addNewCommunication,
    closeComm,
    selectComm,
    renameComm,
    sendMessage,
    addMessage,
    sendSystemMessage,
    reorderComms,
    setDraggedComm,
    draggedCommId,
    findCommByRoomJid,
    getDefaultComm,
    updateCommTitle,
    changeCommName,
    incrementUnreadCount,
    clearUnreadCount,
    setUserTyping,
    setBotTyping,
    clearUserTyping,
    clearBotTyping,
    setCustomerTyping,
    setBotTypingForComm,
    playMessageSound,
    testMessageSound,
    setCurrentlyViewedComm,
  };
});