<script setup lang="ts">
import type { CommunicationType } from '../types';

const props = defineProps<{
  type: CommunicationType;
}>();

const getIcon = () => {
  const icons = {
    email: 'pi-envelope',
    chat: 'pi-comments',
    sms: 'pi-mobile',
    voice: 'pi-phone'
  };
  return `pi ${icons[props.type]}`;
};

const getMessage = () => {
  const messages = {
    email: 'Start a new email thread by choosing recipient(s) and creating a message.',
    chat: 'Start a new chat conversation by sending your first message.',
    sms: 'Start a new SMS thread by choosing a recipient and creating a message',
    voice: 'Start a new call by entering a phone number and clicking the call button.'
  };
  return messages[props.type];
};

const getTitle = () => {
  const titles = {
    email: 'Send New Email',
    chat: 'Start New Chat',
    sms: 'Send New SMS',
    voice: 'Start New Call'
  };
  return titles[props.type];
};
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center">
    <div class="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center mb-6">
      <i :class="[getIcon(), 'text-blue-500 text-2xl']"></i>
    </div>
    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ getTitle() }}</h3>
    <p class="text-gray-600 max-w-sm">{{ getMessage() }}</p>
  </div>
</template>