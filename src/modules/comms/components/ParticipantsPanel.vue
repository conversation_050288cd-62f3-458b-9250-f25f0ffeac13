<script setup lang="ts">
import { computed } from 'vue'
import type { Communication } from '../types'
import Avatar from './Avatar.vue'
import { useCommunicationsStore } from '../stores/communications'

interface Props {
  communication?: Communication
}

const props = defineProps<Props>()
const store = useCommunicationsStore()

const participants = computed(() => {
  if (!props.communication) return []
  
  return props.communication.participants
    .map(participant => ({
      ...participant,
      isCurrentUser: store.isCurrentUser(participant.id),
      isOnline: participant.presence || false
    }))
    .sort((a, b) => {
      // Sort by: 1) Current user first, 2) Online status, 3) Name
      if (a.isCurrentUser && !b.isCurrentUser) return -1
      if (!a.isCurrentUser && b.isCurrentUser) return 1
      if (a.isOnline && !b.isOnline) return -1
      if (!a.isOnline && b.isOnline) return 1
      return a.name.localeCompare(b.name)
    })
})

const participantCount = computed(() => participants.value.length)

const onlineCount = computed(() => 
  participants.value.filter(p => p.isOnline).length
)

const getParticipantStatus = (participant: any) => {
  if (participant.isCurrentUser) return 'You'
  return participant.isOnline ? 'Online' : 'Offline'
}

const getStatusColor = (participant: any) => {
  if (participant.isCurrentUser) return 'text-blue-600'
  return participant.isOnline ? 'text-green-600' : 'text-gray-400'
}

const getStatusDot = (participant: any) => {
  if (participant.isCurrentUser) return 'bg-blue-500'
  return participant.isOnline ? 'bg-green-500' : 'bg-gray-400'
}
</script>

<template>
  <div class="flex flex-col h-full bg-white border-l border-gray-200">
    <!-- Header -->
    <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Participants</h3>
        <div class="text-sm text-gray-500">
          {{ participantCount }} total
        </div>
      </div>
      <div class="text-xs text-gray-500 mt-1">
        {{ onlineCount }} online
      </div>
    </div>

    <!-- Empty state -->
    <div 
      v-if="!communication"
      class="flex-1 flex items-center justify-center text-gray-500"
    >
      <div class="text-center">
        <i class="pi pi-users text-4xl mb-4 text-gray-300"></i>
        <p>Select a conversation to view participants</p>
      </div>
    </div>

    <!-- Participants list -->
    <div 
      v-else
      class="flex-1 overflow-y-auto"
    >
      <div class="p-4 space-y-3">
        <div
          v-for="participant in participants"
          :key="participant.id"
          class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <!-- Avatar -->
          <div class="relative">
            <Avatar
              :src="participant.avatar"
              :name="participant.name"
              size="md"
            />
            <!-- Status indicator -->
            <div 
              class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white"
              :class="getStatusDot(participant)"
            ></div>
          </div>

          <!-- Participant info -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2">
              <span class="font-medium text-gray-900 truncate">
                {{ participant.name }}
              </span>
              <span 
                class="text-xs font-medium"
                :class="getStatusColor(participant)"
              >
                {{ getParticipantStatus(participant) }}
              </span>
            </div>
            
            <!-- Email or ID -->
            <div class="text-sm text-gray-500 truncate">
              {{ participant.email || participant.id }}
            </div>
          </div>

          <!-- Actions menu (optional) -->
          <div class="flex-shrink-0">
            <button 
              class="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="More options"
            >
              <i class="pi pi-ellipsis-v text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer with communication info -->
    <div 
      v-if="communication"
      class="px-4 py-3 border-t border-gray-200 bg-gray-50"
    >
      <div class="text-xs text-gray-500">
        <div class="flex items-center justify-between">
          <span>Communication ID:</span>
          <span class="font-mono">{{ communication.id }}</span>
        </div>
        <div class="flex items-center justify-between mt-1">
          <span>Type:</span>
          <span class="capitalize">{{ communication.type }}</span>
        </div>
        <div 
          v-if="communication.roomJid"
          class="flex items-center justify-between mt-1"
        >
          <span>Room:</span>
          <span class="font-mono text-xs truncate">{{ communication.roomJid }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for participants list */
:deep(.overflow-y-auto) {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

:deep(.overflow-y-auto::-webkit-scrollbar) {
  width: 6px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb) {
  background-color: #cbd5e1;
  border-radius: 3px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
  background-color: #94a3b8;
}
</style> 