<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue';
import { useCommunicationsStore } from '../stores/communications';
import { useUserStore } from '@/stores/user';
import { useCommsAPI } from '@/composables/services/useCommsAPI';
import type { Issue } from '@/composables/services/useIssuesAPI';
import type { AppConfig as CommsAppConfig } from '@/modules/comms/types';
import type { AvailableComm } from '@/modules/comms/types';
import EmailChannel from './channels/EmailChannel.vue';
import ChatChannel from './channels/ChatChannel.vue';
import SmsChannel from './channels/SmsChannel.vue';
import VoiceChannel from './channels/VoiceChannel.vue';

interface Props {
  case: Issue | null;
}

const props = defineProps<Props>();

const store = useCommunicationsStore();
const userStore = useUserStore();
const commsAPI = useCommsAPI();

const isInitializing = ref(false);
const error = ref<string | null>(null);
const selectedTabIndex = ref(0);

// Computed property to get available communications from the case
const availableComms = computed(() => props.case?.availableComm || []);

// Computed property to get the currently selected communication
const selectedComm = computed(() => {
  if (!availableComms.value.length) return null;
  return availableComms.value[selectedTabIndex.value] || availableComms.value[0];
});

// Computed property to determine the selected communication type
const selectedCommType = computed(() => {
  if (!selectedComm.value) return null;
  
  const comm = selectedComm.value;
  
  // Determine type based on object_scope and comm_type
  if (comm.object_scope === 'email') return 'email';
  if (comm.comm_type === 0) return 'chat';
  if (comm.comm_type === 1) return 'email';
  if (comm.comm_type === 2) return 'sms';
  if (comm.comm_type === 3) return 'voice';
  
  return 'chat'; // default
});

// Computed property for the selected communication title
const selectedCommTitle = computed(() => {
  if (!selectedComm.value) return '';
  
  const comm = selectedComm.value;
  return comm.title || comm.comm_label || 'Communication';
});

// Helper function to get communication icon
const getCommIcon = (comm: any) => {
  if (comm.object_scope === 'email') return 'pi pi-envelope';
  if (comm.comm_type === 0) return 'pi pi-comments';
  if (comm.comm_type === 1) return 'pi pi-envelope';
  if (comm.comm_type === 2) return 'pi pi-mobile';
  if (comm.comm_type === 3) return 'pi pi-phone';
  return 'pi pi-comments'; // default
};

// Helper function to get communication title
const getCommTitle = (comm: any) => {
  if (comm.object_scope === 'email') {
    return comm.title || 'Email';
  }
  return comm.title || comm.comm_label || `Case # ${comm.object_id?.slice(-6) || 'Unknown'}`;
};

// Function to handle tab selection
const selectTab = (index: number) => {
  selectedTabIndex.value = index;
  const selectedCommData = availableComms.value[index];
  console.log('📋 CommunicationPanel: Selected tab:', index, selectedCommData);
  
  // Find the corresponding communication in the store and select it
  if (selectedCommData && store.activeComms.length > 0) {
    const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id);
    if (storeComm) {
      store.selectComm(storeComm.id);
      console.log('📋 CommunicationPanel: Updated store selection to:', storeComm.id);
    }
  }
};

const initializeCommunications = async () => {
  if (!props.case || !userStore.xmppData) {
    console.log('🚫 CommunicationPanel: No case or XMPP data available');
    return;
  }

  isInitializing.value = true;
  error.value = null;

  try {
    console.log('🚀 CommunicationPanel: Initializing for case:', props.case);
    
    // Create CommsAppConfig from user's xmppData
    const commsAppConfig: CommsAppConfig = {
      clientInstance: 'panel-instance',
      csrfToken: 'panel-token',
      serviceConfig: userStore.xmppData,
      requestId: 'panel-request',
      availableComm: [],
      members_locations_id: 'panel-location',
      members_users_id: 'panel-user',
      members_id: 'panel-member'
    };

    // Initialize the communications store if not already initialized
    if (!store.appConfig) {
      console.log('📡 CommunicationPanel: Initializing communications store...');
      await store.initialize(commsAppConfig);
    }

    // Fetch detailed communication data for each availableComm
    const availableCommsArray = availableComms.value;
    console.log('💬 CommunicationPanel: Found available communications:', availableCommsArray.length);

    for (const availableComm of availableCommsArray) {
      console.log('📡 CommunicationPanel: Fetching detailed data for comm:', availableComm.id, availableComm.title);
      
      try {
        // Get detailed communication data
        const commData = await commsAPI.getComm({ 
          id: availableComm.id, 
          is_enter: true 
        });
        
        console.log('📋 CommunicationPanel: Got detailed comm data:', {
          id: commData.id,
          external_id: commData.external_id,
          title: commData.title,
          comm_type: commData.comm_type,
          object_scope: commData.object_scope
        });
        
        // Create an AvailableComm object with the detailed data
        const detailedAvailableComm: AvailableComm = {
          id: commData.id,
          object: commData.object,
          object_id: commData.object_id,
          object_scope: commData.object_scope,
          comm_type: commData.comm_type,
          comm_label: commData.comm_label,
          comm_status: commData.comm_status,
          comm_state: commData.comm_state,
          object_source: commData.object_source,
          billing_status: commData.billing_status,
          title: commData.title,
          subtitle: commData.subtitle,
          duration_plus: commData.duration_plus,
          duration: commData.duration,
          message_count: 0, // Default value
          user_message_cnt: commData.user_message_cnt,
          external_rpid: commData.external_rpid,
          external_lpid: commData.external_lpid,
          external_id: commData.external_id,
          external_status: commData.external_status,
          created: commData.created,
          updated: commData.updated,
          occupied: commData.occupied,
          completed: commData.completed,
          c__status: commData.c__status,
          c__state: commData.c__state,
          c__avatar: commData.c__avatar,
          unread_count: 0, // Default value
          participants: commData.participants?.map(p => ({
            object: p.object,
            object_id: p.object_id,
            name: p.name,
            alias: p.alias,
            external_id: p.external_id,
            host: p.host,
            eligible: p.eligible,
            id: p.id,
            presence: p.presence
          })) || [],
          // Add the additional properties we need for email composer
          ...(commData.cc_recipients && { cc_recipients: commData.cc_recipients }),
          ...(commData.last_sender && { last_sender: commData.last_sender })
        };
        
        // Open the communication in the store
        console.log('➕ CommunicationPanel: Opening communication in store:', detailedAvailableComm.id);
        await store.openExistingComm(detailedAvailableComm);
        
      } catch (commError) {
        console.error('❌ CommunicationPanel: Failed to fetch comm data for:', availableComm.id, commError);
        // Continue with other communications even if one fails
      }
    }
    
    console.log('✅ CommunicationPanel: Initialization complete');
    
  } catch (err) {
    console.error('❌ CommunicationPanel: Failed to initialize:', err);
    error.value = err instanceof Error ? err.message : 'Failed to initialize communications';
  } finally {
    isInitializing.value = false;
  }
};

const cleanup = () => {
  console.log('🧹 CommunicationPanel: Cleaning up...');
  try {
    store.$reset();
    console.log('✅ CommunicationPanel: Cleanup complete');
  } catch (err) {
    console.error('❌ CommunicationPanel: Cleanup failed:', err);
  }
};

// Watch for case changes and reinitialize
watch(() => props.case, (newCase) => {
  if (newCase) {
    initializeCommunications();
  } else {
    cleanup();
  }
}, { immediate: true });

// Watch for store selected communication changes and sync tab selection
watch(() => store.selectedComm?.id, (newSelectedId) => {
  if (newSelectedId && availableComms.value.length > 0) {
    const index = availableComms.value.findIndex(comm => comm.id === newSelectedId);
    if (index !== -1 && index !== selectedTabIndex.value) {
      selectedTabIndex.value = index;
      console.log('📋 CommunicationPanel: Synced tab selection to store:', index);
    }
  }
});

onBeforeUnmount(() => {
  cleanup();
});
</script>

<template>
  <div class="communication-panel bg-white shadow-lg flex flex-col h-full w-full">
    <!-- Header - Always show if we have a case -->
    <div v-if="props.case" class="px-2 py-1 flex items-center justify-between">
      <span class="text-[13px]" style="color: #6D7379;">
        Conversations
      </span>
    </div>
    
    <!-- Title - Show based on selected communication type -->
    <div v-if="props.case" class="bg-white py-1 px-4">
      <h1 class="text-[15px] font-medium text-gray-900">
        <template v-if="selectedCommType === 'email'">
          {{ selectedCommTitle || 'No Subject' }}
        </template>
        <template v-else-if="selectedCommType === 'chat'">
          Chat
        </template>
        <template v-else-if="selectedCommType === 'sms'">
          SMS
        </template>
        <template v-else-if="selectedCommType === 'voice'">
          Voice
        </template>
        <template v-else>
          Communication
        </template>
      </h1>
    </div>
    
    <!-- Tab Bar - Show immediately if we have available communications -->
    <div v-if="availableComms.length > 0" class="flex items-center border-b border-gray-200 bg-white">
      <div class="flex-1 flex items-center overflow-x-auto px-2 scroll-smooth">
        <div
          v-for="(comm, index) in availableComms"
          :key="comm.id"
          class="tab-item flex items-center px-3 py-1.5 cursor-pointer relative min-w-[100px]"
          :class="{
            'text-blue-600 border-b-2 border-blue-600': index === selectedTabIndex,
            'text-gray-600 hover:text-gray-900': index !== selectedTabIndex
          }"
          @click="selectTab(index)"
        >
          <i :class="[getCommIcon(comm), 'mr-2 text-sm flex-shrink-0']"></i>
          <span class="truncate text-sm font-medium">
            {{ getCommTitle(comm) }}
          </span>
        </div>
      </div>
      
      <div class="flex-shrink-0 px-2">
        <button
          class="p-1.5 text-gray-400 hover:text-gray-600 rounded"
          title="Add communication"
        >
          <i class="pi pi-plus text-sm"></i>
        </button>
      </div>
    </div>
    
    <!-- Main content area -->
    <div class="flex-1 overflow-hidden">
      <!-- Error state -->
      <div 
        v-if="error"
        class="h-full flex items-center justify-center text-red-600"
      >
        <div class="text-center">
          <div class="text-lg font-medium">Error</div>
          <div class="mt-2">{{ error }}</div>
        </div>
      </div>

      <!-- No case state -->
      <div 
        v-else-if="!props.case"
        class="h-full flex items-center justify-center text-gray-500"
      >
        No case selected
      </div>

      <!-- No communications available -->
      <div 
        v-else-if="availableComms.length === 0"
        class="h-full flex items-center justify-center text-gray-500"
      >
        No communications available for this case
      </div>

      <!-- Loading state -->
      <div 
        v-else-if="isInitializing"
        class="h-full flex items-center justify-center bg-white"
      >
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div class="mt-3 text-gray-600 text-sm">Loading communication data...</div>
        </div>
      </div>

      <!-- Communication content -->
      <template v-else-if="store.selectedComm && selectedComm">
        <EmailChannel 
          v-if="selectedCommType === 'email'"
          :communication="store.selectedComm"
        />
        <ChatChannel 
          v-else-if="selectedCommType === 'chat'"
          :communication="store.selectedComm"
        />
        <SmsChannel 
          v-else-if="selectedCommType === 'sms'"
          :communication="store.selectedComm"
        />
        <VoiceChannel 
          v-else-if="selectedCommType === 'voice'"
          :communication="store.selectedComm"
        />
      </template>

      <!-- Fallback loading -->
      <div 
        v-else
        class="h-full flex items-center justify-center bg-white"
      >
        <div class="text-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          <div class="mt-3 text-gray-600 text-sm">Connecting to {{ selectedCommType }}...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.communication-panel {
  position: relative;
  overflow-x: auto;
  height: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}
</style>