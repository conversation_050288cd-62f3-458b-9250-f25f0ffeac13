<script setup lang="ts">
defineProps<{
  fileName: string;
  progress: number;
  size: number;
}>();

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};
</script>

<template>
  <div class="bg-gray-50 p-3 rounded-lg">
    <div class="flex items-center gap-3">
      <i class="pi pi-file text-gray-400 text-xl"></i>
      <div class="flex-1 min-w-0">
        <div class="flex justify-between items-center mb-1">
          <div class="font-medium truncate">{{ fileName }}</div>
          <div class="text-sm text-gray-500">{{ formatFileSize(size) }}</div>
        </div>
        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
          <div 
            class="h-full bg-blue-500 transition-all duration-300 ease-out"
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
        <div class="text-sm text-gray-500 mt-1">{{ progress }}% uploaded</div>
      </div>
    </div>
  </div>
</template>