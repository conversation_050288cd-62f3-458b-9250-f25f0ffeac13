<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted } from 'vue';
import { useCommunicationsStore } from '../stores/communications';
import Button from 'primevue/button';
import Menu from 'primevue/menu';
import type { MenuItem } from 'primevue/menuitem';
import type { CommunicationType } from '../types';

const store = useCommunicationsStore();
const menu = ref();
const editingCommId = ref<string | null>(null);
const editTitle = ref('');

const menuItems: MenuItem[] = [
  {
    label: 'Email',
    icon: 'pi pi-envelope',
    command: () => store.addNewCommunication('email')
  },
  {
    label: 'SMS',
    icon: 'pi pi-mobile',
    command: () => store.addNewCommunication('sms')
  },
  {
    label: 'Voice',
    icon: 'pi pi-phone',
    command: () => store.addNewCommunication('voice')
  }
];

const dragOverIndex = ref<number | null>(null);

const tabClasses = computed(() => (index: number) => {
  return {
    'tab-item flex items-center px-3 py-1.5 cursor-move relative': true,
    'text-blue-600 border-b-2 border-blue-600': store.selectedComm?.id === store.activeComms[index].id,
    'text-gray-600 hover:text-gray-900': store.selectedComm?.id !== store.activeComms[index].id,
    'opacity-50': store.draggedCommId === store.activeComms[index].id,
    'before:absolute before:inset-0 before:bg-blue-100/50 before:border-l-2 before:border-blue-500': dragOverIndex.value === index
  };
});

const getTabIcon = (type: CommunicationType) => {
  const icons = {
    email: 'pi pi-envelope',
    chat: 'pi pi-comments',
    sms: 'pi pi-mobile',
    voice: 'pi pi-phone'
  };
  return icons[type];
};

const startEditing = (comm: { id: string; title: string }) => {
  editingCommId.value = comm.id;
  editTitle.value = comm.title || '';
  nextTick(() => {
    const input = document.querySelector(`input[data-comm-id="${comm.id}"]`) as HTMLInputElement;
    if (input) {
      input.focus();
      input.select();
      input.onclick = (e) => e.stopPropagation();
    }
  });
};

const saveTitle = () => {
  if (editingCommId.value && editTitle.value.trim()) {
    store.updateCommTitle(editingCommId.value, editTitle.value.trim());
    editingCommId.value = null;
  }
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    saveTitle();
  } else if (e.key === 'Escape') {
    editingCommId.value = null;
  }
};

const handleDragStart = (e: DragEvent, index: number) => {
  if (!e.dataTransfer) return;
  e.dataTransfer.effectAllowed = 'move';
  store.setDraggedComm(store.activeComms[index].id);
  // Add some transparency to dragged element
  if (e.target instanceof HTMLElement) {
    e.target.style.opacity = '0.5';
  }
};

const handleDragEnd = (e: DragEvent) => {
  if (e.target instanceof HTMLElement) {
    e.target.style.opacity = '';
  }
  store.setDraggedComm(null);
  dragOverIndex.value = null;
};

const handleDragOver = (e: DragEvent, index: number) => {
  e.preventDefault();
  dragOverIndex.value = index;
  if (e.dataTransfer) {
    e.dataTransfer.dropEffect = 'move';
  }
};

const handleDrop = (e: DragEvent, toIndex: number) => {
  e.preventDefault();
  const fromIndex = store.activeComms.findIndex(comm => comm.id === store.draggedCommId);
  if (fromIndex !== -1 && fromIndex !== toIndex) {
    store.reorderComms(fromIndex, toIndex);
  }
  dragOverIndex.value = null;
  store.setDraggedComm(null);
};

const handleDragLeave = () => {
  dragOverIndex.value = null;
};

// Add ref for the tab container
const tabContainer = ref<HTMLElement | null>(null);

// Add method to scroll active tab into view
const scrollActiveTabIntoView = () => {
  if (!tabContainer.value) return;
  
  const activeTab = tabContainer.value.querySelector('.border-blue-600');
  if (activeTab) {
    activeTab.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'center'
    });
  }
};

// Watch for selected comm changes to trigger scroll
watch(() => store.selectedComm?.id, () => {
  nextTick(() => {
    scrollActiveTabIntoView();
  });
});

// Also scroll on mount
onMounted(() => {
  scrollActiveTabIntoView();
});
</script>

<template>
  <div class="flex items-center border-b border-gray-200 bg-white">
    <div 
      ref="tabContainer"
      class="flex-1 flex items-center overflow-x-auto px-2 scroll-smooth"
    >
      <div
        v-for="(comm, index) in store.activeComms"
        :key="comm.id"
        :class="tabClasses(index)"
        class="tab-item flex items-center px-3 py-1 cursor-move relative min-w-[100px]"
        draggable="true"
        @dragstart="handleDragStart($event, index)"
        @dragend="handleDragEnd"
        @dragover.prevent="handleDragOver($event, index)"
        @dragleave="handleDragLeave"
        @drop="handleDrop($event, index)"
        @click="store.selectComm(comm.id)"
      >
        <i :class="[getTabIcon(comm.type), 'mr-2 text-sm flex-shrink-0']"></i>
        
        <!-- Title (editable) -->
        <div class="flex items-center min-w-0 flex-1">
          <div 
            v-if="editingCommId === comm.id"
            class="flex-1 min-w-[100px]"
          >
            <input
              v-model="editTitle"
              :data-comm-id="comm.id"
              class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white text-gray-900"
              @blur="saveTitle"
              @keydown="handleKeyDown"
              @click.stop
            >
          </div>
          <span 
            v-else 
            class="truncate text-sm font-medium"
            @dblclick.stop="startEditing(comm)"
          >
            {{ comm.title }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="flex-shrink-0 px-2">
      <Button
        icon="pi pi-plus"
        class="text-gray-600 hover:text-gray-900 h-[34px] w-[34px]"
        rounded
        text
        @click="menu.toggle($event)"
        aria-label="New Communication"
      />
      <Menu
        ref="menu"
        :model="menuItems"
        :popup="true"
      />
    </div>
  </div>
</template>

<style scoped>
.tab-item {
  transition: all 0.2s ease;
  margin-right: 4px;
  user-select: none;
  position: relative;
  max-width: 200px;
}

.tab-item input {
  color: #111827;
  background-color: white;
  min-width: 100px;
  width: 100%;
}

:deep(.p-button.p-button-text) {
  padding: 0.25rem;
  color: inherit;
  width: 34px;
  height: 34px;
}

:deep(.p-button.p-button-text:enabled:hover) {
  background: rgba(0, 0, 0, 0.04);
  color: inherit;
}

/* Hide scrollbar but keep functionality */
.overflow-x-auto {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.overflow-x-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
</style>