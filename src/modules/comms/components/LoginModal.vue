<script setup lang="ts">
import { ref } from 'vue';
import { useUserStore } from '../stores/user';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

const userStore = useUserStore();
const username = ref('');
const password = ref('');
const loading = ref(false);
const errorMessage = ref('');

async function handleLogin() {
  if (!username.value || !password.value) {
    errorMessage.value = 'Please fill in all fields';
    return;
  }

  loading.value = true;
  errorMessage.value = '';

  try {
    await userStore.login({
      username: username.value,
      password: password.value
    });
    emit('update:visible', false);
    username.value = '';
    password.value = '';
  } catch (error) {
    errorMessage.value = 'Login failed. Please try again.';
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <Dialog
    :visible="visible"
    modal
    header="Login"
    :style="{ width: '400px' }"
    :closable="true"
    @update:visible="(value) => emit('update:visible', value)"
  >
    <div class="flex flex-col space-y-4">
      <div v-if="errorMessage" class="text-red-500 text-sm">
        {{ errorMessage }}
      </div>

      <div class="flex flex-col space-y-2">
        <label for="username" class="text-sm font-medium text-gray-700">Username</label>
        <InputText
          id="username"
          v-model="username"
          type="text"
          class="p-inputtext-sm"
          :disabled="loading"
        />
      </div>

      <div class="flex flex-col space-y-2">
        <label for="password" class="text-sm font-medium text-gray-700">Password</label>
        <InputText
          id="password"
          v-model="password"
          type="password"
          class="p-inputtext-sm"
          :disabled="loading"
          @keyup.enter="handleLogin"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button
          label="Cancel"
          text
          @click="emit('update:visible', false)"
          :disabled="loading"
        />
        <Button
          label="Login"
          @click="handleLogin"
          :loading="loading"
        />
      </div>
    </template>
  </Dialog>
</template> 