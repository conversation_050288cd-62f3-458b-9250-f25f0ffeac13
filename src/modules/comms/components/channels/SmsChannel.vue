<script setup lang="ts">
import type { Communication } from '../../types';
import { ref, onMounted, watch } from 'vue';
import { useCommunicationsStore } from '../../stores/communications';
import ProgressSpinner from 'primevue/progressspinner';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';

const props = defineProps<{
  communication: Communication;
}>();

const store = useCommunicationsStore();
const phoneNumber = ref('');
const message = ref('');
const isLoading = ref(true);

const sendMessage = async () => {
  if (phoneNumber.value.trim() && message.value.trim()) {
    await store.sendMessage(props.communication.id, message.value.trim());
    message.value = '';
  }
};

onMounted(() => {
  isLoading.value = true;
  setTimeout(() => {
    isLoading.value = false;
  }, 250);
});

watch(() => props.communication.id, () => {
  isLoading.value = true;
  setTimeout(() => {
    isLoading.value = false;
  }, 250);
});
</script>

<template>
  <div class="flex flex-col h-full bg-white">
    <!-- Loading state -->
    <div 
      v-if="isLoading"
      class="flex-1 flex items-center justify-center bg-white"
    >
      <div class="text-center">
        <ProgressSpinner 
          style="width: 50px; height: 50px;" 
          strokeWidth="4" 
          animationDuration="1.5s"
        />
        <div class="mt-4 text-gray-600">
          Loading messages...
        </div>
      </div>
    </div>

    <!-- Content (only show when not loading) -->
    <template v-else>
      <!-- Empty state -->
      <BravoZeroStateScreen
        v-if="!communication.messages.length"
        title="Send New SMS"
        message="Start a new SMS thread by choosing a recipient and creating a message."
        :showButton="false"
        :imageSrc="EmailZeroStateSvg"
        image-alt="SMS Messages"
        :action-handler="() => {}"
        class="flex items-center justify-center h-full"
      />

      <div 
        v-else
        class="flex-1 overflow-y-auto p-4"
      >
        <!-- SMS messages will go here -->
      </div>
      
      <div class="border-t border-gray-200 p-4">
        <div class="mb-4">
          <input 
            type="tel" 
            v-model="phoneNumber"
            class="w-full border border-gray-300 rounded px-2 py-1 text-[#282A2C]"
            placeholder="Enter phone number"
          >
        </div>
        <textarea 
          v-model="message"
          class="w-full border border-gray-300 rounded p-2 h-20 resize-none text-[#282A2C]"
          placeholder="Type your message..."
        ></textarea>
        <div class="flex justify-end">
          <button 
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="!phoneNumber.trim() || !message.trim()"
            @click="sendMessage"
          >
            Send Message
          </button>
        </div>
      </div>
    </template>
  </div>
</template>