<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import type { Communication, Message, XMPPNotificationObject, MessageEmail } from '../../types';
import { tryParseMessageContent, shouldHideMessage, formatFileSize } from '../../types';
import { format } from 'date-fns';
import { useCommunicationsStore } from '../../stores/communications';
import { useCommsAPI } from '@/composables/services/useCommsAPI';
import { htmlToPlainText, separateEmailContent, getAvatarColor } from '../../utils/commHelpers';
import { useUserStore } from '@/stores/user';
import { useMetaStore } from '@/stores/meta';
import { xmppService } from '../../services/xmpp';
import { nextTick } from 'vue';
import FileUploadProgress from '../FileUploadProgress.vue';
import Dropdown from 'primevue/dropdown';
import Editor from 'primevue/editor';
import Dialog from 'primevue/dialog';
import Tooltip from 'primevue/tooltip';
import { useAIAssistant } from '@/composables/useAIAssistant';
import { useCannedResponseTokens } from '@/composables/useCannedResponseTokens';
import type { Issue } from '@/composables/services/useIssuesAPI';
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue';
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue';
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue';
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import EmailZeroStateSvg from '@/assets/email-zero-state.svg';
import BravoChip from '@services/ui-component-library/components/BravoChip.vue';
import InputText from 'primevue/inputtext';

const props = defineProps<{
  communication: Communication;
  issue?: Issue | null;
}>();

// Register the tooltip directive
const vTooltip = Tooltip;

const store = useCommunicationsStore();
const commsAPI = useCommsAPI();
const userStore = useUserStore();
const metaStore = useMetaStore();
const aiAssistant = useAIAssistant();
const { processCannedResponse } = useCannedResponseTokens(computed(() => props.issue || null));
const expandedMessages = ref<Set<string>>(new Set());
const showMetadata = ref<Set<string>>(new Set());
const fileInput = ref<HTMLInputElement | null>(null);
const uploadingFiles = ref<Array<{ id: string; name: string; size: number; progress: number }>>([]);
const attachedFiles = ref<Array<{ id: string; name: string; size: number; type: string; file: File; preview?: string }>>([]);
const showTemplates = ref(false);
const selectedTemplate = ref(null);
const selectedAiAction = ref(null);
const showSummaryDialog = ref(false);
const summaryContent = ref('');
const isSummarizing = ref(false);
const isGeneratingReply = ref(false);
const isReformatting = ref(false);
const isSendingEmail = ref(false);
const showLinkDialog = ref(false);
const linkUrl = ref('');
const selectedText = ref('');
const editorRef = ref();
const showEditorToolbar = ref(false);
const textareaRef = ref<HTMLTextAreaElement | null>(null);
const selectionRange = ref<{ start: number; end: number } | null>(null);
const messagesContainer = ref<HTMLElement | null>(null);

// Simplified loading state
const isLoading = ref(true);

// Composer resizing
const composerHeight = ref(300); // Default height in pixels
const isResizingComposer = ref(false);
const baseComposerHeight = 300; // Base height without fields
const expandedComposerHeight = 420; // Expanded height with fields

// Email content separation - track which messages have expanded quoted content
const expandedQuotedContent = ref<Set<string>>(new Set());

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// Enhanced message parsing function for emails
const parseEmailMessage = (message: Message): {
  parsedContent: XMPPNotificationObject | null;
  emailData: MessageEmail | null;
  displayContent: string;
  shouldHide: boolean;
} => {
  const parseResult = tryParseMessageContent(message.content);
  
  if (!parseResult.isParsed) {
    // Plain text message
    return {
      parsedContent: null,
      emailData: null,
      displayContent: message.content,
      shouldHide: false
    };
  }

  const notificationObj = parseResult.data!;
  const shouldHide = shouldHideMessage(notificationObj);

  // Extract email data
  const emailData = notificationObj.email || null;
  
  // Handle different message types
  let displayContent = '';
  
  if (emailData?.body) {
    displayContent = emailData.body;
  } else if (notificationObj.message) {
    displayContent = decodeURIComponent(String(notificationObj.message).replace(/\+/g, ' '));
  }

  return {
    parsedContent: notificationObj,
    emailData,
    displayContent,
    shouldHide
  };
};

// Get sender details with enhanced information
const getSenderDetails = (message: Message) => {
  const participant = props.communication.participants.find(p => p.id === message.senderId);
  const parseResult = parseEmailMessage(message);
  
  return {
    name: parseResult.parsedContent?.context?.from_name || 
          parseResult.emailData?.from_name || 
          participant?.name || 
          message.senderId.split('@')[0],
    email: parseResult.parsedContent?.context?.from || 
           parseResult.emailData?.from || 
           participant?.email || 
           `${message.senderId}@example.com`,
    initials: (parseResult.parsedContent?.context?.from_name || 
               parseResult.emailData?.from_name || 
               participant?.name || 
               message.senderId).charAt(0).toUpperCase(),
    avatar: parseResult.parsedContent?.context?.from_avatar || participant?.avatar
  };
};

// Check if message is from current user
const isMessageFromCurrentUser = (message: Message): boolean => {
  return store.isCurrentUser(message.senderId);
};

// Define messages computed property with enhanced parsing
const messages = computed(() => {
  // Filter out system messages and hidden messages, then process
  const processedMessages = props.communication.messages
    .filter(message => {
      if (message.type === 'system') return false;
      const parseResult = parseEmailMessage(message);
      return !parseResult.shouldHide;
    })
    .map((message: Message) => {
      const parseResult = parseEmailMessage(message);
      const sender = getSenderDetails(message);
      
      return {
        ...message,
        sender,
        parsedContent: parseResult.parsedContent,
        emailData: parseResult.emailData,
        displayContent: parseResult.displayContent,
        hasAttachment: !!(parseResult.parsedContent?.attachment),
        attachmentData: parseResult.parsedContent?.attachment
      };
    });

  // Group file attachments with their parent messages
  const groupedMessages = [];
  for (let i = 0; i < processedMessages.length; i++) {
    const currentMessage = processedMessages[i];
    const nextMessage = processedMessages[i + 1];
    
    // If the next message is a file attachment (has attachment but no email body)
    if (
      nextMessage?.parsedContent?.attachment && 
      (!nextMessage.emailData?.body || !nextMessage.emailData.body.trim()) &&
      currentMessage.emailData?.body // Current message has an email body
    ) {
      // Merge the attachment into the current message
      currentMessage.attachmentData = nextMessage.parsedContent.attachment;
      currentMessage.hasAttachment = true;
      // Skip the next message since we've merged it
      i++;
    }
    
    groupedMessages.push(currentMessage);
  }
  
  return groupedMessages;
});

// Simplified mount
onMounted(() => {
  // Expand the latest message
  const latestMessage = messages.value[messages.value.length - 1];
  if (latestMessage) {
    expandedMessages.value = new Set([latestMessage.id]);
  }
  
  setTimeout(() => {
    isLoading.value = false;
    scrollToBottom();
  }, 100);
  
  // Load canned responses when component mounts
  loadCannedResponses();
});

// Watch for new messages and scroll to bottom
watch(() => messages.value.length, (newLength, oldLength) => {
  // If new messages were added, expand only the latest one
  if (newLength > oldLength) {
    const latestMessage = messages.value[messages.value.length - 1];
    if (latestMessage) {
      expandedMessages.value = new Set([latestMessage.id]);
    }
  }
  scrollToBottom();
});

// Add watcher for communication changes
watch(() => props.communication.id, () => {
  isLoading.value = true;
  setTimeout(() => {
    isLoading.value = false;
    scrollToBottom();
  }, 100);
  
  // Reload canned responses when communication changes
  loadCannedResponses();
});

// Fetch canned responses from API
const cannedResponses = ref<any[]>([]);
const loadingCannedResponses = ref(false);

// Get filtered canned responses based on partner team
const filteredCannedResponses = computed(() => {
  if (!cannedResponses.value.length) return [];
  
  // Get the issue's owner partner team ID
  const issueTeamId = props.issue?.owner_partners_teams_id;
  const issuePartnerId = props.issue?.owner_partners_id;
  
  console.log('🔍 Filtering canned responses:', {
    issueTeamId,
    issuePartnerId,
    totalResponses: cannedResponses.value.length,
    issue: props.issue
  });
  if (!issueTeamId && !issuePartnerId) {
    // If no team or partner ID, return all responses
    console.log('🔍 No issue team/partner ID found, returning all responses');
    return cannedResponses.value;
  }
  
  // First try to filter by exact team match
  let filtered = [];
  if (issueTeamId) {
    filtered = cannedResponses.value.filter(response => 
      response.partners_teams_ids && 
      response.partners_teams_ids.includes(issueTeamId)
    );
    console.log('🔍 Exact team match results:', filtered.length);
  }
  
  // If no exact matches, try filtering by partner ID (less restrictive)
  if (filtered.length === 0 && issuePartnerId) {
    filtered = cannedResponses.value.filter(response => 
      response.partners_id === issuePartnerId
    );
    console.log('🔍 Partner ID match results:', filtered.length);
  }
  
  // If still no matches, return all responses (fallback)
  if (filtered.length === 0) {
    console.log('🔍 No matches found, returning all responses as fallback');
    return cannedResponses.value;
  }
  
  console.log('🔍 Final filtered responses:', filtered.length);
  return filtered;
});

// Convert filtered canned responses to dropdown options
const templateOptions = computed(() => {
  const options = filteredCannedResponses.value.map(response => ({
    label: response.name || response.lbl || response._title,
    value: response.id,
    content: response.content
  }));
  
  console.log('📋 Template options computed:', {
    loading: loadingCannedResponses.value,
    optionsLength: options.length,
    disabled: loadingCannedResponses.value || !options.length,
    cannedResponsesLength: cannedResponses.value.length,
    filteredLength: filteredCannedResponses.value.length
  });
  
  return options;
});

const loadCannedResponses = async () => {
  try {
    loadingCannedResponses.value = true;
    const response = await metaStore.fetchCannedResponses({
      object: 'issues', // These params are no longer used but kept for interface compatibility
      object_id: props.communication.availableComm?.id || ''
    });
    cannedResponses.value = response.canned_responses?.results || response.pl__canned_responses || [];
    console.log('📋 Loaded canned responses:', cannedResponses.value.length);
  } catch (error) {
    console.error('❌ Failed to load canned responses:', error);
    cannedResponses.value = [];
  } finally {
    loadingCannedResponses.value = false;
  }
};

const selectedTemplateValue = ref('');

const applyTemplate = () => {
  try {
    if (selectedTemplateValue.value) {
      const template = templateOptions.value.find(t => t.value === selectedTemplateValue.value);
      if (template && template.content) {
        // Process the template content to replace tokens with actual case data
        const processedContent = processCannedResponse(template.content);
        
        // Append to existing content instead of replacing
        const existingContent = composerState.value.content || '';
        const separator = existingContent.trim() ? '<br><br>' : '';
        composerState.value.content = existingContent + separator + processedContent;
        
        console.log('✅ Applied template:', template.label);
      }
      selectedTemplateValue.value = ''; // Reset selection
    }
  } catch (error) {
    console.error('❌ Error applying template:', error);
  }
};

const aiActions = [
  {
    label: 'Summarize Conversation',
    value: 'summarize',
    icon: 'pi pi-list'
  },
  {
    label: 'Generate Next Reply',
    value: 'generate',
    icon: 'pi pi-reply'
  },
  {
    label: 'Reformat Current Response',
    value: 'reformat',
    icon: 'pi pi-pencil'
  }
];

const handleAiAction = async () => {
  if (!selectedAiAction.value) return;

  try {
    switch (selectedAiAction.value) {
      case 'summarize':
        showSummaryDialog.value = true;
        isSummarizing.value = true;
        // Use the AI assistant composable
        summaryContent.value = await aiAssistant.summarizeConversation(messages.value);
        break;
      case 'generate':
        isGeneratingReply.value = true;
        // Use the AI assistant composable
        const generatedReply = await aiAssistant.generateReply(messages.value);
        // Update the editor content with the generated reply
        composerState.value.content = generatedReply;
        break;
      case 'reformat':
        if (!composerState.value.content.trim()) {
          return;
        }
        isReformatting.value = true;
        const reformattedContent = await aiAssistant.reformatResponse(composerState.value.content);
        // Update the editor content with the reformatted text
        composerState.value.content = reformattedContent;
        break;
    }
  } catch (error) {
    console.error('AI action failed:', error);
    if (selectedAiAction.value === 'summarize') {
      summaryContent.value = 'Failed to process request. Please try again.';
    }
  } finally {
    isSummarizing.value = false;
    isGeneratingReply.value = false;
    isReformatting.value = false;
    selectedAiAction.value = null;
  }
};

const composerState = ref({
  to: [] as string[],
  cc: [] as string[],
  subject: '',
  content: '',
  minimizedFields: true
});

// Composer resize functionality
const startComposerResize = (event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();

  // Add a class to the resize handle
  const resizeHandle = event.currentTarget as HTMLElement;
  resizeHandle.classList.add('resizing');

  // Add a transparent overlay that covers the whole document to capture mouse events
  const overlay = document.createElement('div');
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100%';
  overlay.style.height = '100%';
  overlay.style.backgroundColor = 'transparent';
  overlay.style.zIndex = '9999';
  overlay.style.cursor = 'row-resize';
  document.body.appendChild(overlay);

  // Store initial position
  const startY = event.clientY;
  const startHeight = composerHeight.value;
  document.body.classList.add('resizing-composer');

  // Add mousemove event listener to the overlay
  const onMouseMove = (e: MouseEvent) => {
    // For top resize handle, when dragging up (negative deltaY), composer should get larger
    // When dragging down (positive deltaY), composer should get smaller
    const deltaY = startY - e.clientY;
    let newHeight = startHeight + deltaY;

    // Add min/max constraints
    newHeight = Math.max(newHeight, 200); // Min height
    newHeight = Math.min(newHeight, 600); // Max height

    composerHeight.value = newHeight;
  };

  // Add mouseup event listener to the overlay
  const onMouseUp = () => {
    // Remove the resizing class
    resizeHandle.classList.remove('resizing');

    document.body.classList.remove('resizing-composer');
    document.body.removeEventListener('mousemove', onMouseMove);
    document.body.removeEventListener('mouseup', onMouseUp);
    document.body.removeChild(overlay);

    // Persist the height to local storage
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('email-composer-height', String(composerHeight.value));
    }
  };

  document.body.addEventListener('mousemove', onMouseMove);
  document.body.addEventListener('mouseup', onMouseUp);
};

// Load saved composer height from localStorage on mount
const loadSavedComposerHeight = () => {
  if (typeof localStorage !== 'undefined') {
    const savedHeight = localStorage.getItem('email-composer-height');
    if (savedHeight) {
      const height = parseInt(savedHeight, 10);
      // Only use saved height if it's reasonable, otherwise use default based on field state
      if (height >= 200 && height <= 800) {
        composerHeight.value = height;
      } else {
        // Use appropriate default based on current field state
        composerHeight.value = composerState.value.minimizedFields ? baseComposerHeight : expandedComposerHeight;
      }
    } else {
      // No saved height, use default based on field state
      composerHeight.value = composerState.value.minimizedFields ? baseComposerHeight : expandedComposerHeight;
    }
  }
};

const handleInsertLink = () => {
  if (!textareaRef.value) return;
  const start = textareaRef.value.selectionStart;
  const end = textareaRef.value.selectionEnd;
  if (start === end) return;
  selectedText.value = composerState.value.content.substring(start, end);
  selectionRange.value = { start, end };
  showLinkDialog.value = true;
};

const insertLink = () => {
  if (!selectionRange.value || !linkUrl.value.trim()) return;
  const { start, end } = selectionRange.value;
  const linkHtml = `<a href="${linkUrl.value}">${selectedText.value}</a>`;
  composerState.value.content = 
    composerState.value.content.substring(0, start) +
    linkHtml +
    composerState.value.content.substring(end);
  showLinkDialog.value = false;
  linkUrl.value = '';
  selectedText.value = '';
  selectionRange.value = null;
};

const handleFileUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    Array.from(input.files).forEach(async (file) => {
      const fileId = crypto.randomUUID();
      
      // Add to uploading files for progress display
      uploadingFiles.value.push({
        id: fileId,
        name: file.name,
        size: file.size,
        progress: 0
      });

      // Create preview for images
      let preview: string | undefined;
      if (file.type.startsWith('image/')) {
        try {
          preview = await createImagePreview(file);
        } catch (error) {
          console.warn('Failed to create preview for', file.name, error);
        }
      }

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        const fileIndex = uploadingFiles.value.findIndex((f: any) => f.id === fileId);
        if (fileIndex !== -1) {
          uploadingFiles.value[fileIndex].progress = progress;
        }
        
        if (progress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            // Remove from uploading and add to attached
            uploadingFiles.value = uploadingFiles.value.filter((f: any) => f.id !== fileId);
            attachedFiles.value.push({
              id: fileId,
              name: file.name,
              size: file.size,
              type: file.type,
              file: file,
              preview: preview
            });
          }, 500);
        }
      }, 200);
    });

    // Clear the input so the same file can be selected again
    input.value = '';
  }
};

// Helper function to create image previews
const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('FileReader error'));
    reader.readAsDataURL(file);
  });
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.metadata-toggle') && !target.closest('.metadata-dropdown')) {
    showMetadata.value.clear();
  }
};

onMounted(() => {
  window.addEventListener('click', handleClickOutside);
  loadSavedComposerHeight();
});

onBeforeUnmount(() => {
  window.removeEventListener('click', handleClickOutside);
});

const toggleMessage = (messageId: string) => {
  if (expandedMessages.value.has(messageId)) {
    expandedMessages.value.delete(messageId);
  } else {
    expandedMessages.value.add(messageId);
  }
};

const isMessageExpanded = (messageId: string) => {
  return expandedMessages.value.has(messageId);
};

const toggleQuotedContent = (messageId: string) => {
  if (expandedQuotedContent.value.has(messageId)) {
    expandedQuotedContent.value.delete(messageId);
  } else {
    expandedQuotedContent.value.add(messageId);
  }
};

const isQuotedContentExpanded = (messageId: string) => {
  return expandedQuotedContent.value.has(messageId);
};

const toggleMetadata = (messageId: string) => {
  if (showMetadata.value.has(messageId)) {
    showMetadata.value.delete(messageId);
  } else {
    showMetadata.value.add(messageId);
  }
};

const formatDetailedDate = (date: Date) => {
  return `${format(date, 'MMM d, yyyy, h:mm:ss a')} (${
    Intl.DateTimeFormat().resolvedOptions().timeZone
  })`;
};

const getFileTypeLabel = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return 'Image';
  if (mimeType.startsWith('video/')) return 'Video';
  if (mimeType.startsWith('audio/')) return 'Audio';
  if (mimeType === 'application/pdf') return 'PDF';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'Document';
  if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'Spreadsheet';
  if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'Presentation';
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) return 'Archive';
  if (mimeType.includes('text/')) return 'Text';
  return 'File';
};

const sendEmail = async () => {
  if (!composerState.value.content.trim() && attachedFiles.value.length === 0) {
    return;
  }

  try {
    isSendingEmail.value = true;
    console.log('📧 EmailChannel: Sending email via API...');
    
    // Prepare email data for the API call
    const emailReplyData = {
      to: composerState.value.to.filter(email => email.trim()).join(',') || getEmailSummary.value.to || '',
      toLabel: composerState.value.to.filter(email => email.trim()).join(',') || getEmailSummary.value.to || '',
      cc: composerState.value.cc.filter(email => email.trim()),
      subject: composerState.value.subject || getEmailSummary.value.subject || '',
      replyBody: composerState.value.content,
      from: userStore.userData?.email || '',
      from_name: userStore.userData?.full_name || `${userStore.userData?.first_name || ''} ${userStore.userData?.last_name || ''}`.trim() || '',
      from_email: userStore.userData?.email || '',
      origParts: []
    };

    // Prepare files for upload
    const files: File[] = attachedFiles.value.map(attachedFile => attachedFile.file);

    // Call the sendEmail API
    const emailResponse = await commsAPI.sendEmail({
      id: props.communication.id,
      commId: props.communication.id,
      from_email: emailReplyData.from_email,
      replyBody: emailReplyData.replyBody,
      subject: emailReplyData.subject,
      reply: emailReplyData,
      files: files.length > 0 ? files : undefined,
      file_tag: files.length > 0 ? 'attachment' : undefined
    });

    console.log('✅ EmailChannel: Email API response:', emailResponse);

    // Handle the response payloads - send each payload to XMPP
    if (emailResponse.payloads && emailResponse.payloads.length > 0) {
      console.log('📨 EmailChannel: Processing', emailResponse.payloads.length, 'payloads');
      
      for (const messagePayload of emailResponse.payloads) {
        console.log('📤 EmailChannel: Sending payload to XMPP:', messagePayload);
        
        // Send each payload as a separate XMPP message
        await xmppService.sendMessage(props.communication, JSON.stringify(messagePayload));
      }
    }

    // Clear the composer after successful send
    composerState.value.content = '';
    // Don't clear subject and recipients - keep them for follow-up emails
    attachedFiles.value = [];
    
    console.log('✅ EmailChannel: Email sent successfully');
    
  } catch (error) {
    console.error('❌ EmailChannel: Failed to send email:', error);
    // You could add a toast notification here to inform the user
  } finally {
    isSendingEmail.value = false;
  }
};

// Add this computed property after other computed properties
const getEmailSummary = computed(() => {
  if (!props.communication.messages.length) return { to: 'No recipients' };

  // Get the latest message
  const latestMessage = props.communication.messages[props.communication.messages.length - 1];
  const parseResult = parseEmailMessage(latestMessage);
  
  if (!parseResult.parsedContent) {
    return {
      from: 'You',
      to: 'Recipients',
      subject: 'No subject',
      cc: null,
      bcc: null
    };
  }
  
  const context = parseResult.parsedContent.context;
  const emailData = parseResult.emailData;
  
  if (!context) return { to: 'No recipients' };

  return {
    from: context.from_name || context.from,
    to: context.to || context.to_name || 'Recipients',
    subject: emailData?.subject || props.communication.title,
    cc: context.cc,
    bcc: context.bcc
  };
});

// Add this computed property after getEmailSummary
const composerDefaults = computed(() => {
  const latestMessage = props.communication.messages.length > 0 
    ? props.communication.messages[props.communication.messages.length - 1]
    : null;
  
  const parseResult = latestMessage ? parseEmailMessage(latestMessage) : null;
  
  // Get subject from latest email message
  const subject = parseResult?.emailData?.subject || 
                  props.communication.title || 
                  '';
  

  // Get TO recipients from last_sender in communication object
  const toRecipients = (props.communication.availableComm as any)?.last_sender 
    ? [(props.communication.availableComm as any).last_sender as string]
    : [];
  
  // Get CC recipients from cc_recipients in communication object
  const ccRecipients = (props.communication.availableComm as any)?.cc_recipients 
    ? Object.values((props.communication.availableComm as any).cc_recipients as Record<string, string>)
    : [];
  
  return {
    to: toRecipients,
    cc: ccRecipients,
    subject: subject.startsWith('Re: ') ? subject : `Re: ${subject}`
  };
});

// Watch for communication changes and update composer defaults
watch(() => props.communication.id, () => {
  // Update composer with new defaults when communication changes
  const defaults = composerDefaults.value;
  composerState.value.to = defaults.to;
  composerState.value.cc = defaults.cc;
  composerState.value.subject = defaults.subject;
}, { immediate: true });

// Also update when messages change (new email received)
watch(() => props.communication.messages.length, () => {
  // Only update if fields are empty or minimized
  if (composerState.value.minimizedFields || 
      (!composerState.value.subject.trim() && composerState.value.to.length === 0)) {
    const defaults = composerDefaults.value;
    composerState.value.to = defaults.to;
    composerState.value.cc = defaults.cc;
    composerState.value.subject = defaults.subject;
  }
});

// Email validation function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// State for adding new CC recipients
const newCcRecipient = ref('');
const ccInputError = ref('');

// Function to add CC recipient
const addCcRecipient = () => {
  const email = newCcRecipient.value.trim();
  if (!email) return;
  
  if (!isValidEmail(email)) {
    ccInputError.value = 'Please enter a valid email address';
    return;
  }
  
  if (composerState.value.cc.includes(email)) {
    ccInputError.value = 'This email is already added';
    return;
  }
  
  composerState.value.cc.push(email);
  newCcRecipient.value = '';
  ccInputError.value = '';
};

// Function to remove CC recipient
const removeCcRecipient = (index: number) => {
  composerState.value.cc.splice(index, 1);
};

// Handle Enter key in CC input
const handleCcInputKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    addCcRecipient();
  }
};

// Clear CC input error when user starts typing and trim whitespace
const handleCcInputChange = () => {
  // Trim the input value
  newCcRecipient.value = newCcRecipient.value.trim();
  // Clear any existing error
  ccInputError.value = '';
};

// Watch for field visibility changes and adjust height
watch(() => composerState.value.minimizedFields, (isMinimized) => {
  if (isMinimized) {
    // Fields are hidden - use base height
    composerHeight.value = baseComposerHeight;
  } else {
    // Fields are shown - expand to accommodate them
    composerHeight.value = expandedComposerHeight;
  }
  
  // Save the new height to localStorage
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('email-composer-height', String(composerHeight.value));
  }
});
</script>

<template>
  <div class="flex flex-col h-full bg-white">
    <!-- Email skeleton loading state -->
    <div 
      v-if="isLoading"
      class="flex-1 overflow-y-auto"
    >
      <div class="mx-auto">
        <div class="divide-y divide-gray-100">
          <!-- Create 3 skeleton email messages -->
          <div v-for="n in 3" :key="`skeleton-${n}`" class="py-4">
            <div class="flex items-start gap-2 px-4">
                             <!-- Avatar skeleton -->
               <BravoSkeleton 
                 borderRadius="16px"
                 size="32px" 
                 class="flex-shrink-0"
               />
              
              <div class="flex-1 min-w-0">
                <div class="flex justify-between items-start">
                  <div class="flex flex-col gap-1">
                    <!-- Sender name skeleton -->
                    <BravoSkeleton 
                      width="120px" 
                      height="16px" 
                      border-radius="4px"
                    />
                  </div>
                  <div class="flex items-center gap-2">
                    <!-- Timestamp skeleton -->
                    <BravoSkeleton 
                      width="80px" 
                      height="14px" 
                      border-radius="4px"
                    />
                  </div>
                </div>
                
                <div class="mt-1">
                  <!-- "to" line skeleton -->
                  <BravoSkeleton 
                    width="160px" 
                    height="12px" 
                    border-radius="4px"
                  />
                </div>
                
                <!-- Message preview skeleton -->
                <div class="mt-2">
                  <BravoSkeleton 
                    width="85%" 
                    height="14px" 
                    border-radius="4px" 
                    class="mb-1"
                  />
                  <BravoSkeleton 
                    width="60%" 
                    height="14px" 
                    border-radius="4px"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <template v-else>
      <!-- Empty state -->
      <BravoZeroStateScreen
        v-if="!messages.length"
        title="Send New Email"
        message="Start a new email thread by choosing recipient(s) and creating a message."
        :showButton="false"
        :imageSrc="EmailZeroStateSvg"
        image-alt="Email Messages"
        :action-handler="() => {}"
        class="flex items-center justify-center h-full"
      />
      
      <!-- Email thread -->
      <div ref="messagesContainer" class="flex-1 overflow-y-auto">
        <div class="mx-auto">
          <!-- Message thread -->
          <div class="divide-y divide-gray-100">
            <template v-for="message in messages" :key="message.id">
              <div 
                class="py-4"
                :class="{ 'cursor-pointer hover:bg-gray-50': !isMessageExpanded(message.id) }"
                @click="!isMessageExpanded(message.id) && toggleMessage(message.id)"
              >
                <!-- Message header -->
                <div 
                  class="flex items-start gap-3 px-4 cursor-pointer"
                  @click.stop="toggleMessage(message.id)"
                >
                  <!-- Avatar -->
                  <BravoAvatar
                    :firstName="message.sender.name.split(' ')[0]"
                    :lastName="message.sender.name.split(' ')[1] || ''"
                    :backgroundColor="getAvatarColor(message.sender.email)"
                    :style="{ color: '#ffffff' }"
                    size="32"
                    class="flex-shrink-0"
                  />

                  <div class="flex-1 min-w-0">
                    <div class="flex justify-between items-start">
                      <div class="flex flex-col">
                        <span class="font-medium text-gray-900 leading-none">{{ message.sender.name }}</span>

                      </div>
                      <div class="flex items-center gap-2">
                        <BravoTimestamp 
                          :datetime="message.timestamp.toISOString()"
                          class="text-sm text-gray-500 whitespace-nowrap"
                        />
                      </div>
                    </div>
                    <div class="relative w-full">
                      <!-- Gmail-style: Show recipients when expanded, preview when minimized -->
                      <template v-if="isMessageExpanded(message.id)">
                        <div class="flex items-center space-x-1 max-w-full">
                          <span class="text-sm text-gray-500 mt-0.5 truncate inline-flex items-center max-w-[calc(100%-24px)]">
                            <span class="truncate">to {{ message.parsedContent?.context?.to || message.emailData?.to || message.parsedContent?.context?.to_name || 'Recipients' }}</span>
                          </span>
                          <button 
                            class="p-1 -m-1 text-gray-500 hover:text-gray-700 metadata-toggle flex-shrink-0"
                            @click.stop="toggleMetadata(message.id)"
                          >
                            <i class="pi pi-chevron-down text-xs"></i>
                          </button>
                        </div>
                      </template>
                      
                      <!-- Message preview when minimized -->
                      <template v-else>
                        <div class="text-gray-600 line-clamp-1 mt-0 pointer-events-none">
                          {{ htmlToPlainText(message.displayContent).substring(0, 150) }}
                        </div>
                      </template>
                      
                      <!-- Email metadata dropdown -->
                      <div 
                        v-if="showMetadata.has(message.id)"
                        class="absolute left-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 w-[500px] metadata-dropdown"
                        @click.stop
                      >
                        <div class="p-4 space-y-2 text-sm">
                          <div class="grid grid-cols-[80px_1fr] gap-2">
                            <span class="text-gray-500">from:</span>
                            <span>{{ message.sender.name }} &lt;{{ message.sender.email }}&gt;</span>
                            
                            <span class="text-gray-500">to:</span>
                            <span>{{ message.parsedContent?.context?.to || message.emailData?.to || message.parsedContent?.context?.to_name || 'Recipients' }}</span>
                            
                            <template v-if="message.emailData?.cc || message.parsedContent?.context?.cc">
                              <span class="text-gray-500">cc:</span>
                              <span>{{ message.emailData?.cc || message.parsedContent?.context?.cc }}</span>
                            </template>
                            
                            <span class="text-gray-500">date:</span>
                            <span>{{ formatDetailedDate(message.timestamp) }}</span>
                            
                            <span class="text-gray-500">subject:</span>
                            <span>{{ message.emailData?.subject || props.communication.title }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Expanded message -->
                <div 
                  v-if="isMessageExpanded(message.id)"
                  class="mt-2 pl-[60px] cursor-default"
                  @click.stop
                >
                  <!-- Parse content to separate new from quoted -->
                  <template v-if="separateEmailContent(message.displayContent).hasQuotedContent">
                    <!-- New content (always visible) -->
                    <div 
                      class="prose text-gray-800 max-w-full break-words"
                      v-html="separateEmailContent(message.displayContent).newContent"
                    ></div>
                    
                    <!-- Quoted content toggle button (three dots) -->
                    <div class="mt-3 mb-2">
                      <button 
                        @click="toggleQuotedContent(message.id)"
                        class="quoted-content-toggle inline-flex items-center justify-center w-8 h-5 hover:bg-gray-100 rounded-full transition-colors"
                        v-tooltip="isQuotedContentExpanded(message.id) ? 'Hide previous messages' : 'Show previous messages'"
                      >
                        <i class="pi pi-ellipsis-h" style="color: var(--icon-color-primary);"></i>
                      </button>
                    </div>
                    
                    <!-- Quoted content (collapsible) -->
                    <div 
                      v-if="isQuotedContentExpanded(message.id)"
                      class="border-l-3 border-gray-200 pl-4 ml-2"
                    >
                      <div 
                        class="prose prose-sm text-gray-600 max-w-full break-words"
                        v-html="separateEmailContent(message.displayContent).quotedContent"
                      ></div>
                    </div>
                  </template>
                  
                  <!-- If no quoted content, show original content -->
                  <template v-else>
                    <div 
                      class="prose text-gray-800 max-w-full break-words"
                      v-html="message.displayContent"
                    ></div>
                  </template>

                  <!-- Attachments -->
                  <div 
                    v-if="message.hasAttachment && message.attachmentData"
                    class="mt-4 space-y-2"
                  >
                    <div class="text-sm font-medium text-gray-500 mb-2">
                      Attachments (1)
                    </div>
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <i 
                        class="pi text-gray-400 text-xl"
                        :class="{
                          'pi-file-pdf': message.attachmentData.type === 'application/pdf',
                          'pi-image': message.attachmentData.type.startsWith('image/'),
                          'pi-file': !message.attachmentData.type.startsWith('image/') && message.attachmentData.type !== 'application/pdf'
                        }"
                      ></i>
                      <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">{{ message.attachmentData.name }}</div>
                        <div class="text-sm text-gray-500">{{ formatFileSize(message.attachmentData.size) }}</div>
                      </div>
                      <a 
                        :href="message.attachmentData.url"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="p-2 hover:bg-gray-100 rounded"
                        download
                      >
                        <i class="pi pi-download"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      
      <!-- Email composer - simplified -->
      <div 
        class="border-t bg-white px-4 pt-4 pb-2 relative composer-container"
        :style="{ height: `${composerHeight}px` }"
      >
        <!-- Composer resize handle -->
        <div
          class="composer-resize-handle"
          :class="{ resizing: isResizingComposer }"
          @mousedown="startComposerResize"
          data-testid="composer-resize-handle"
        >
          <div class="composer-resize-grabber"></div>
        </div>
        <!-- Attached files -->
        <div v-if="attachedFiles.length" class="mb-4">
          <div class="text-sm font-medium text-gray-500 mb-2">
            Attached Files ({{ attachedFiles.length }})
          </div>
          <div class="space-y-2">
            <div 
              v-for="file in attachedFiles"
              :key="file.id"
              class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
            >
              <!-- File preview/icon -->
              <div class="w-12 h-12 flex-shrink-0 rounded overflow-hidden bg-gray-200 flex items-center justify-center">
                <img 
                  v-if="file.preview" 
                  :src="file.preview" 
                  :alt="file.name"
                  class="w-full h-full object-cover"
                />
                <i 
                  v-else
                  class="text-gray-400 text-xl"
                  :class="{
                    'pi pi-file-pdf': file.type === 'application/pdf',
                    'pi pi-file-word': file.type.includes('word') || file.type.includes('document'),
                    'pi pi-file-excel': file.type.includes('sheet') || file.type.includes('excel'),
                    'pi pi-image': file.type.startsWith('image/'),
                    'pi pi-video': file.type.startsWith('video/'),
                    'pi pi-file': !file.type.startsWith('image/') && !file.type.startsWith('video/') && !file.type.includes('pdf') && !file.type.includes('word') && !file.type.includes('sheet')
                  }"
                ></i>
              </div>
              
              <!-- File info -->
              <div class="flex-1 min-w-0">
                <div class="font-medium truncate" :title="file.name">{{ file.name }}</div>
                <div class="text-sm text-gray-500">
                  {{ formatFileSize(file.size) }} • {{ getFileTypeLabel(file.type) }}
                </div>
              </div>
              
              <!-- Remove button -->
              <button 
                class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                @click="attachedFiles = attachedFiles.filter((f: any) => f.id !== file.id)"
                title="Remove file"
              >
                <i class="pi pi-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- File upload progress -->
        <div v-if="uploadingFiles.length" class="mb-4 space-y-2">
          <FileUploadProgress
            v-for="file in uploadingFiles"
            :key="file.id"
            :file-name="file.name"
            :size="file.size"
            :progress="file.progress"
          />
        </div>

        <!-- Email fields -->
        <div v-if="!composerState.minimizedFields" class="space-y-4 mb-4">
          <!-- TO field (read-only chips) -->
          <div class="flex items-start space-x-2">
            <span class="w-12 text-gray-500 pl-1 pt-1">To:</span>
            <div class="flex-1">
              <div v-if="composerState.to.length > 0" class="flex flex-wrap gap-1">
                <BravoChip
                  v-for="(email, index) in composerState.to" 
                  :key="`to-${index}`"
                  :label="email"
                  class="bg-blue-100 text-blue-800 text-xs px-2 py-1"
                />
              </div>
              <div v-else class="text-gray-400 text-sm py-1">
                No recipients
              </div>
            </div>
          </div>

          <!-- CC field (editable chips) -->
          <div class="flex items-start space-x-2">
            <span class="w-12 text-gray-500 pl-1 pt-1">CC:</span>
            <div class="flex-1">
              <!-- Existing CC chips -->
              <div v-if="composerState.cc.length > 0" class="flex flex-wrap gap-1 mb-2">
                <BravoChip 
                  v-for="(email, index) in composerState.cc" 
                  :key="`cc-${index}`"
                  :label="email"
                  removable
                  @remove="removeCcRecipient(index)"
                  class="bg-gray-100 text-gray-800 text-xs px-2 py-1"
                />
              </div>
              
              <!-- Add new CC input -->
              <div class="space-y-1">
                <InputText
                  v-model="newCcRecipient"
                  placeholder="Add CC recipient"
                  class="w-full"
                  :class="{ 'p-invalid': ccInputError }"
                  @keydown="handleCcInputKeydown"
                  @input="handleCcInputChange"
                />
                <div v-if="ccInputError" class="text-red-500 text-xs">
                  {{ ccInputError }}
                </div>
                <div class="text-xs text-gray-500">
                  Press Enter to add recipient
                </div>
              </div>
            </div>
          </div>

          <!-- Subject field -->
          <div class="flex items-center space-x-2">
            <span class="w-12 text-gray-500 pl-1 mr-3">Subject:</span>
            <input 
              type="text" 
              v-model="composerState.subject"
              class="flex-1 border-0 border-b border-gray-200 px-0 py-1 ml-1 focus:ring-0 focus:border-blue-500 text-[#282A2C]"
              placeholder="Email subject"
            >
          </div>
        </div>
        
        <!-- Editor and content area -->
        <div class="bg-white relative flex-1 flex flex-col composer-content">
          <Editor
            v-model="composerState.content"
            :editorStyle="{ height: '100%', 'font-family': 'inherit' }"
            :disabled="isGeneratingReply || isReformatting"
            ref="editorRef"
            :class="{ 'toolbar-visible': showEditorToolbar }"
            class="email-editor flex-1"
            placeholder="Compose your email..."
          >
            <template v-slot:toolbar>
              <span class="ql-formats">
                <select class="ql-header" v-tooltip.bottom="'Headers'">
                  <option selected></option>
                  <option value="1"></option>
                  <option value="2"></option>
                  <option value="3"></option>
                </select>
              </span>
              <span class="ql-formats">
                <button class="ql-bold" v-tooltip.bottom="'Bold'"></button>
                <button class="ql-italic" v-tooltip.bottom="'Italic'"></button>
                <button class="ql-underline" v-tooltip.bottom="'Underline'"></button>
                <button class="ql-strike" v-tooltip.bottom="'Strikethrough'"></button>
              </span>
              <span class="ql-formats">
                <select class="ql-color" v-tooltip.bottom="'Text Color'"></select>
                <select class="ql-background" v-tooltip.bottom="'Background Color'"></select>
              </span>
              <span class="ql-formats">
                <button class="ql-list" value="ordered" v-tooltip.bottom="'Numbered List'"></button>
                <button class="ql-list" value="bullet" v-tooltip.bottom="'Bullet List'"></button>
              </span>
              <span class="ql-formats">
                <select class="ql-align" v-tooltip.bottom="'Text Alignment'">
                  <option selected></option>
                  <option value="center"></option>
                  <option value="right"></option>
                  <option value="justify"></option>
                </select>
              </span>
              <span class="ql-formats">
                <button class="ql-link" v-tooltip.bottom="'Insert Link'"></button>
                <button class="ql-image" v-tooltip.bottom="'Insert Image'"></button>
                <button class="ql-video" v-tooltip.bottom="'Insert Video'"></button>
              </span>
            </template>
          </Editor>
          
          <!-- Loading overlay for reply generation -->
          <div 
            v-if="isGeneratingReply || isReformatting"
            class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10"
          >
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <div class="mt-2 text-gray-600 text-sm">
                {{ isGeneratingReply ? 'Generating reply...' : 'Reformatting response...' }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Sticky bottom toolbar -->
        <div class="composer-toolbar bg-white border-t border-gray-200 px-4 py-2 flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <BravoButton
              icon="pi pi-align-left"
              text
              severity="secondary"
              @click="showEditorToolbar = !showEditorToolbar"
              v-tooltip.bottom="'Formatting Options'"
            />
            <BravoButton
              icon="pi pi-paperclip"
              text
              severity="secondary"
              @click="($refs.fileInput as HTMLInputElement)?.click()"
              v-tooltip.bottom="'Attach File'"
            />
            <Dropdown
              v-model="selectedTemplateValue"
              :options="templateOptions"
              optionLabel="label"
              optionValue="value"
              placeholder=""
              class="quick-reply-dropdown"
              @change="applyTemplate"
              :filter="false"
              :showClear="false"
              :loading="loadingCannedResponses"
              :disabled="false"
              v-tooltip.bottom="'Quick Replies'"
            >
              <template #value>
                <div class="flex items-center">
                  <i class="pi pi-comments"></i>
                </div>
              </template>
              <template #option="slotProps">
                <div class="flex items-center">
                  <i class="pi pi-file mr-2 text-gray-400"></i>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </Dropdown>
            <Dropdown
              v-model="selectedAiAction"
              :options="aiActions"
              optionLabel="label"
              optionValue="value"
              placeholder=""
              class="ai-assistant-dropdown"
              @change="handleAiAction"
              :filter="false"
              :showClear="false"
              v-tooltip.bottom="'AI Assistant'"
            >
              <template #value>
                <div class="flex items-center">
                  <i class="pi pi-sparkles"></i>
                </div>
              </template>
              <template #option="slotProps">
                <div class="flex items-center">
                  <i :class="slotProps.option.icon" class="mr-2 text-gray-400"></i>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </Dropdown>
            <BravoButton
              :label="composerState.minimizedFields ? 'Show Fields' : 'Hide Fields'"
              text
              severity="secondary"
              size="small"
              @click="composerState.minimizedFields = !composerState.minimizedFields"
              v-tooltip.bottom="'Toggle To and Subject fields'"
            />
          </div>
          <BravoButton
            label="Send"
            size="small"
            severity="primary"
            :disabled="(!composerState.content.trim() && attachedFiles.length === 0) || isSendingEmail"
            :loading="isSendingEmail"
            @click="sendEmail"
          />
        </div>
        
        <input
          ref="fileInput"
          type="file"
          multiple
          class="hidden"
          @change="handleFileUpload"
        >
        
        <!-- Link Dialog -->
        <Dialog
          v-model:visible="showLinkDialog"
          modal
          header="Insert Link"
          :style="{ width: '400px' }"
          :closable="true"
        >
          <div class="space-y-4 p-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Text to Link
              </label>
              <div class="text-gray-600 bg-gray-50 p-2 rounded">
                {{ selectedText }}
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                URL
              </label>
              <input
                type="url"
                v-model="linkUrl"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://"
                @keydown.enter="insertLink"
              >
            </div>
          </div>
          <template #footer>
            <div class="flex justify-end gap-2">
              <button
                class="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
                @click="showLinkDialog = false"
              >
                Cancel
              </button>
              <button
                class="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700"
                @click="insertLink"
                :disabled="!linkUrl.trim()"
              >
                Insert
              </button>
            </div>
          </template>
        </Dialog>

        <!-- Summary Dialog -->
        <Dialog
          v-model:visible="showSummaryDialog"
          modal
          header="Conversation Summary"
          :style="{ width: '600px' }"
          :closable="true"
        >
          <div class="p-4">
            <div v-if="isSummarizing" class="flex flex-col items-center justify-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <div class="mt-4 text-gray-600">
                Generating summary...
              </div>
            </div>
            <div 
              v-else
              class="prose max-w-none"
              v-html="summaryContent"
            ></div>
          </div>
          <template #footer>
            <div class="flex justify-end">
              <button
                class="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
                @click="showSummaryDialog = false"
              >
                Close
              </button>
            </div>
          </template>
        </Dialog>
      </div>
    </template>
  </div>
</template>

<style scoped>
.email-metadata-dropdown {
  min-width: 500px;
  max-width: calc(100vw - 2rem);
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(.prose) {
  max-width: none;
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

:deep(.prose p) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

:deep(.prose p:first-child) {
  margin-top: 0;
}

:deep(.prose p:last-child) {
  margin-bottom: 0;
}

/* Ensure proper line height for readable content */
:deep(.prose p:empty) {
  margin: 0.5em 0;
  height: 1em;
  display: block;
}

/* Hide empty paragraphs at the beginning - these are usually editor artifacts */
:deep(.prose p:first-child:empty) {
  display: none;
  margin: 0;
  height: 0;
}

/* Target only plain text elements for font standardization, preserve rich HTML formatting */
:deep(.prose p:not([style*="font-size"]):not([style*="font-family"])),
:deep(.prose div:not([class]):not([style]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

/* Ensure plain text spans inherit proper font (but preserve styled spans) */
:deep(.prose span:not([style*="font-size"]):not([style*="color"]):not([style*="font-family"]):not([class]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

:deep(.prose img) {
  max-width: 100%;
  height: auto;
}

:deep(.prose a) {
  color: #2563eb;
  text-decoration: underline;
}

:deep(.prose ul),
:deep(.prose ol) {
  padding-left: 1.5em;
  margin: 0.25em 0;
  list-style-position: outside;
}

:deep(.prose ul) {
  list-style-type: disc;
}

:deep(.prose ol) {
  list-style-type: decimal;
}

:deep(.prose ul ul) {
  list-style-type: circle;
  margin: 0;
}

:deep(.prose ol ol) {
  list-style-type: lower-alpha;
  margin: 0;
}

:deep(.prose li) {
  margin: 0.125em 0;
  padding-left: 0.25em;
}

/* Remove excessive spacing from common email elements - but preserve intentional line breaks */
:deep(.prose br + br + br) {
  display: none; /* Hide only when there are 3+ consecutive <br> tags */
}

:deep(.prose div:empty) {
  display: none;
}

/* Don't hide empty paragraphs - they might be intentional line breaks */

/* Ensure single and double line breaks are preserved */
:deep(.prose br) {
  display: block;
  content: "";
  margin: 0.125em 0;
}

:deep(.prose blockquote:not([style*="font-family"])) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  color: #4b5563;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
}

/* Ensure headers in email content use consistent Inter font while preserving semantic sizing */
:deep(.prose h1:not([style*="font-family"])),
:deep(.prose h2:not([style*="font-family"])),
:deep(.prose h3:not([style*="font-family"])),
:deep(.prose h4:not([style*="font-family"])),
:deep(.prose h5:not([style*="font-family"])),
:deep(.prose h6:not([style*="font-family"])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* Ensure strong/bold and emphasis maintain consistent font while preserving their styling */
:deep(.prose strong:not([style*="font-family"])),
:deep(.prose b:not([style*="font-family"])),
:deep(.prose em:not([style*="font-family"])),
:deep(.prose i:not([style*="font-family"])) {
  font-family: inherit;
}

/* Preserve table formatting and rich HTML email layouts */
:deep(.prose table),
:deep(.prose td),
:deep(.prose th),
:deep(.prose tr) {
  /* Don't override table styling - let rich HTML emails maintain their layout */
  font-family: inherit;
  font-size: inherit;
}

/* Preserve any elements with background colors, borders, or custom styling */
:deep(.prose [style*="background"]),
:deep(.prose [style*="border"]),
:deep(.prose [style*="padding"]),
:deep(.prose [style*="margin"]),
:deep(.prose [bgcolor]),
:deep(.prose [color]) {
  /* These are likely intentionally styled elements - preserve them */
  font-family: inherit;
  font-size: inherit;
}

:deep(.p-dropdown) {
  @apply border border-gray-200 rounded-lg shadow-lg;
  min-width: 200px;
}

:deep(.p-dropdown-item) {
  @apply px-4 py-2;
}

:deep(.p-dialog-header) {
  @apply py-4 px-6 border-b border-gray-200;
}

:deep(.p-dialog-content) {
  @apply p-0;
}

:deep(.p-dialog-footer) {
  @apply p-4 border-t border-gray-200;
}

:deep(.email-editor) {
  .ql-toolbar {
    display: none;
    border-bottom: 1px solid #e5e7eb;
  }

  &.toolbar-visible .ql-toolbar {
    display: block;
   }

  .ql-container {
    border: none !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .ql-editor {
    padding: 0.5rem;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: var(--text-color-primary) !important;
    border: none !important;
    outline: none !important;
    height: 100% !important;
    min-height: 100% !important;
    overflow-y: auto !important;
  }

  /* Ensure placeholder text is visible */
  .ql-editor.ql-blank::before {
    color: var(--text-color-secondary) !important;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: 14px !important;
    font-style: normal !important;
    font-weight: normal !important;
    content: attr(data-placeholder) !important;
    pointer-events: none !important;
    position: absolute !important;
    left: 8px !important;
    top: 8px !important;
  }

  /* Alternative approach - style the placeholder directly */
  .ql-editor::before {
    color: var(--text-color-secondary) !important;
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: 14px !important;
    font-style: normal !important;
  }
  
  /* Remove all borders from the editor wrapper */
  border: none !important;
  outline: none !important;
}

/* Quoted content styling */
.border-l-3 {
  border-left-width: 3px;
}

.quoted-content-toggle {
  transition: all 0.2s ease;
}

.quoted-content-toggle:hover {
  background-color: #f9fafb;
}

/* Quoted text styling */
:deep(.prose-sm) {
  font-size: 1rem;
  line-height: 1.5;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* Target only plain text elements in quoted content, preserve rich formatting */
:deep(.prose-sm p:not([style*="font-size"]):not([style*="font-family"])),
:deep(.prose-sm div:not([class]):not([style]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

:deep(.prose-sm span:not([style*="font-size"]):not([style*="color"]):not([style*="font-family"]):not([class]):not([id])) {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

:deep(.prose-sm p) {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

:deep(.prose-sm p:first-child) {
  margin-top: 0;
}

:deep(.prose-sm p:last-child) {
  margin-bottom: 0;
}

/* Composer container styling */
.composer-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Composer content area - allows scrolling */
.composer-content {
  overflow: hidden;
}

/* Composer toolbar - stays at bottom */
.composer-toolbar {
  flex-shrink: 0;
  margin-left: -1rem;
  margin-right: -1rem;
  margin-bottom: -0.5rem;
}

/* Composer resize handle */
.composer-resize-handle {
  position: absolute;
  top: -8px;
  left: 0;
  right: 0;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: row-resize;
  z-index: 10;
}

.composer-resize-grabber {
  width: 60px;
  height: 4px;
  border-radius: 2px;
  background-color: #ddd;
  position: relative;
  transition: background-color 0.2s;
}

.composer-resize-handle:hover .composer-resize-grabber,
.composer-resize-handle.resizing .composer-resize-grabber {
  background-color: var(--primary-color, #3b82f6);
}

/* Add style for the body while resizing composer */
:global(body.resizing-composer) {
  cursor: row-resize !important;
  user-select: none;
}

/* Quick Reply Dropdown Styling */
:deep(.quick-reply-dropdown) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: auto !important;
  min-width: auto !important;
}

/* Target the specific dropdown element */
:deep(.quick-reply-dropdown .p-select-dropdown) {
  width: 0 !important;
}

:deep(.quick-reply-dropdown .p-dropdown-trigger) {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 4px !important;
  width: auto !important;
  display: none !important; /* Hide the caret icon */
}

/* Additional selectors to ensure caret is hidden */
:deep(.quick-reply-dropdown .p-dropdown-trigger-icon) {
  display: none !important;
}

:deep(.quick-reply-dropdown .p-icon) {
  display: none !important;
}

:deep(.quick-reply-dropdown .pi-chevron-down) {
  display: none !important;
}

:deep(.quick-reply-dropdown .p-dropdown-label) {
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: auto !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover) {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover .p-dropdown-label) {
  background-color: transparent !important;
}

:deep(.quick-reply-dropdown:not(.p-disabled):hover .p-dropdown-trigger) {
  background-color: transparent !important;
}

:deep(.quick-reply-dropdown.p-focus) {
  box-shadow: none !important;
  border: none !important;
  background-color: #f3f4f6 !important;
  border-radius: 6px !important;
}

/* AI Assistant Dropdown Styling - Same as Quick Reply */
:deep(.ai-assistant-dropdown) {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: auto !important;
  min-width: auto !important;
}

:deep(.ai-assistant-dropdown .p-select-dropdown) {
  width: 0 !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-trigger) {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 4px !important;
  width: auto !important;
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-trigger-icon) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-icon) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .pi-chevron-down) {
  display: none !important;
}

:deep(.ai-assistant-dropdown .p-dropdown-label) {
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  width: auto !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover) {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover .p-dropdown-label) {
  background-color: transparent !important;
}

:deep(.ai-assistant-dropdown:not(.p-disabled):hover .p-dropdown-trigger) {
  background-color: transparent !important;
}

:deep(.ai-assistant-dropdown.p-focus) {
  box-shadow: none !important;
  border: none !important;
  background-color: #f3f4f6 !important;
  border-radius: 6px !important;
}
</style>