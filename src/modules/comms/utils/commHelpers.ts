/**
 * Communication utility helper functions
 */

// Color palette for avatar backgrounds (consistent with BravoAvatar's palette)
export const avatarColorPalette = [
  '#0078E6',
  '#32A051', 
  '#F0A202',
  '#F47536',
  '#DB3C4C',
  '#6D6CC6',
  '#759ABC'
];

/**
 * Generate consistent color for an identifier (email address, user ID, etc.)
 * Always returns the same color for the same input string
 */
export const getAvatarColor = (identifier: string): string => {
  if (!identifier) return avatarColorPalette[0]; // Default to first color
  
  // Use identifier to generate consistent color
  const hash = identifier.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const colorIndex = hash % avatarColorPalette.length;
  return avatarColorPalette[colorIndex];
};

/**
 * Converts HTML content to clean plain text
 * - Properly decodes HTML entities (like &nbsp;, &amp;, etc.)
 * - Strips all HTML tags
 * - Normalizes whitespace
 * - Converts line breaks to spaces for proper text flow
 */
export const htmlToPlainText = (html: string): string => {
  if (!html) return '';
  
  // First, replace HTML line break elements with spaces to ensure proper word separation
  const processedHtml = html
    // Replace <br>, <br/>, <br /> with spaces
    .replace(/<br\s*\/?>/gi, ' ')
    // Replace </p>, </div>, </h1-6> etc. with spaces to separate content blocks
    .replace(/<\/(p|div|h[1-6]|li|tr|td|th)>/gi, ' ')
    // Replace opening block elements with spaces (in case they don't have closing tags)
    .replace(/<(p|div|h[1-6]|li|tr|td|th)[^>]*>/gi, ' ');
  
  // Create a temporary DOM element to parse HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = processedHtml;
  
  // Get text content (this automatically decodes HTML entities)
  let text = tempDiv.textContent || tempDiv.innerText || '';
  
  // Normalize whitespace - replace multiple spaces/newlines with single spaces
  text = text.replace(/\s+/g, ' ');
  
  // Trim and return
  return text.trim();
};

/**
 * Separates email content into new content and quoted content
 * Identifies common email quoting patterns and splits the content
 */
export const separateEmailContent = (htmlContent: string): {
  newContent: string;
  quotedContent: string;
  hasQuotedContent: boolean;
} => {
  if (!htmlContent) {
    return {
      newContent: '',
      quotedContent: '',
      hasQuotedContent: false
    };
  }

  // Common patterns that indicate quoted content
  const quotingPatterns = [
    // "On [date], [person] wrote:" pattern
    /<div[^>]*>[\s]*On\s+.*?wrote:[\s\S]*$/i,
    /On\s+.*?wrote:[\s\S]*$/i,
    
    // Gmail style quoted content
    /<div[^>]*class=[^>]*gmail_quote[^>]*>[\s\S]*$/i,
    
    // Outlook style quoted content
    /<div[^>]*class=[^>]*OutlookMessageHeader[^>]*>[\s\S]*$/i,
    
    // Generic "From:" headers
    /<div[^>]*>[\s]*From:[\s\S]*$/i,
    /From:[\s\S]*$/i,
    
    // Forwarded message patterns
    /<div[^>]*>[\s]*-+\s*Forwarded message[\s\S]*$/i,
    /-+\s*Forwarded message[\s\S]*$/i,
    
    // Original message patterns
    /<div[^>]*>[\s]*-+\s*Original Message[\s\S]*$/i,
    /-+\s*Original Message[\s\S]*$/i,

    // Blockquote elements (common for quoted content)
    /<blockquote[\s\S]*$/i,
    
    // Lines starting with ">" (plain text quoting)
    /^[\s]*&gt;.*$/m
  ];

  let newContent = htmlContent;
  let quotedContent = '';
  let hasQuotedContent = false;

  // Try each pattern to find where quoted content begins
  for (const pattern of quotingPatterns) {
    const match = htmlContent.match(pattern);
    if (match) {
      const splitIndex = match.index || 0;
      newContent = htmlContent.substring(0, splitIndex).trim();
      quotedContent = htmlContent.substring(splitIndex).trim();
      hasQuotedContent = true;
      break;
    }
  }

  // If no quoted content found, check for common div separators
  if (!hasQuotedContent) {
    // Look for div elements that might contain quoted content
    const divPattern = /<div[^>]*class=[^>]*(?:quoted|reply|original)[^>]*>[\s\S]*$/i;
    const divMatch = htmlContent.match(divPattern);
    if (divMatch) {
      const splitIndex = divMatch.index || 0;
      newContent = htmlContent.substring(0, splitIndex).trim();
      quotedContent = htmlContent.substring(splitIndex).trim();
      hasQuotedContent = true;
    }
  }

  // Clean up the content
  newContent = newContent.replace(/(<br\s*\/?>\s*){3,}$/i, '').trim();
  
  return {
    newContent: newContent || htmlContent,
    quotedContent,
    hasQuotedContent: quotedContent.length > 0
  };
}; 