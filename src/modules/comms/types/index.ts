export type CommunicationType = 'email' | 'chat' | 'sms' | 'voice';

export interface ServiceConfig {
  websocket_host: string;
  bosh_host: string;
  jid: string;
  password: string;
  resource: string;
  relay_jid: string;
  relay_username: string;
  relay_avatar: string;
  service_name: string;
  pubsub_service: string;
  pubsub_node: string;
}

export interface AppConfig {
  clientInstance: string;
  csrfToken: string;
  serviceConfig: ServiceConfig;
  requestId: string;
  availableComm: AvailableComm[];
  members_locations_id: string;
  members_users_id: string;
  members_id: string;
}

export interface AvailableComm {
  id: string;
  object: string;
  object_id: string;
  object_scope: string;
  comm_type: number;
  comm_label: string;
  comm_status: number;
  comm_state: number;
  object_source: string;
  billing_status: number;
  title: string;
  subtitle: string;
  duration_plus: number;
  duration: number;
  message_count: number;
  user_message_cnt: Record<string, number>;
  external_rpid: string;
  external_lpid: string;
  external_id: string;
  external_status: string;
  created: string;
  updated: string;
  occupied: string;
  completed: string;
  c__status: string;
  c__state: string;
  c__avatar: string;
  unread_count: number;
  participants?: APIParticipant[];
}

export interface APIParticipant {
  object: string;
  object_id: string;
  name: string;
  alias: string;
  external_id: string;
  host: boolean;
  eligible: boolean;
  id: string;
  presence: boolean;
}

export interface Communication {
  id: string;
  type: CommunicationType;
  title: string;
  participants: Participant[];
  messages: Message[];
  isMinimized?: boolean;
  customerTyping?: boolean;
  botTyping?: boolean;
  roomJid?: string;
  availableComm?: AvailableComm;
  unreadCount?: number;
}

export interface Participant {
  id: string;
  name: string;
  alias?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  type: 'internal' | 'external';
  external_id?: string;
  presence?: boolean;
}

// XMPP Message Context (from notification object)
export interface XMPPMessageContext {
  from: string;
  from_name: string;
  from_nickname?: string;
  from_avatar?: string;
  to: string;
  to_name: string;
  to_nickname?: string;
  to_user?: string | null;
  to_avatar?: string;
  cc?: string;
  bcc?: string;
}

// Message Actions (for bot interactions)
export interface MessageAction {
  key: string;
  lbl: string;
  uri: string;
  selected?: boolean;
}

export interface MessageRatingAction {
  actUri: string;
  commId: string;
  fromResource: string;
  messageId: string;
  messageBody: string;
  lbl: string;
  selected?: boolean;
}

// Attachment information
export interface MessageAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  preview?: string;
  url: string;
}

// Knowledge Base content
export interface MessageKB {
  id: string;
  title: string;
  preview: string;
  preview_length: number;
  owner_partner_id: string;
  partner_ids: string[];
  perms: string;
}

// Email specific data
export interface MessageEmail {
  subject?: string;
  body?: string;
  cc?: string;
  bcc?: string;
  to?: string;
  from?: string;
  from_name?: string;
}

// Parsed XMPP notification object
export interface XMPPNotificationObject {
  timestamp?: string;
  ttl?: null;
  message?: string;
  context?: XMPPMessageContext;
  attachment?: MessageAttachment;
  attachments?: MessageAttachment[];
  email?: MessageEmail;
  kb?: MessageKB;
  revision?: null;
  actions?: MessageAction[];
  secret?: string;
  channel?: string;
  service?: string;
  notificationsEnable?: boolean;
  hideMessage?: boolean;
  rating?: 'Up' | 'Down';
}

// Enhanced Message interface
export interface Message {
  id: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'html' | 'file' | 'system' | 'transcription' | 'email' | 'external';
  
  // Basic message properties
  isExternal?: boolean;
  isEmail?: boolean;
  isMine?: boolean;
  hideMessage?: boolean;
  
  // Sender information
  nickname?: string;
  fullname?: string;
  fromLabel?: string;
  avatar?: string;
  fromResource?: string;
  
  // Service/Channel information
  service?: string;
  channel?: string;
  serviceAvatar?: string;
  viaServiceLabel?: string;
  externalFrom?: string;
  
  // Attachment information
  hasAttachment?: boolean;
  hasImage?: boolean;
  attachmentName?: string;
  attachmentSize?: string;
  attachmentType?: string;
  attachmentPreview?: string;
  attachmentUrl?: string;
  isEmailAttachment?: boolean;
  
  // Email specific
  emailJson?: string;
  subject?: string;
  cc?: string;
  
  // Knowledge Base
  hasKb?: boolean;
  kbId?: string;
  kbTitle?: string;
  kbPreview?: string;
  kbPreviewLength?: number;
  kbOwnerPartnerId?: string;
  kbPartnerIds?: string[];
  kbPerms?: string;
  
  // Actions and interactions
  actions?: MessageAction[];
  ratingActions?: MessageRatingAction[];
  
  // Raw data
  raw?: string;
  notificationObj?: XMPPNotificationObject;
  stanza?: any;
  
  // Legacy metadata support
  metadata?: {
    fileName?: string;
    fileSize?: number;
    fileUrl?: string;
    emailSubject?: string;
    emailTo?: string[];
    emailCc?: string[];
    emailBcc?: string[];
  };
}

// Typing indicator information
export interface TypingUser {
  name: string;
  nickName?: string;
  avatar?: string;
}

export interface ComposerState {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject?: string;
  content: string;
  expanded: boolean;
  minimizedFields: boolean;
}

// Message parsing utilities
export interface ParsedMessageContent {
  isParsed: boolean;
  isPlainText?: boolean;
  text?: string;
  data?: XMPPNotificationObject;
}

// Constants
export const END_CHAT_MESSAGE_SECRET = 'MessengerUserExit';

// Helper functions for message parsing
export function tryParseMessageContent(content: string): ParsedMessageContent {
  try {
    const parsed = JSON.parse(content);
    if (typeof parsed === 'object' && parsed !== null) {
      return { isParsed: true, data: parsed };
    }
    return { isParsed: false, isPlainText: true, text: content };
  } catch {
    return { isParsed: false, isPlainText: true, text: content };
  }
}

export function isEndChatMessage(notificationObj?: XMPPNotificationObject): boolean {
  return notificationObj?.secret === END_CHAT_MESSAGE_SECRET;
}

export function shouldHideMessage(notificationObj?: XMPPNotificationObject): boolean {
  return !!(notificationObj?.hideMessage || isEndChatMessage(notificationObj));
}

export function formatFileSize(bytes: number, precision?: number): string {
  const units = [
    { UNIT: 'TB', VALUE: Math.pow(1024, 4) },
    { UNIT: 'GB', VALUE: Math.pow(1024, 3) },
    { UNIT: 'MB', VALUE: Math.pow(1024, 2) },
    { UNIT: 'KB', VALUE: 1024 },
    { UNIT: 'B', VALUE: 1 }
  ];

  for (const unit of units) {
    if (bytes >= unit.VALUE) {
      const result = bytes / unit.VALUE;
      const finalPrecision = typeof precision === 'number' 
        ? precision 
        : unit.UNIT === 'B' ? 0 : result > 99 ? 1 : 2;
      return `${result.toFixed(finalPrecision)} ${unit.UNIT}`;
    }
  }
  return '0 B';
}

export function getUserBaseId(id: string): string {
  return id.split(';')[0];
}

export function getResourceFromId(id: string): string {
  return id.split(':')[1] || id;
}