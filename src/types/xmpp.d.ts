declare module '@xmpp/client' {
  export function client(options: any): any
  export function xml(name: string, attrs?: any, ...children: any[]): any
}

declare module '@xmpp/debug' {
  export default function debug(xmpp: any): void;
}

declare module 'crypto-js' {
  export const MD5: any;
  export default any;
  export function randomUUID(): string
}

declare module 'stanza.io' {
  export function createClient(options: any): any
  export default {
    createClient: (options: any) => any
  }
} 