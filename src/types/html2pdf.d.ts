declare module 'html2pdf.js' {
    interface Html2PdfOptions {
        margin?: number | number[];
        filename?: string;
        image?: {
            type?: string;
            quality?: number;
        };
        enableLinks?: boolean;
        html2canvas?: {
            scale?: number;
            useCORS?: boolean;
            logging?: boolean;
            letterRendering?: boolean;
            [key: string]: any;
        };
        jsPDF?: {
            unit?: string;
            format?: string;
            orientation?: 'portrait' | 'landscape';
            compress?: boolean;
            [key: string]: any;
        };
        pagebreak?: {
            mode?: string[];
            [key: string]: any;
        };
        footer?: {
            height?: string;
            contents?: {
                default?: string;
                [key: string]: string | undefined;
            };
            [key: string]: any;
        };
        [key: string]: any;
    }

    interface Html2PdfInstance {
        from(element: HTMLElement | string): Html2PdfInstance;
        set(options: Html2PdfOptions): Html2PdfInstance;
        save(): Promise<void>;
        outputPdf(): any;
        outputImg(): any;
    }

    function html2pdf(): Html2PdfInstance;
    function html2pdf(element: HTMLElement | string, options?: Html2PdfOptions): Html2PdfInstance;

    export default html2pdf;
}
