import { BaseResponse, FilterParam, PaginatedResponse, PaginationParams } from './BaseContract';

/**
 * Knowledge Node structure for the knowledge tree
 */
export interface KnowledgeNode {
    id: string;
    text: string;
    leaf: boolean;
    expanded?: boolean;
    children?: KnowledgeNode[];
    iconCls?: string;
    qtip?: string;
}

/**
 * Knowledge Item representation
 */
export interface KnowledgeItem {
    id: string;
    title: string;
    content: string;
    type?: string;
    status?: string;
    access?: string;
    updatedAt?: string;
    tags?: string[];
    body?: string;
    metadata?: {
        created_at?: string;
        updated_at?: string;
        tags?: string[];
        author?: string;
        category?: string;
    };
}

/**
 * Revision of a knowledge item
 */
export interface KnowledgeRevision {
    id: string;
    body: string;
}

/**
 * Knowledge filter parameters
 */
export interface KnowledgeFilterParams {
    filter?: FilterParam[];
    node?: string;
}

/**
 * Knowledge listing parameters
 */
export interface KnowledgeListingParams extends PaginationParams {
    filter?: FilterParam[];
}

/**
 * Response for fetching the knowledge tree
 */
export interface KnowledgeTreeResponse extends BaseResponse {
    children?: KnowledgeNode[];
}

/**
 * Response for fetching a single knowledge item
 */
export interface KnowledgeItemResponse extends BaseResponse {
    data?: KnowledgeItem;
}

/**
 * Response for listing knowledge items
 */
export interface KnowledgeListingResponse extends BaseResponse {
    data?: {
        items: KnowledgeItem[];
        total: number;
    };
    kb?: {
        results: KnowledgeItem[];
        total: number;
    };
}

/**
 * Result of a knowledge listing operation
 */
export interface KnowledgeListingResult {
    items: KnowledgeItem[];
    totalCount: number;
}

/**
 * Response for knowledge revisions operations
 */
export interface KnowledgeRevisionResponse extends BaseResponse {
    data?: any;
}

/**
 * Knowledge API Contract
 */
export interface KnowledgeContract {
    /**
     * Fetch the knowledge tree structure
     * @param params Knowledge tree filter parameters
     * @returns Promise with an array of KnowledgeNode objects
     */
    fetchKnowledgeTree(params?: KnowledgeFilterParams): Promise<KnowledgeNode[]>;

    /**
     * Fetch a specific knowledge item by ID
     * @param id The ID of the knowledge item to fetch
     * @returns Promise with the knowledge response
     */
    fetchKnowledgeItem(id: string): Promise<KnowledgeItemResponse>;

    /**
     * Fetch a list of knowledge items
     * @param params Listing parameters including pagination and filters
     * @returns Promise with the listing result
     */
    fetchKnowledgeListing(params?: KnowledgeListingParams): Promise<KnowledgeListingResult>;

    /**
     * Update knowledge item revisions
     * @param revisions Array of knowledge revisions to update
     * @returns Promise with the revision response
     */
    putRevisions(revisions: KnowledgeRevision[]): Promise<KnowledgeRevisionResponse>;
}
