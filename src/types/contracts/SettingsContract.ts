import type { BaseResponse, PaginationParams } from './BaseContract';

/**
 * View representation
 */
export interface View {
    id: string;
    label: string;
}

/**
 * Response for settings operations
 */
export interface SettingsResponse extends BaseResponse {
    relay_views?: {
        results: View[];
        total?: number;
    };
}

/**
 * Parameters for fetching views
 */
export type ViewsParams = PaginationParams;

/**
 * Settings API Contract
 */
export interface SettingsContract {
    /**
     * Fetch available views
     * @param params Optional parameters for pagination
     * @returns Promise with an array of views
     */
    fetchViews(params?: ViewsParams): Promise<View[]>;

    // Additional methods can be added as they are implemented in the API
}
