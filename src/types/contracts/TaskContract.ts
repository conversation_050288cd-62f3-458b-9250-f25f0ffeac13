import type { BaseResponse } from './BaseContract';

/**
 * Task representation
 * Note: This is a simplified representation that may need to be expanded
 * based on the actual Task structure in the application
 */
export interface Task {
    id: string;
    title: string;
    description?: string;
    status?: string;
    dueDate?: string;
    assignedTo?: string;
    priority?: string;
    orgId: string;
    userId?: string;
    teamId?: string;
    // Additional fields can be added as needed
}

/**
 * Service error representation
 */
export interface ServiceError {
    name: string;
    message: string;
    stack?: string;
    context?: any;
}

/**
 * Service response wrapper
 */
export interface ServiceResponse<T> {
    success: boolean;
    data: T;
    error?: ServiceError;
}

/**
 * Parameters for fetching tasks
 */
export interface FetchTasksParams {
    orgId: string;
    userId?: string;
    teamId?: string;
}

/**
 * Task Service Contract
 */
export interface TaskContract {
    /**
     * Retrieves tasks assigned to a specific user
     * @param params Parameters including orgId and userId
     * @returns Promise resolving to user's tasks
     */
    fetchUserTasks(params: FetchTasksParams): Promise<ServiceResponse<Task[]>>;

    /**
     * Retrieves tasks assigned to a team
     * @param params Parameters including orgId and teamId
     * @returns Promise resolving to team's tasks
     */
    fetchTeamTasks(params: FetchTasksParams): Promise<ServiceResponse<Task[]>>;
}
