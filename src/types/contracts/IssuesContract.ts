import type { BaseResponse, FilterParam, PaginationParams, SortParam } from './BaseContract';

/**
 * Case status type
 */
export type CaseStatus = 'new' | 'ready' | 'active' | 'closed';

/**
 * Issue representation
 */
export interface Issue {
    id: string;
    display_name: string;
    reference_num?: string;
    availableComm: any[]; // This can be further refined when actual data structure is better known
    source_data?: {
        source: string;
    };
    // Additional properties can be added as they are identified
}

/**
 * Parameters for fetching cases
 */
export interface FetchCasesParams extends PaginationParams {
    sort?: SortParam[];
    filter?: FilterParam[];
    status?: CaseStatus;
}

/**
 * Response for issue operations
 */
export interface IssueResponse extends BaseResponse {
    issues?: {
        results: Issue[];
        total?: number;
    };
}

/**
 * Issues API Contract
 */
export interface IssuesContract {
    /**
     * Fetch a specific issue by ID
     * @param issueId The ID of the issue to fetch
     * @returns Promise with the issue
     */
    fetchIssue(issueId: string): Promise<Issue>;

    /**
     * Fetch available views for issues
     * @returns Promise with the views data
     */
    fetchViews(): Promise<any>; // This can be better typed when the response structure is known

    /**
     * Fetch a list of cases/issues
     * @param params Parameters for the query including pagination, sorting, and filtering
     * @returns Promise with an array of issues
     */
    fetchCases(params: FetchCasesParams): Promise<Issue[]>;
}
