import type { BaseResponse } from './BaseContract';

/**
 * Login credentials for authentication
 */
export interface LoginCredentials {
    email: string;
    password: string;
}

/**
 * Login response structure
 */
export interface LoginResponse {
    success: boolean;
    message?: string;
    csrf_token?: string;
    all_access?: string[];
}

/**
 * Request options for HTTP requests
 */
export interface RequestOptions {
    params?: Record<string, string>;
    body?: any;
    headers?: Record<string, string>;
}

/**
 * User status information
 */
export interface UserStatus {
    success: boolean;
    csrf_token?: string;
    users?: {
        email: string;
        user_id?: string;
        fullname?: string;
        first_name?: string;
        last_name?: string;
        [key: string]: any;
    };
}

/**
 * HTTP Client Contract
 */
export interface HttpClientContract {
    /**
     * Initialize the HTTP client
     * @returns Promise that resolves when initialization is complete
     */
    initialize(): Promise<void>;

    /**
     * Perform user login
     * @param credentials User login credentials
     * @returns Promise with login response
     */
    login(credentials: LoginCredentials): Promise<LoginResponse>;

    /**
     * Perform user logout
     * @returns Promise that resolves when logout is complete
     */
    logout(): Promise<void>;

    /**
     * Make a GET request
     * @param endpoint API endpoint
     * @param params Optional query parameters
     * @param options Additional request options
     * @returns Promise with response data
     */
    get<T = any>(endpoint: string, params?: Record<string, string>, options?: RequestOptions): Promise<T>;

    /**
     * Make a POST request
     * @param endpoint API endpoint
     * @param body Request body
     * @param params Optional query parameters
     * @param options Additional request options
     * @returns Promise with response data
     */
    post<T = any>(endpoint: string, body: any, params?: Record<string, string>, options?: RequestOptions): Promise<T>;

    /**
     * Make a PUT request
     * @param endpoint API endpoint
     * @param body Request body
     * @param params Optional query parameters
     * @param options Additional request options
     * @returns Promise with response data
     */
    put<T = any>(endpoint: string, body: any, params?: Record<string, string>, options?: RequestOptions): Promise<T>;

    /**
     * Make a DELETE request
     * @param endpoint API endpoint
     * @param params Optional query parameters
     * @param options Additional request options
     * @returns Promise with response data
     */
    delete<T = any>(endpoint: string, params?: Record<string, string>, options?: RequestOptions): Promise<T>;
}
