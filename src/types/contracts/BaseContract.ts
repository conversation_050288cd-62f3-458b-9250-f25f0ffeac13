/**
 * Base interface for all API responses
 */
export interface BaseResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
}

/**
 * Common error response structure
 */
export interface ErrorResponse {
    success: false;
    message: string;
    code?: string;
    status?: number;
}

/**
 * Common pagination parameters
 */
export interface PaginationParams {
    page?: number;
    start?: number;
    limit?: number;
}

/**
 * Standard filter parameter structure
 */
export interface FilterParam {
    property: string;
    value: any;
    operator?: string;
}

/**
 * Standard sort parameter structure
 */
export interface SortParam {
    property: string;
    direction: 'ASC' | 'DESC';
}

/**
 * Common response with pagination metadata
 */
export interface PaginatedResponse<T = any> extends BaseResponse<T> {
    meta?: {
        total: number;
        page: number;
        limit: number;
    };
}
