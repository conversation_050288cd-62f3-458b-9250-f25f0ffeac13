import type { BaseResponse, <PERSON>lterParam, <PERSON>ginationParams, SortParam } from './BaseContract';

/**
 * Interaction Event representation
 */
export interface InteractionEvent {
    category: {
        id: string;
        label: string;
    };
    body: {
        type: string;
        data: string;
    };
    created: string;
    event_data?: any; // Can be refined when exact structure is known
}

/**
 * Parameters for fetching interaction events
 */
export interface FetchInteractionEventsParams extends PaginationParams {
    issueId?: string;
    sort?: SortParam[];
    filter?: FilterParam[];
}

/**
 * Response for interaction events operations
 */
export interface InteractionEventsResponse extends BaseResponse {
    log_feed?: {
        results: InteractionEvent[];
        total?: number;
    };
}

/**
 * Interaction Events API Contract
 */
export interface InteractionEventsContract {
    /**
     * Fetch interaction events
     * @param params Parameters for the query including pagination, sorting, filtering and issue ID
     * @returns Promise with an array of interaction events
     */
    fetchInteractionEvents(params: FetchInteractionEventsParams): Promise<InteractionEvent[]>;

    // Additional methods can be added as they are implemented in the API
}
