/**
 * MFE Configuration structure
 */
export interface MFEConfig {
    key: string;
    splitName: string;
    css: string[];
    esModules: string[];
}

/**
 * Request information for HTTP requests
 */
export interface RequestInfo {
    headers?: Record<string, string>;
    params?: Record<string, string>;
}

/**
 * Context for widget initialization
 */
export interface WidgetContext {
    [key: string]: any;
}

/**
 * Widget initialization parameters
 */
export interface WidgetInit {
    adminHttpClient: any;
    context: WidgetContext;
    config: Record<string, any>;
}

/**
 * Widget instance interface
 */
export interface WidgetInstance {
    updateContext: (context: WidgetContext) => void;
    mount: (element: string | Element, context: WidgetContext) => Promise<void>;
    unmount: () => void;
}

/**
 * Widget factory function type
 */
export type WidgetLike = (init: WidgetInit) => WidgetInstance;

/**
 * MFE Loader Contract
 */
export interface MFELoaderContract {
    /**
     * Load a Micro Frontend by name
     * @param mfeName The name of the MFE to load
     * @returns Promise that resolves when the MFE is loaded
     */
    loadMFE(mfeName: string): Promise<void>;

    /**
     * Get configuration for a specific MFE
     * @param mfeName The name of the MFE
     * @returns The MFE configuration
     */
    getMFEConfig(mfeName: string): MFEConfig;
}

/**
 * Dynamic Widget Adapter Contract
 */
export interface MFEAdapterContract {
    /**
     * Set the base URL for API requests
     * @param baseUrl The new base URL
     * @returns The adapter instance for chaining
     */
    setBaseUrl(baseUrl: string): MFEAdapterContract;

    /**
     * Get the HTTP client used by the adapter
     */
    readonly httpClient: any;

    /**
     * Set configuration for the adapter
     * @param config Configuration object
     * @returns The adapter instance for chaining
     */
    setConfig(config: Record<string, any>): MFEAdapterContract;

    /**
     * Register a widget with the adapter
     * @param name The name of the widget
     * @param widget The widget factory function
     */
    registerWidget(name: string, widget: WidgetLike): void;

    /**
     * Mount a widget to a DOM element
     * @param name The name of the widget to mount
     * @param element The DOM element or selector to mount to
     * @param context The context to provide to the widget
     * @returns Promise that resolves to the widget instance
     */
    mountWidget(name: string, element: string | Element, context?: WidgetContext): Promise<WidgetInstance>;
}
