export interface ButtonPermission {
    enableBrEnter: boolean;
    enableBasicTextStyle: boolean;
    enableAdvancedTextStyle: boolean;
    enableColors: boolean;
    enableFontStyles: boolean;
    enableLineHeight: boolean;
    enableParagraphFormat: boolean;
    enableParagraphStyle: boolean;
    enableParagraphAlign: boolean;
    enableSelectAll: boolean;
    enableClearFormatting: boolean;
    enableAdvanced: boolean;
    enableLists: boolean;
    enableUndo: boolean;
    enableHR: boolean;
    enableLinks: boolean;
    enableImage: boolean;
    enableTeamviewer: boolean;
    enableCannedResponses: boolean;
    enableUpload: boolean;
    enableVideo: boolean;
    enableKbInsert: boolean;
    enableKbInsertBlock: boolean;
    enableKbInsertBlockEmbedded: boolean;
    enableTables: boolean;
    enableTabs: boolean;
    enableAccordions: boolean;
    enableDivPanels: boolean;
    enableSourceEdit: boolean;
    enableFontAwesome: boolean;
    enableHelp: boolean;
    enableTrackChanges: boolean;
    enableTrackChangesSingle: boolean;
    enableTokenInsert: boolean;
}
