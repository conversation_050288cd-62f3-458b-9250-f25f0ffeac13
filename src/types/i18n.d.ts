import { DefineLocaleMessage } from 'vue-i18n';

// Define types for our translation schema to provide type checking and autocomplete
declare module 'vue-i18n' {
    // Define the shape of our locale messages
    export interface DefineLocaleMessage {
        common: {
            welcome: string;
            save: string;
            cancel: string;
            delete: string;
            edit: string;
            loading: string;
        };
        nav: {
            home: string;
            settings: string;
            profile: string;
        };
        errors: {
            something_went_wrong: string;
            required_field: string;
        };
    }
}
