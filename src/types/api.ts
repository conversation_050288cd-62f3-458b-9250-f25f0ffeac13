export interface ApiResponse<T> {
    data: T;
    error?: string;
    meta?: {
        total: number;
        page: number;
        limit: number;
    };
}

export interface ApiError {
    message: string;
    code: string;
    status: number;
}

// Example endpoint types
export interface GetUsersResponse {
    users: Array<{
        id: string;
        name: string;
        email: string;
    }>;
}
