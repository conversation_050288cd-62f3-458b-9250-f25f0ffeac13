/**
 * Base64 utility class for encoding and decoding strings
 */
export class Base64 {
    private static readonly _str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';

    /**
     * Encodes given string into base64 formatted string
     * @param input - The string to encode
     * @returns Base64 encoded string
     */
    public static encode(input: string): string {
        let output = '';
        let i = 0;
        let chr1: number, chr2: number, chr3: number;
        let enc1: number, enc2: number, enc3: number, enc4: number;
        const len: number = input.length;

        input = this._utf8_encode(input);

        while (i < len) {
            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);

            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;

            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }

            output =
                output +
                this._str.charAt(enc1) +
                this._str.charAt(enc2) +
                this._str.charAt(enc3) +
                this._str.charAt(enc4);
        }

        return output;
    }

    /**
     * Decodes given base64 formatted string
     * @param input - The base64 string to decode
     * @returns Decoded string
     */
    public static decode(input: string): string {
        let output = '';
        let i = 0;
        let chr1: number, chr2: number, chr3: number;
        let enc1: number, enc2: number, enc3: number, enc4: number;
        const len: number = input.length;

        input = input.replace(/[^A-Za-z0-9+=\/]/g, '');

        while (i < len) {
            enc1 = this._str.indexOf(input.charAt(i++));
            enc2 = this._str.indexOf(input.charAt(i++));
            enc3 = this._str.indexOf(input.charAt(i++));
            enc4 = this._str.indexOf(input.charAt(i++));

            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;

            output = output + String.fromCharCode(chr1);

            if (enc3 !== 64) {
                output = output + String.fromCharCode(chr2);
            }

            if (enc4 !== 64) {
                output = output + String.fromCharCode(chr3);
            }
        }

        output = this._utf8_decode(output);

        return output;
    }

    /**
     * UTF-8 encoding
     * @private
     */
    private static _utf8_encode(string: string): string {
        let utftext = '';
        let c: number, n: number, len: number;

        string = string.replace(/\r\n/g, '\n');

        for (n = 0, len = string.length; n < len; n++) {
            c = string.charCodeAt(n);

            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if (c > 127 && c < 2048) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }

        return utftext;
    }

    /**
     * UTF-8 decoding
     * @private
     */
    private static _utf8_decode(utftext: string): string {
        let string = '';
        let i = 0;
        let c = 0;
        let c3 = 0;
        let c2 = 0;
        const len = utftext.length;

        while (i < len) {
            c = utftext.charCodeAt(i);

            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            } else if (c > 191 && c < 224) {
                c2 = utftext.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                i += 2;
            } else {
                c2 = utftext.charCodeAt(i + 1);
                c3 = utftext.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                i += 3;
            }
        }

        return string;
    }
}
