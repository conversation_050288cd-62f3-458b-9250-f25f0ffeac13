import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
    isApiRecorderEnabled,
    setApiRecorderEnabled,
    toggleApiRecorder,
    recordApiResponse,
    getRecordedApiResponses,
    clearApiRecordings,
} from '../apiRecorder';

// Mock localStorage
const localStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
        getItem: vi.fn((key: string) => store[key] || null),
        setItem: vi.fn((key: string, value: string) => {
            store[key] = value;
        }),
        removeItem: vi.fn((key: string) => {
            delete store[key];
        }),
        clear: vi.fn(() => {
            store = {};
        }),
    };
})();

// Mock import.meta.env.VITE_ALLOW_MSW
vi.mock('import.meta.env', () => ({
    env: {
        VITE_ALLOW_MSW: true,
        VITE_ALLOW_SCREEN_RECORDER: true,
    },
}));

// Mock crypto.randomUUID
global.crypto = {
    ...global.crypto,
    randomUUID: vi.fn(() => '123e4567-e89b-12d3-a456-************'),
};

describe('API Recorder', () => {
    beforeEach(() => {
        // Setup mocks before each test
        Object.defineProperty(window, 'localStorage', { value: localStorageMock });
        localStorageMock.clear();
        vi.clearAllMocks();
    });

    it('should check if API recorder is enabled', () => {
        // Initially should be false
        expect(isApiRecorderEnabled()).toBe(false);

        // Set it to true
        localStorageMock.getItem.mockReturnValueOnce('true');
        expect(isApiRecorderEnabled()).toBe(true);
    });

    it('should set API recorder enabled state', () => {
        setApiRecorderEnabled(true);
        expect(localStorageMock.setItem).toHaveBeenCalledWith('api-recorder-enabled', 'true');

        setApiRecorderEnabled(false);
        expect(localStorageMock.setItem).toHaveBeenCalledWith('api-recorder-enabled', 'false');
    });

    it('should toggle API recorder state', () => {
        // Initial state is false
        localStorageMock.getItem.mockReturnValueOnce('false');
        expect(toggleApiRecorder()).toBe(true);

        // Now state should be true, so toggling should return false
        localStorageMock.getItem.mockReturnValueOnce('true');
        expect(toggleApiRecorder()).toBe(false);
    });

    it('should record API responses when enabled', () => {
        // Mock enabled state
        localStorageMock.getItem.mockImplementation((key: string) => {
            if (key === 'api-recorder-enabled') return 'true';
            if (key === 'api-recorder-data') return '[]';
            return null;
        });

        const mockResponse = { data: 'test' };

        recordApiResponse('testFunction', 'https://api.example.com', 'GET', mockResponse);

        // Check that localStorage.setItem was called with the correct arguments
        expect(localStorageMock.setItem).toHaveBeenCalledTimes(1);
        expect(localStorageMock.setItem.mock.calls[0][0]).toBe('api-recorder-data');

        // Parse the JSON to check the recorded data
        const recordedData = JSON.parse(localStorageMock.setItem.mock.calls[0][1]);
        expect(recordedData).toHaveLength(1);
        expect(recordedData[0]).toMatchObject({
            id: expect.any(String),
            timestamp: expect.any(String),
            functionName: 'testFunction',
            url: 'https://api.example.com',
            method: 'GET',
            response: mockResponse,
        });
    });

    it('should not record API responses when disabled', () => {
        // Mock disabled state
        localStorageMock.getItem.mockReturnValueOnce('false');

        recordApiResponse('testFunction', 'https://api.example.com', 'GET', { data: 'test' });

        // Check that localStorage.setItem was not called
        expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });

    it('should get recorded API responses', () => {
        const mockRecordedResponses = [
            {
                id: '123',
                timestamp: '2023-01-01T12:00:00.000Z',
                functionName: 'testFunction',
                url: 'https://api.example.com',
                method: 'GET',
                response: { data: 'test1' },
            },
            {
                id: '456',
                timestamp: '2023-01-02T12:00:00.000Z',
                functionName: 'anotherFunction',
                url: 'https://api.example.com/data',
                method: 'POST',
                response: { data: 'test2' },
            },
        ];

        localStorageMock.getItem.mockReturnValueOnce(JSON.stringify(mockRecordedResponses));

        const responses = getRecordedApiResponses();
        expect(responses).toEqual(mockRecordedResponses);
    });

    it('should return empty array when no responses are recorded', () => {
        localStorageMock.getItem.mockReturnValueOnce(null);

        const responses = getRecordedApiResponses();
        expect(responses).toEqual([]);
    });

    it('should clear API recordings', () => {
        clearApiRecordings();

        expect(localStorageMock.removeItem).toHaveBeenCalledWith('api-recorder-data');
    });
});
