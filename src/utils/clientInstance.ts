import { v4 as uuidv4 } from 'uuid';
import { CLIENT_INSTANCE_ID_KEY } from './constants';

/**
 * Get a unique client instance ID from localStorage or generate a new one
 * @returns A unique client instance ID
 */
export function fetchClientInstanceId(): string {
    try {
        // Try to get from localStorage first
        let id = localStorage.getItem(CLIENT_INSTANCE_ID_KEY);

        // If not found, create a new one and save it
        if (!id) {
            id = uuidv4();
            localStorage.setItem(CLIENT_INSTANCE_ID_KEY, id);
        }

        return id;
    } catch (error) {
        // If localStorage fails, still return a unique ID (but it won't persist)
        console.warn('Failed to use localStorage for client instance ID:', error);
        return uuidv4();
    }
}
