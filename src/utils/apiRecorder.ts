/**
 * Storage keys for API recorder
 */
const API_RECORDER_ENABLED_KEY = 'api-recorder-enabled';
const API_RECORDER_DATA_KEY = 'api-recorder-data';

/**
 * Interface for recorded API responses
 */
export interface RecordedApiResponse {
    id: string;
    timestamp: string;
    functionName: string;
    url: string;
    method: string;
    response: any;
}

/**
 * Check if API recorder is enabled
 */
export function isApiRecorderEnabled(): boolean {
    return import.meta.env.VITE_ALLOW_MSW && localStorage.getItem(API_RECORDER_ENABLED_KEY) === 'true';
}

/**
 * Set API recorder enabled state
 */
export function setApiRecorderEnabled(enabled: boolean): void {
    localStorage.setItem(API_RECORDER_ENABLED_KEY, enabled ? 'true' : 'false');
}

/**
 * Toggle API recorder state
 */
export function toggleApiRecorder(): boolean {
    const newState = !isApiRecorderEnabled();
    setApiRecorderEnabled(newState);
    return newState;
}

/**
 * Record an API response
 */
export function recordApiResponse(functionName: string, url: string, method: string, response: any): void {
    if (!isApiRecorderEnabled() || !import.meta.env.VITE_ALLOW_MSW) {
        return;
    }

    try {
        // Generate a unique ID for this record
        const id = crypto.randomUUID();

        // Create new record
        const record: RecordedApiResponse = {
            id,
            timestamp: new Date().toISOString(),
            functionName,
            url,
            method,
            response,
        };

        // Get existing records
        const existingRecordsJson = localStorage.getItem(API_RECORDER_DATA_KEY) || '[]';
        const existingRecords: RecordedApiResponse[] = JSON.parse(existingRecordsJson);

        // Add new record and save
        existingRecords.push(record);

        // Limit to last 100 records to prevent localStorage from getting too full
        const limitedRecords = existingRecords.slice(-100);
        localStorage.setItem(API_RECORDER_DATA_KEY, JSON.stringify(limitedRecords));

        console.debug('API response recorded:', record);
    } catch (error) {
        console.error('Failed to record API response:', error);
    }
}

/**
 * Get all recorded API responses
 */
export function getRecordedApiResponses(): RecordedApiResponse[] {
    try {
        const recordsJson = localStorage.getItem(API_RECORDER_DATA_KEY) || '[]';
        return JSON.parse(recordsJson);
    } catch (error) {
        console.error('Failed to get recorded API responses:', error);
        return [];
    }
}

/**
 * Clear all recorded API responses
 */
export function clearApiRecordings(): void {
    localStorage.removeItem(API_RECORDER_DATA_KEY);
}
