/**
 * Storage keys for Mock API configuration
 */
const MSW_ENABLED_KEY = 'msw-enabled';
const MSW_FIXTURE_KEY = 'msw-fixture';

/**
 * Custom event for fixture changes
 */
export const FIXTURE_CHANGE_EVENT = 'msw-fixture-change';

/**
 * Fixture interface
 */
export interface MockFixture {
    id: number;
    name: string;
    description?: string;
}

/**
 * Available mock data fixtures
 */
export const MOCK_FIXTURES: MockFixture[] = [
    {
        id: 1,
        name: 'Default',
        description: 'Standard mock data set'
    },
    {
        id: 2,
        name: 'Alternative',
        description: 'Modified mock data with variations'
    }
];

// Storage that works in both environments
export const createStorage = () => {
    const isNode = typeof window === 'undefined';
    const store: Record<string, string | null> = {}; // Properly typed object
    
    return {
        getItem: (key: string): string | null => 
            isNode ? store[key] || null : localStorage.getItem(key),
        
        setItem: (key: string, value: string): void => {
            if (isNode) {
                store[key] = value;
            } else {
                localStorage.setItem(key, value);
            }
        }
    };
};

// Create a singleton storage instance
const storage = createStorage();

/**
 * Check if MSW is enabled from storage
 */
export function isMswEnabled(): boolean {
    return storage.getItem(MSW_ENABLED_KEY) === 'true';
}

/**
 * Set MSW enabled state in storage
 */
export function setMswEnabled(enabled: boolean): void {
    storage.setItem(MSW_ENABLED_KEY, enabled ? 'true' : 'false');
}

/**
 * Toggle MSW on/off and return new state
 */
export function toggleMsw(): boolean {
    const newState = !isMswEnabled();
    setMswEnabled(newState);
    return newState;
}

/**
 * Get current mock fixture ID
 */
export function getCurrentFixtureId(): number {
    const fixtureId = parseInt(storage.getItem(MSW_FIXTURE_KEY) || '1', 10);
    return isNaN(fixtureId) ? 1 : fixtureId;
}

/**
 * Get current mock fixture
 */
export function getCurrentFixture(): MockFixture {
    const fixtureId = getCurrentFixtureId();
    return MOCK_FIXTURES.find(f => f.id === fixtureId) || MOCK_FIXTURES[0];
}

/**
 * Set current mock fixture and emit change event
 */
export function setCurrentFixture(fixtureId: number): void {
    const validFixture = MOCK_FIXTURES.some(f => f.id === fixtureId);
    if (validFixture) {
        storage.setItem(MSW_FIXTURE_KEY, fixtureId.toString());
        // Dispatch a custom event only in browser environment
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent(FIXTURE_CHANGE_EVENT, { 
                detail: { fixtureId }
            }));
        }
    } else {
        console.warn(`Invalid fixture ID: ${fixtureId}`);
    }
} 