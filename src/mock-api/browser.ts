import { setupWorker } from 'msw/browser';
import { handlers, getHandlers } from './handlers';

// Export the worker with current handlers
export const worker = setupWorker(...handlers);

// Function to update handlers with fresh copies
export async function resetHandlers() {
    // Get fresh handlers that will reload mock data
    const freshHandlers = await getHandlers();
    
    // Reset the worker with fresh handlers
    await worker.resetHandlers(...freshHandlers);
    
    console.log('🔄 MSW handlers have been reset with fresh data');
    return freshHandlers;
}
