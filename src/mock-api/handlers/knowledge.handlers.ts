import { http, HttpResponse, delay } from 'msw';
import { getKnowledgeMock } from '../data/fixtures';
import { handleListingLogRequest } from '../data/knowledge.mock';
import { RESPONSE_DELAY_IN_MS } from '.';
import { FIXTURE_CHANGE_EVENT } from '../fixtures-manager';

// Define types for the mock data
interface KBResult {
    id: string;
    [key: string]: any;
}

interface KBListingResponse {
    kb?: {
        results?: KBResult[];
        [key: string]: any;
    };
    [key: string]: any;
}

// Define the structure of the knowledge mock data
interface KnowledgeMockData {
    mockKBGetListingResponse?: KBListingResponse;
    mockKBGetListingTreeResponse?: any;
    mockKBListingLogResponse?: any;
    mockKBMetaRelatedArticlesResponse?: any;
    mockKBRevisionsListingResponse?: any;
    mockKBGetArticleTreeResponse?: any;
    mockKBGetAticleOptions?: any;
    mockKBFileManagerResponse?: any;
    [key: string]: any;
}

// Initialize handlers with empty mock data
let mockKBGetListingResponse: KBListingResponse = {};
let mockKBGetListingTreeResponse: any = {};
let mockKBListingLogResponse: any = {};
let mockKBMetaRelatedArticlesResponse: any = {};
let mockKBRevisionsListingResponse: any = {};
let mockKBGetArticleTreeResponse: any = {};
let mockKBGetAticleOptions: any = {};
let mockKBFileManagerResponse: any = {};


// Track if data has been loaded
let dataLoaded = false;

// Function to load mock data
async function loadMockData() {
    try {
        console.debug('🔄 Loading knowledge mock data');
        const mockData: KnowledgeMockData = await getKnowledgeMock();
        mockKBGetListingResponse = mockData.mockKBGetListingResponse || {};
        mockKBGetListingTreeResponse = mockData.mockKBGetListingTreeResponse || {};
        mockKBListingLogResponse = mockData.mockKBListingLogResponse || {};
        mockKBMetaRelatedArticlesResponse = mockData.mockKBMetaRelatedArticlesResponse || {};
        mockKBRevisionsListingResponse = mockData.mockKBRevisionsListingResponse || {};
        mockKBGetArticleTreeResponse = mockData.mockKBGetArticleTreeResponse || {};
        mockKBGetAticleOptions = mockData.mockKBGetAticleOptions || {};
        mockKBFileManagerResponse = mockData.mockKBFileManagerResponse || {};
        console.debug('🔄 done Loading knowledge mock data');
    } catch (error) {
        console.error('Error loading knowledge mock data:', error);
    }
}

// Event handler for fixture changes
const handleFixtureChange = () => {
    console.log('🔄 Knowledge handlers: Reloading fixture data');
    loadMockData();
};

// Ensure data is loaded - only loads once if already loaded
export async function ensureMockDataLoaded(): Promise<void> {
    if (!dataLoaded) {
        await loadMockData();
        dataLoaded = true;
    }
}

// Initialize data loading
ensureMockDataLoaded()
    .then(() => console.log('🔄 Knowledge mock data initialized'))
    .catch(err => console.error('Failed to load knowledge mock data:', err));

// Set up event listener for fixture changes
if (typeof window !== 'undefined') {
    // Only add listener in browser context
    window.removeEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
    window.addEventListener(FIXTURE_CHANGE_EVENT, handleFixtureChange);
}

export const knowledgeHandlers = [
    // Mock getting knowledge tree
    http.get('*/admin/v4/kb', async ({ request }) => {
        const queryParams = new URL(request.url).searchParams;
        await delay(RESPONSE_DELAY_IN_MS);

        let response;
        if (queryParams.get('sAction') === 'listingTree') {
            response = mockKBGetListingTreeResponse;
        } else if (queryParams.get('sAction') === 'listing') {
            response = mockKBGetListingResponse;
        } else if (queryParams.get('sAction') === 'revisionsListing') {
            response = mockKBRevisionsListingResponse;
        } else if (queryParams.get('sAction') === 'metaRelatedArticles') {
            response = mockKBMetaRelatedArticlesResponse;
        } else if (queryParams.get('sAction') === 'listingLog') {
            response = handleListingLogRequest(queryParams);
        } else if (queryParams.get('sAction') === 'read') {
            // Handle fetching a specific article by ID from query parameter
            const articleId = queryParams.get('id');
            if (articleId) {
                const article = mockKBGetListingResponse.kb?.results?.find(
                    (article: KBResult) => article.id === articleId
                );

                if (!article) {
                    return new HttpResponse(
                        JSON.stringify({
                            success: false,
                            error: 'Not Found',
                            status: 404,
                            message: `Article with ID ${articleId} not found`
                        }),
                        {
                            status: 404,
                            headers: { 'Content-Type': 'application/json' }
                        }
                    );
                }

                return HttpResponse.json({
                    success: true,
                    data: {
                        id: articleId,
                        title: article.title || `Article ${articleId}`,
                        content: article.content || `This is the content for article ${articleId}.`,
                        created_at: article.created_at || new Date().toISOString(),
                        updated_at: article.updated_at || new Date().toISOString(),
                    }
                });
            }
        } else {
            response = {
                success: false,
                message: 'Invalid action',
            };
        }
        return HttpResponse.json(response);
    }),

    // Mock the file manager endpoint
    http.get('*/admin/v4/files/', async ({ request }) => {
        const queryParams = new URL(request.url).searchParams;
        await delay(RESPONSE_DELAY_IN_MS);

        // Check if this is a listingMgr request
        if (queryParams.get('sAction') === 'listingMgr') {
            console.log('🔄 Handling listingMgr request for file manager');

            // Get pagination parameters
            const limit = parseInt(queryParams.get('limit') || '25', 10);
            const start = parseInt(queryParams.get('start') || '0', 10);

            // Get search query if present
            const query = queryParams.get('query') || '';

            // Clone the response to avoid modifying the original
            const response = JSON.parse(JSON.stringify(mockKBFileManagerResponse));

            // Apply search filter if query is provided
            if (query) {
                const lowerQuery = query.toLowerCase();
                response.upload_files.results = response.upload_files.results.filter((file: any) =>
                    file.name.toLowerCase().includes(lowerQuery)
                );
                response.upload_files.totalCount = response.upload_files.results.length;
            }

            // Apply pagination
            const paginatedResults = response.upload_files.results.slice(start, start + limit);
            response.upload_files.results = paginatedResults;

            return HttpResponse.json(response);
        }

        return HttpResponse.json({
            success: false,
            message: 'Invalid action for files endpoint',
        });
    }),

    // Mock the file manager delete endpoint
    http.post('*/admin/v4/files/', async ({ request }) => {
        await delay(RESPONSE_DELAY_IN_MS);

        // Parse the form data
        const formData = await request.formData();
        const sAction = formData.get('sAction');

        // Handle file deletion
        if (sAction === 'mgrDeleteReplace') {
            console.log('🔄 Handling file deletion request');

            // Get the file IDs to delete
            const uploadFiles = formData.get('upload_files');
            const removeFromArticles = formData.get('remove_from_articles') === 'true';

            console.log('Files to delete:', uploadFiles);
            console.log('Remove from articles:', removeFromArticles);

            // Return success response
            return HttpResponse.json({
                success: true,
                message: 'Files deleted successfully',
                deleted_count: 1
            });
        }

        // Handle file upload
        if (sAction === 'putFile') {
            console.log('🔄 Handling file upload request');

            // Return success response with mock file data
            return HttpResponse.json({
                success: true,
                message: 'File uploaded successfully',
                data: {
                    id: 'new-file-' + Date.now(),
                    name: 'Uploaded File.pdf',
                    type: 'application/pdf',
                    size: 1024000,
                    url: 'https://example.com/files/uploaded-file.pdf',
                    thumbnail: 'https://example.com/thumbnails/uploaded-file.jpg',
                    created: new Date().toISOString(),
                    updated: new Date().toISOString(),
                    mime_type: 'application/pdf',
                    extension: 'pdf',
                    file_tag: 'managed'
                }
            });
        }

        return HttpResponse.json({
            success: false,
            message: 'Invalid action for files endpoint',
        });
    }),

    http.get('*/admin/v4/core', async ({ request }) => {
        const queryParams = new URL(request.url).searchParams;
        await delay(RESPONSE_DELAY_IN_MS);

        let response;
        if (queryParams.get('sAction') === 'listingTree') {
            response = mockKBGetListingTreeResponse;
        } else if (queryParams.get('sAction') === 'metaKBsTree') {
            const node = queryParams.get('node') || '';
            if (node.startsWith('library')) {
                response = mockKBGetAticleOptions;
            } else {
                response = mockKBGetArticleTreeResponse;
            }
        } else if (queryParams.get('sAction') === 'read') {
            // Handle fetching a specific article by ID from query parameter
            const articleId = queryParams.get('id');
            if (articleId) {
                const article = mockKBGetListingResponse.kb?.results?.find(
                    (article: KBResult) => article.id === articleId
                );

                if (!article) {
                    return new HttpResponse(
                        JSON.stringify({
                            success: false,
                            error: 'Not Found',
                            status: 404,
                            message: `Article with ID ${articleId} not found`
                        }),
                        {
                            status: 404,
                            headers: { 'Content-Type': 'application/json' }
                        }
                    );
                }

                return HttpResponse.json({
                    success: true,
                    data: {
                        id: articleId,
                        title: article.title || `Article ${articleId}`,
                        content: article.content || `This is the content for article ${articleId}.`,
                        created_at: article.created_at || new Date().toISOString(),
                        updated_at: article.updated_at || new Date().toISOString(),
                    }
                });
            }
        } else {
            response = {
                success: false,
                message: 'Invalid action',
            };
        }
        return HttpResponse.json(response);
    }),

    // Mock getting knowledge articles when no specific action is requested
    // http.get('*/admin/v4/kb', async ({ request }) => {
    //     await delay(RESPONSE_DELAY_IN_MS);
    //     // Example response with mock data
    //     return HttpResponse.json(mockKBGetListingResponse);
    // }),
];

