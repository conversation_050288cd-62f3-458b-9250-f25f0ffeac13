import { http, HttpResponse, delay } from 'msw';
import { RESPONSE_DELAY_IN_MS } from '.';

export const commHandlers = [
  // Handle communication details (itemGet) - API endpoint
  http.get('*/api/comm/', async ({ request }) => {
    const url = new URL(request.url);
    const sAction = url.searchParams.get('sAction');
    const id = url.searchParams.get('id');

    if (sAction === 'itemGet') {
      await delay(RESPONSE_DELAY_IN_MS);

      // Mock communication data
      const mockCommData = {
        id: id || '46677C',
        object: 'issues',
        object_id: '47916b67-be01-4a13-a07a-1fda19b2fdda',
        object_scope: 'private',
        comm_type: 0,
        comm_label: '',
        comm_status: 1,
        comm_state: 1,
        object_source: 'relay',
        billing_status: 1,
        title: 'Mock Communication',
        subtitle: 'Mock Subtitle',
        duration_plus: 0,
        duration: 0,
        user_message_cnt: {},
        external_rpid: '',
        external_lpid: '',
        external_id: `comm.${id?.toLowerCase()}@conference.xmpp.stage.goboomtown.com`,
        external_status: '',
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        occupied: new Date().toISOString(),
        completed: '0000-00-00 00:00:00',
        c__status: 'Active',
        c__state: 'Active',
        c__avatar: '',
        email_address: '',
        participants: [
          {
            object: 'users',
            object_id: 'mock-user-1',
            name: 'John Doe',
            alias: 'john.doe',
            external_id: '<EMAIL>',
            host: true,
            eligible: true,
            id: 'mock-user-1',
            presence: true
          },
          {
            object: 'users',
            object_id: 'mock-user-2',
            name: 'Jane Smith',
            alias: 'jane.smith',
            external_id: '<EMAIL>',
            host: false,
            eligible: true,
            id: 'mock-user-2',
            presence: false
          }
        ],
        files: [],
        hasAutoForwarding: false,
        autoForwardDest: '',
        contactData: {
          users: [],
          sms: '',
          email: '',
          phone: null,
          sms_did: null
        },
        buttonRedactPatterns: [],
        autoRedactPatterns: [],
        cc_recipients: null,
        last_sender: null
      };

      return HttpResponse.json({
        success: true,
        comm: mockCommData,
        current_server_time: new Date().toISOString()
      });
    }

    return new HttpResponse(null, { status: 404 });
  }),

  // Handle communication details (itemGet) - Admin endpoint
  http.get('*/admin/v4/comm/', async ({ request }) => {
    const url = new URL(request.url);
    const sAction = url.searchParams.get('sAction');
    const id = url.searchParams.get('id');

    if (sAction === 'itemGet') {
      await delay(RESPONSE_DELAY_IN_MS);

      // Mock communication data (same as API endpoint)
      const mockCommData = {
        id: id || '46677C',
        object: 'issues',
        object_id: '47916b67-be01-4a13-a07a-1fda19b2fdda',
        object_scope: 'private',
        comm_type: 0,
        comm_label: '',
        comm_status: 1,
        comm_state: 1,
        object_source: 'relay',
        billing_status: 1,
        title: 'Mock Communication',
        subtitle: 'Mock Subtitle',
        duration_plus: 0,
        duration: 0,
        user_message_cnt: {},
        external_rpid: '',
        external_lpid: '',
        external_id: `comm.${id?.toLowerCase()}@conference.xmpp.stage.goboomtown.com`,
        external_status: '',
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        occupied: new Date().toISOString(),
        completed: '0000-00-00 00:00:00',
        c__status: 'Active',
        c__state: 'Active',
        c__avatar: '',
        email_address: '',
        participants: [
          {
            object: 'users',
            object_id: 'mock-user-1',
            name: 'John Doe',
            alias: 'john.doe',
            external_id: '<EMAIL>',
            host: true,
            eligible: true,
            id: 'mock-user-1',
            presence: true
          },
          {
            object: 'users',
            object_id: 'mock-user-2',
            name: 'Jane Smith',
            alias: 'jane.smith',
            external_id: '<EMAIL>',
            host: false,
            eligible: true,
            id: 'mock-user-2',
            presence: false
          }
        ],
        files: [],
        hasAutoForwarding: false,
        autoForwardDest: '',
        contactData: {
          users: [],
          sms: '',
          email: '',
          phone: null,
          sms_did: null
        },
        buttonRedactPatterns: [],
        autoRedactPatterns: [],
        cc_recipients: null,
        last_sender: null
      };

      return HttpResponse.json({
        success: true,
        comm: mockCommData,
        current_server_time: new Date().toISOString()
      });
    }

    return new HttpResponse(null, { status: 404 });
  }),

  // Handle communication updates (itemPut) - API endpoint
  http.post('*/api/comm/', async ({ request }) => {
    const url = new URL(request.url);
    const sAction = url.searchParams.get('sAction');

    if (sAction === 'itemPut') {
      await delay(RESPONSE_DELAY_IN_MS);

      try {
        const formData = await request.formData();
        const commData = formData.get('comm');
        
        if (commData) {
          const parsedComm = JSON.parse(commData.toString());
          console.log('🔄 Mock: Updating communication:', parsedComm);
          
          const commUpdate = parsedComm[0];
          
          // Handle different types of updates
          if (commUpdate.hasOwnProperty('presence')) {
            // Presence update
            return HttpResponse.json({
              success: true,
              comm: {
                id: commUpdate.id || 'mock-comm-id',
                presence: commUpdate.presence || false
              },
              current_server_time: new Date().toISOString()
            });
          } else if (commUpdate.hasOwnProperty('comm_label')) {
            // Label update
            return HttpResponse.json({
              success: true,
              comm: {
                id: commUpdate.id || 'mock-comm-id',
                comm_label: commUpdate.comm_label,
                title: commUpdate.comm_label || 'Updated Communication'
              },
              current_server_time: new Date().toISOString()
            });
          }
          
          // Generic update response
          return HttpResponse.json({
            success: true,
            comm: {
              id: commUpdate.id || 'mock-comm-id',
              ...commUpdate
            },
            current_server_time: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Mock: Error parsing communication data:', error);
      }

      return HttpResponse.json({
        success: false,
        message: 'Invalid communication data'
      }, { status: 400 });
    }

    return new HttpResponse(null, { status: 404 });
  }),

  // Handle communication updates (itemPut) - Admin endpoint
  http.post('*/admin/v4/comm/', async ({ request }) => {
    const url = new URL(request.url);
    const sAction = url.searchParams.get('sAction');

    if (sAction === 'itemPut') {
      await delay(RESPONSE_DELAY_IN_MS);

      try {
        const formData = await request.formData();
        const commData = formData.get('comm');
        
        if (commData) {
          const parsedComm = JSON.parse(commData.toString());
          console.log('🔄 Mock Admin: Updating communication:', parsedComm);
          
          const commUpdate = parsedComm[0];
          
          // Handle different types of updates
          if (commUpdate.hasOwnProperty('presence')) {
            // Presence update
            return HttpResponse.json({
              success: true,
              comm: {
                id: commUpdate.id || 'mock-comm-id',
                presence: commUpdate.presence || false
              },
              current_server_time: new Date().toISOString()
            });
          } else if (commUpdate.hasOwnProperty('comm_label')) {
            // Label update
            return HttpResponse.json({
              success: true,
              comm: {
                id: commUpdate.id || 'mock-comm-id',
                comm_label: commUpdate.comm_label,
                title: commUpdate.comm_label || 'Updated Communication'
              },
              current_server_time: new Date().toISOString()
            });
          }
          
          // Generic update response
          return HttpResponse.json({
            success: true,
            comm: {
              id: commUpdate.id || 'mock-comm-id',
              ...commUpdate
            },
            current_server_time: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Mock Admin: Error parsing communication data:', error);
      }

      return HttpResponse.json({
        success: false,
        message: 'Invalid communication data'
      }, { status: 400 });
    }

    return new HttpResponse(null, { status: 404 });
  })
]; 