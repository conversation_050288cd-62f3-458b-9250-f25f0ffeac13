import { http, HttpResponse, delay } from 'msw';
import { RESPONSE_DELAY_IN_MS } from '.';

export const filesHandlers = [
  // Handle file uploads for various objects (issues, knowledge base, etc.)
  http.post('*/admin/v4/files/', async ({ request }) => {
    const url = new URL(request.url);
    const sAction = url.searchParams.get('sAction');

    if (sAction === 'putFile') {
      await delay(RESPONSE_DELAY_IN_MS);

      try {
        const formData = await request.formData();
        
        // Get parameters from URL query string first, then FormData
        const object = url.searchParams.get('object') || formData.get('object')?.toString();
        const objectId = url.searchParams.get('object_id') || formData.get('object_id')?.toString();
        const fileTag = url.searchParams.get('file_tag') || formData.get('file_tag')?.toString();
        
        // Check for file in different field names
        const fileAttachment = formData.get('fileAttachment[]') as File;
        
        // Handle file uploads for issues (cases)
        if (object === 'issues' && objectId && fileAttachment) {
          console.log('🔄 Files Handler: Uploading file for issue:', objectId, 'file:', fileAttachment.name);

          // Mock successful file upload response
          const mockFileId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const fileExtension = fileAttachment.name.split('.').pop() || 'bin';
          const mockFileName = `${mockFileId}.${fileExtension}`;
          const mockFileKey = fileAttachment.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();

          return HttpResponse.json({
            success: true,
            uploads_files_ids: [mockFileId],
            file_keys: [mockFileKey],
            file_tags: [fileTag || 'attachment'],
            file_names: [mockFileName],
            file_names2: [fileAttachment.name],
            file_types: [fileAttachment.type || 'application/octet-stream'],
            link: `https://api.stage.goboomtown.com/upload_files/${mockFileName}`,
            links: [`https://api.stage.goboomtown.com/upload_files/${mockFileName}`],
            url_avatar: null,
            current_server_time: new Date().toISOString()
          });
        }

        // Handle file uploads for knowledge base
        if (object === 'kb_library' && objectId && fileAttachment) {
          console.log('🔄 Files Handler: Uploading file for knowledge base:', objectId, 'file:', fileAttachment.name);

          // Mock successful file upload response
          const mockFileId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const fileExtension = fileAttachment.name.split('.').pop() || 'bin';
          const mockFileName = `${mockFileId}.${fileExtension}`;
          const mockFileKey = fileAttachment.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();

          return HttpResponse.json({
            success: true,
            uploads_files_ids: [mockFileId],
            file_keys: [mockFileKey],
            file_tags: [fileTag || 'managed'],
            file_names: [mockFileName],
            file_names2: [fileAttachment.name],
            file_types: [fileAttachment.type || 'application/octet-stream'],
            link: `https://api.stage.goboomtown.com/upload_files/${mockFileName}`,
            links: [`https://api.stage.goboomtown.com/upload_files/${mockFileName}`],
            url_avatar: null,
            current_server_time: new Date().toISOString()
          });
        }

      } catch (error) {
        console.error('Files handler - upload error:', error);
        return HttpResponse.json({
          success: false,
          message: 'Failed to process file upload'
        }, { status: 500 });
      }
    }

    // Handle file listing for various objects
    if (sAction === 'listing') {
      await delay(RESPONSE_DELAY_IN_MS);

      try {
        const filterParam = url.searchParams.get('filter');
        let object = 'issues';
        let objectId = '';

        if (filterParam) {
          try {
            const filters = JSON.parse(filterParam);
            const objectFilter = filters.find((f: any) => f.property === 'object');
            const objectIdFilter = filters.find((f: any) => f.property === 'object_id');
            
            if (objectFilter) object = objectFilter.value;
            if (objectIdFilter) objectId = objectIdFilter.value;
          } catch (e) {
            console.warn('Failed to parse filter parameter:', e);
          }
        }

        if (object === 'issues' && objectId) {
          console.log('🔄 Files Handler: Fetching files for issue:', objectId);

          // Mock file listing response for issues
          const mockFiles = [
            {
              id: `file_${Date.now()}_1`,
              object: 'issues',
              object_id: objectId,
              uploader_object: 'users',
              uploader_id: '3ET',
              hash: 'de0009ab76672a9c080d4d6aa1d415fe',
              type: 'application/pdf',
              file: 'https://api.stage.goboomtown.com/upload_files/sample_document.pdf',
              name: 'Sample Document.pdf',
              tag: 'attachment',
              notes: '',
              created: new Date(Date.now() - 86400000).toISOString().replace('T', ' ').replace('Z', ''),
              scanner_status: 10,
              size: 15432,
              metadata: null,
              c__d__name: 'Sample Document.pdf',
              c__d__created: 'May 28, 2025 12:31 PM',
              c__d__type: 'pdf',
              c__d__fullname: 'John Doe',
              _highlighted: false,
              _highlightmap: {},
              thumbnail: 'https://api.stage.goboomtown.com/upload_files/sample_document/preview_300.pdf'
            },
            {
              id: `file_${Date.now()}_2`,
              object: 'issues',
              object_id: objectId,
              uploader_object: 'users',
              uploader_id: '3ET',
              hash: '164e90c935001a37373f06459af82e82',
              type: 'image/png',
              file: 'https://api.stage.goboomtown.com/upload_files/facility_map.png',
              name: 'facility-map-modernized.png',
              tag: 'attachment',
              notes: '',
              created: new Date(Date.now() - 43200000).toISOString().replace('T', ' ').replace('Z', ''),
              scanner_status: 10,
              size: 19364,
              metadata: null,
              c__d__name: 'facility-map-modernized.png',
              c__d__created: 'May 28, 2025 1:27 PM',
              c__d__type: 'png',
              c__d__fullname: 'Jane Smith',
              _highlighted: false,
              _highlightmap: {},
              thumbnail: 'https://picsum.photos/300/300?random=1'
            },
            {
              id: `file_${Date.now()}_3`,
              object: 'issues',
              object_id: objectId,
              uploader_object: 'users',
              uploader_id: '3ET',
              hash: '164e90c935001a37373f06459af82e83',
              type: 'text/csv',
              file: 'https://api.stage.goboomtown.com/upload_files/data_export.csv',
              name: 'data-export.csv',
              tag: 'attachment',
              notes: '',
              created: new Date(Date.now() - 21600000).toISOString().replace('T', ' ').replace('Z', ''),
              scanner_status: 10,
              size: 5432,
              metadata: null,
              c__d__name: 'data-export.csv',
              c__d__created: 'May 28, 2025 9:15 AM',
              c__d__type: 'csv',
              c__d__fullname: 'Bob Johnson',
              _highlighted: false,
              _highlightmap: {},
              thumbnail: ''
            }
          ];

          return HttpResponse.json({
            success: true,
            upload_files: {
              results: mockFiles,
              totalCount: mockFiles.length
            },
            current_server_time: new Date().toISOString()
          });
        }

        // Return empty results for other objects
        return HttpResponse.json({
          success: true,
          upload_files: {
            results: [],
            totalCount: 0
          },
          current_server_time: new Date().toISOString()
        });

      } catch (error) {
        console.error('Files handler - listing error:', error);
        return HttpResponse.json({
          success: false,
          message: 'Failed to fetch files'
        }, { status: 500 });
      }
    }

    // If this doesn't match our criteria, pass through to other handlers
    return;
  })
]; 