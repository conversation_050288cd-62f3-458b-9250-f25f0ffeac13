import { http, HttpResponse } from 'msw';
import { RESPONSE_DELAY_IN_MS } from './index';

// Mock analytics data
const mockAnalyticsResponse = {
    success: true,
    jwt: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    dashboard_uri: "https://ovationcxm-stage.cloud.gooddata.com/dashboards/embedded/#/workspace/workspace-org-H3F/dashboard/af3a156d-7a10-4d8b-96e5-66faf9e74c41?apiTokenAuthentication=true&showNavigation=true",
    current_server_time: new Date().toISOString()
};

export const analyticsHandlers = [
    // Handle GoodData cloud login
    http.post('*/api/core/', async ({ request }) => {
        await new Promise(resolve => setTimeout(resolve, RESPONSE_DELAY_IN_MS));
        
        try {
            const formData = await request.formData();
            const sAction = formData.get('sAction');
            
            if (sAction === 'gooddatacloudlogin') {
                console.log('🎯 Mock Analytics API: GoodData cloud login request');
                
                return HttpResponse.json(mockAnalyticsResponse, {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
            }
            
            // If it's not the analytics action, let other handlers deal with it
            return new HttpResponse(null, { status: 404 });
            
        } catch (error) {
            console.error('❌ Mock Analytics API: Error processing request:', error);
            return HttpResponse.json(
                { 
                    success: false, 
                    error: 'Failed to authenticate with analytics service' 
                },
                { status: 500 }
            );
        }
    }),
]; 