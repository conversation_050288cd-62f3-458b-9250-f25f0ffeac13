# Mock Service Worker Handlers

This directory contains mock API handlers for use with MSW (Mock Service Worker).

## Available Mock Endpoints

### Authentication

- `POST */admin/v4/core/?sAction=userLogin` - Mock user login
- `GET */admin/v4/core/?sAction=userStatus` - Mock user status/session info
- `POST */admin/v4/core/?sAction=userLogout` - Mock user logout
- `GET */admin/v4/core/?sAction=metaPartners` - Get partner metadata for the logged-in user

### Knowledge Base

- `GET */admin/v4/knowledge` - Get list of knowledge articles
- `GET */admin/v4/knowledge/:id` - Get specific knowledge article by ID

### Issues

- `GET */admin/v4/issues` - Get list of issues
- `GET */admin/v4/issues/:id` - Get specific issue by ID
- `POST */admin/v4/issues` - Create a new issue

### Settings

- `GET */admin/v4settings` - Get user settings
- `PUT */admin/v4settings` - Update user settings

## Adding New Handlers

To add new mock handlers:

1. Create or modify handler files in this directory
2. Import and export them in the `index.ts` file
3. The handlers will be automatically registered with MSW
