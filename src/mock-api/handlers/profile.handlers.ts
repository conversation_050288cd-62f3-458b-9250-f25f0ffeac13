import { http, HttpResponse, delay } from 'msw';
import { RESPONSE_DELAY_IN_MS } from '.';

export const profileHandlers = [
  // Handle avatar upload
  http.post('*/admin/v4/files/', async ({ request }) => {
    const url = new URL(request.url);
    const sAction = url.searchParams.get('sAction');

    if (sAction === 'putFile') {
      await delay(RESPONSE_DELAY_IN_MS);

      try {
        const formData = await request.formData();
        
        // Get parameters from URL query string first, then FormData
        const object = url.searchParams.get('object') || formData.get('object')?.toString();
        const objectId = url.searchParams.get('object_id') || formData.get('object_id')?.toString();
        const fileTag = url.searchParams.get('file_tag') || formData.get('file_tag')?.toString();
        
        // Check for file in different field names
        const file = formData.get('file') as File;

        // Only handle avatar uploads for users in this handler
        if (object === 'users' && fileTag === 'avatar' && objectId && file) {
          console.log('🔄 Handling avatar upload for user:', objectId);

          // Mock successful avatar upload response
          const mockFileId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const mockFileName = `${mockFileId}.jpg`;
          const mockAvatarUrl = `https://api.stage.goboomtown.com/avatar/users/${objectId}/100`;

          return HttpResponse.json({
            success: true,
            uploads_files_ids: [mockFileId],
            file_keys: [`${fileTag}_jpg`],
            file_tags: [fileTag],
            file_names: [mockFileName],
            file_names2: [file.name],
            file_types: [file.type || 'image/jpeg'],
            link: `https://api.stage.goboomtown.com/upload_files/${mockFileId}.jpg`,
            links: [`https://api.stage.goboomtown.com/upload_files/${mockFileId}.jpg`],
            url_avatar: mockAvatarUrl,
            current_server_time: new Date().toISOString()
          });
        }

      } catch (error) {
        console.error('Profile handler - avatar upload error:', error);
        return HttpResponse.json({
          success: false,
          message: 'Failed to process avatar upload'
        }, { status: 500 });
      }
    }

    // If this doesn't match our criteria, pass through to other handlers
    // by not returning a response (let other handlers handle it)
    return;
  })
]; 