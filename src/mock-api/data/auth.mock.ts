/**
 * Auth mock data responses
 *
 * Note: The auth handlers currently define their mock responses inline,
 * but we're creating this file for consistency and future use.
 */

export const mockUserLoginResponse = {
    success: true,
    authenticated: true,
    csrf_token: 'mock-csrf-token-12345',
    message: 'Login successful',
    all_access: [
        'billing',
        'notifications_log',
        'issues',
        'cases',
        'members',
        'members_locations',
        'members_users',
        'partners',
        'users'
    ]
};

export const mockUserStatusResponse = {
    success: true,
    authenticated: true,
    csrf_token: 'mock-csrf-token-12345',
    users: {
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        id: '123',
        perms: [
            'issue_view',
            'issue_edit',
            'issue_add',
            'cust_view',
            'cust_loc_edit',
            'cust_user_edit',
            'ship_view',
            'billing_view',
        ],
    },
    all_access: [
        'billing',
        'notifications_log',
        'issues',
        'cases',
        'members',
        'members_locations',
        'members_users',
        'partners',
        'users'
    ]
};

export const mockUserLogoutResponse = {
    success: true,
    message: 'Successfully logged out'
};

export const mockPartnerMetadataResponse = {
    success: true,
    partners: [
        {
            id: '1',
            name: 'Partner 1',
            description: 'Description for Partner 1',
            status: 'active',
        },
        {
            id: '2',
            name: 'Partner 2',
            description: 'Description for Partner 2',
            status: 'active',
        },
    ]
};

// SSO Auth responses
export const mockUserAuthResponse = {
    success: true,
    canAuthenticateInternal: true,
    canAuthenticateExternal: false,
    externalIdPs: [],
    current_server_time: new Date().toISOString()
};

export const mockUserAuthWithExternalResponse = {
    success: true,
    canAuthenticateInternal: true,
    canAuthenticateExternal: true,
    externalIdPs: [
        {
            logo: 'https://example.com/sso-logo.png',
            name: 'Company SSO',
            description: 'Login with your company credentials',
            url: 'https://auth.example.com/sso-login'
        },
        {
            logo: null,
            name: 'SAML Identity Provider',
            description: 'SAML protected single sign-on/logout (SSO/SLO) via an external identity provider',
            url: 'https://app.local-env.goboomtown.com/api/sso/saml/XGWPW7/sso'
        }
    ],
    current_server_time: new Date().toISOString()
};

export const mockUserAuthSsoOnlyResponse = {
    success: true,
    canAuthenticateInternal: false,
    canAuthenticateExternal: true,
    externalIdPs: [
        {
            logo: null,
            name: 'SAML Identity Provider',
            description: 'SAML protected single sign-on/logout (SSO/SLO) via an external identity provider',
            url: 'https://app.local-env.goboomtown.com/api/sso/saml/XGWPW7/sso'
        }
    ],
    current_server_time: new Date().toISOString()
};

export const mockUserAuthMultipleSsoResponse = {
    success: true,
    canAuthenticateInternal: false,
    canAuthenticateExternal: true,
    externalIdPs: [
        {
            logo: null,
            name: 'SAML Identity Provider',
            description: 'SAML protected single sign-on/logout (SSO/SLO) via an external identity provider',
            url: 'https://app.local-env.goboomtown.com/api/sso/saml/XGWPW7/sso'
        },
        {
            logo: 'https://example.com/oauth-logo.png',
            name: 'OAuth Provider',
            description: 'OAuth 2.0 authentication via external provider',
            url: 'https://oauth.example.com/authorize'
        },
        {
            logo: 'https://example.com/azure-logo.png',
            name: 'Azure AD',
            description: 'Microsoft Azure Active Directory SSO',
            url: 'https://login.microsoftonline.com/common/oauth2/authorize'
        }
    ],
    current_server_time: new Date().toISOString()
};

export const mockUserAuthFailedResponse = {
    success: false,
    message: 'User not found',
    current_server_time: new Date().toISOString()
};