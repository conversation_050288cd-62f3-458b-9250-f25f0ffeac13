# Migrating Mock Data to the New Structure

## Overview

This guide explains how to complete the migration of mock data from the original `mock-data.ts` file to our new modular structure.

## Current Status

We've created the new file structure and set up placeholder mock data in each file:

- `auth.mock.ts`
- `issues.mock.ts`
- `knowledge.mock.ts`
- `settings.mock.ts`

These files currently contain skeleton versions of the responses, but not the full data.

## Migration Steps

### Step 1: Extract Data from Existing File

The original `mock-data.ts` file is very large (over 20,000 lines) and needs to be split carefully. Here are the line ranges for each mock response:

- `mockKBGetListingTreeResponse`: lines 1-1457
- `mockKBGetListingResponse`: lines 1458-6055
- `mockKBRevisionsListingResponse`: lines 6056-6449
- `mockKBMetaRelatedArticlesResponse`: lines 6450-6455
- `mockKBListingLogResponse`: lines 6456-6997
- `mockGetIssuesResponse`: lines 6998-17398
- `mockGetIssueResponse`: lines 17399-19357
- `mockGetSettingsResponse`: lines 19358-21664

### Step 2: Copy Full Data to New Files

For each mock data object:

1. Open the original `mock-data.ts` file
2. Copy the complete mock data object based on the line ranges above
3. Paste it into the corresponding new file, replacing the placeholder skeleton

For example:

```typescript
// In knowledge.mock.ts
export const mockKBGetListingTreeResponse = {
    // Paste the complete object from mock-data.ts lines 1-1457
    success: true,
    kb_labels: { totalCount: 0, results: [] },
    // ... full data here
};
```

### Step 3: Verify Handler Integration

All handler files have been updated to import from the new structure:

- `knowledge.handlers.ts` → imports from `../data/knowledge.mock`
- `issues.handlers.ts` → imports from `../data/issues.mock`
- `settings.handlers.ts` → imports from `../data/settings.mock`

### Step 4: Test the Application

After completing the migration:

1. Start the application with MockAPI enabled
2. Test each feature that relies on mock data
3. Check the browser console for any errors related to missing mock data

### Step 5: Clean Up

Once everything is working correctly:

1. Delete the original `mock-data.ts` file
2. Commit the changes to version control

## Notes

- The auth handlers directly define their mock responses rather than importing them, but we've created `auth.mock.ts` for consistency and potential future use.
- If you find other mock data objects in the original file not listed above, add them to the appropriate category file. 