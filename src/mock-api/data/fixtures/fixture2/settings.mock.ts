/**
 * Settings mock data responses - Fixture 2 (Alternative)
 */

import { mockGetSettingsResponse as originalSettings } from '../../../data/settings.mock';

// Modified version with a different label for some views
export const mockGetSettingsResponse = {
    ...originalSettings,
    relay_views: {
        ...originalSettings.relay_views,
        results: originalSettings.relay_views.results.map((view, index) => 
            index < 3 ? {
                ...view,
                label: `${view.label} (Fixture 2 Variation)`
            } : view
        )
    }
}; 