/**
 * Knowledge Base (KB) mock data responses - Fixture 2 (Alternative)
 */

import { 
    mockKBGetListingResponse as originalListing,
    mockKBGetListingTreeResponse as originalTree,
    mockKBRevisionsListingResponse,
    mockKBMetaRelatedArticlesResponse,
    mockKBListingLogResponse 
} from '../../../data/knowledge.mock';

// Modified version with a different title for the first article
export const mockKBGetListingResponse = {
    ...originalListing,
    kb: {
        ...originalListing.kb,
        results: originalListing.kb.results.map((article, index) => 
            index === 0 ? {
                ...article,
                title: `${article.title} (Fixture 2 Variation)`,
                _title: `${article._title} (Fixture 2 Variation)`,
                full_title: `${article.full_title} (Fixture 2 Variation)`
            } : article
        )
    }
};

// Modified version with a different title for the first library
export const mockKBGetListingTreeResponse = {
    ...originalTree,
    children: originalTree.children.map((lib, index) =>
        index === 0 ? {
            ...lib,
            title: `${lib.title} (Fixture 2 Variation)`,
            _title: `${lib._title} (Fixture 2 Variation)`,
            text: `${lib.text} (Fixture 2 Variation)`
        } : lib
    )
};

// Re-export other unchanged mock data
export {
    mockKBRevisionsListingResponse,
    mockKBMetaRelatedArticlesResponse,
    mockKBListingLogResponse
}; 