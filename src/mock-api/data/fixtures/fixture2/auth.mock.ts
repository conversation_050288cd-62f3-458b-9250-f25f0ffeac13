/**
 * Auth mock data responses - Fixture 2 (Alternative)
 */

import { 
    mockUserLoginResponse,
    mockUserStatusResponse as originalUserStatus,
    mockUserLogoutResponse,
    mockPartnerMetadataResponse
} from '../../../data/auth.mock';

// Re-export most auth responses
export {
    mockUserLoginResponse,
    mockUserLogoutResponse,
    mockPartnerMetadataResponse
};

// Modified user status with a different name for the user
export const mockUserStatusResponse = {
    ...originalUserStatus,
    users: {
        ...originalUserStatus.users,
        first_name: 'Fixture2',
        last_name: 'User',
        email: '<EMAIL>'
    }
}; 