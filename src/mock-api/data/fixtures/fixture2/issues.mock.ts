/**
 * Issues mock data responses - Fixture 2 (Alternative)
 */

import { 
    mockGetIssueResponse as originalIssue,
    mockGetIssuesResponse as originalIssues
} from '../../../data/issues.mock';

// Modified version with a different display name for the issues
export const mockGetIssuesResponse = {
    ...originalIssues,
    issues: {
        ...originalIssues.issues,
        results: originalIssues.issues.results.map(issue => ({
            ...issue,
            display_name: `${issue.display_name} (Fixture 2 Variation)`
        }))
    }
};

// Modified version with a different display name for the single issue
export const mockGetIssueResponse = {
    ...originalIssue,
    issues: {
        ...originalIssue.issues,
        results: originalIssue.issues.results.map(issue => ({
            ...issue,
            display_name: `${issue.display_name} (Fixture 2 Variation)`
        }))
    }
}; 