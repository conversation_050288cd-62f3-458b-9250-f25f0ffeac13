# Mock Data Fixtures

This directory contains different sets of mock data fixtures that can be used with the Mock API.

## Directory Structure

- `/fixture1`: Default mock data set
- `/fixture2`: Alternative mock data set with variations

## Using Fixtures

The Mock API UI allows you to select which fixture set to use. Fixtures are loaded based on the fixture ID stored in local storage.

## Adding New Fixtures

To add a new fixture:

1. Create a new directory (e.g., `/fixture3`) 
2. Co<PERSON> and modify the mock data files from an existing fixture
3. Update the `MOCK_FIXTURES` array in `src/mock-api/fixtures-manager.ts` to include your new fixture 