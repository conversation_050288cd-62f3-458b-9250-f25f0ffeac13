/**
 * Fixtures loader for mock API
 * 
 * This file selects and exports the appropriate fixture based on the current fixture ID.
 */

import { getCurrentFixtureId } from '../../fixtures-manager';

// Cache for loaded modules to avoid reloading
const moduleCache = new Map();

// Clear the cache to force reload
export function clearFixtureCache() {
    moduleCache.clear();
    console.log('🧹 Fixture cache cleared');
}

// This approach dynamically imports the fixture based on the ID
// The function will need to be awaited when used
export async function getFixture() {
    try {
        switch (getCurrentFixtureId()) {
            case 1:
                return import('./fixture1');
            case 2:
                return import('./fixture2');
            default:
                console.warn(`Unknown fixture ID: ${getCurrentFixtureId()}, falling back to fixture 1`);
                return import('./fixture1');
        }
    } catch (error) {
        console.error('Error loading fixture:', error);
        return { default: {} };
    }
}

// Helper function to load and cache modules
async function loadModule(path: string) {
    // Always clear the cache entry for this path to ensure fresh data
    moduleCache.delete(path);
    
    try {
        let module;
        // Explicitly handle different fixture paths
        if (path === './fixture1/knowledge.mock') {
            module = await import('./fixture1/knowledge.mock');
        } else if (path === './fixture2/knowledge.mock') {
            module = await import('./fixture2/knowledge.mock');
        } else if (path === './fixture1/issues.mock') {
            module = await import('./fixture1/issues.mock');
        } else if (path === './fixture2/issues.mock') {
            module = await import('./fixture2/issues.mock');
        } else if (path === './fixture1/settings.mock') {
            module = await import('./fixture1/settings.mock');
        } else if (path === './fixture2/settings.mock') {
            module = await import('./fixture2/settings.mock');
        } else if (path === './fixture1/auth.mock') {
            module = await import('./fixture1/auth.mock');
        } else if (path === './fixture2/auth.mock') {
            module = await import('./fixture2/auth.mock');
        } else {
            console.warn(`Unknown module path: ${path}`);
            module = {};
        }
        
        moduleCache.set(path, module);
        console.log(`✅ Successfully loaded module: ${path}`);
    } catch (error) {
        console.error(`❌ Error loading module ${path}:`, error);
        moduleCache.set(path, {});
    }
    
    return moduleCache.get(path);
}

// Get fixture-specific module paths based on current fixture ID
function getFixtureModulePaths() {
    const fixtureId = getCurrentFixtureId();
    const fixturePrefix = fixtureId === 2 ? './fixture2' : './fixture1';
    return [
        `${fixturePrefix}/knowledge.mock`,
        `${fixturePrefix}/issues.mock`,
        `${fixturePrefix}/settings.mock`,
        `${fixturePrefix}/auth.mock`
    ];
}

// Load only the modules for the current fixture
// We must use top-level await which requires ES2022
// (async function preloadModules() {
//     try {
//         const modulePaths = getFixtureModulePaths();
//         await Promise.all(modulePaths.map(path => loadModule(path)));
//         console.log(`Preloaded all modules for fixture ${getCurrentFixtureId()}`);
//     } catch (error) {
//         console.error('Error preloading modules:', error);
//     }
// })();

// Force reload current fixture - used when fixture changes
export async function reloadAllFixtures() {
    // Clear cache first
    clearFixtureCache();
    
    // Reload with fresh data for current fixture only
    try {
        const modulePaths = getFixtureModulePaths();
        await Promise.all(modulePaths.map(path => loadModule(path)));
        console.log(`🔄 All fixtures for fixture ${getCurrentFixtureId()} reloaded successfully`);
        return true;
    } catch (error) {
        console.error('Error reloading fixtures:', error);
        return false;
    }
}

// For imports (used by existing handlers), export functions to get fixtures
export async function getKnowledgeMock() {
    const fixtureId = getCurrentFixtureId();
    const path = fixtureId === 2 
        ? './fixture2/knowledge.mock'
        : './fixture1/knowledge.mock';
    
    return await loadModule(path);
}

export async function getIssuesMock() {
    const fixtureId = getCurrentFixtureId();
    const path = fixtureId === 2 
        ? './fixture2/issues.mock'
        : './fixture1/issues.mock';
    
    return await loadModule(path);
}

export async function getSettingsMock() {
    const fixtureId = getCurrentFixtureId();
    const path = fixtureId === 2 
        ? './fixture2/settings.mock'
        : './fixture1/settings.mock';
    
    return await loadModule(path);
}

export async function getAuthMock() {
    const fixtureId = getCurrentFixtureId();
    const path = fixtureId === 2 
        ? './fixture2/auth.mock'
        : './fixture1/auth.mock';
    
    return await loadModule(path);
} 