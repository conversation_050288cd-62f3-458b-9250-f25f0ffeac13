# Mock Data Organization

This directory contains mock data used by the Mock Service Worker (MSW) handlers for simulating API responses during development and testing.

## File Structure

The mock data is organized by API feature area to make it easier to find and maintain:

- `auth.mock.ts`: Authentication-related mock responses (login, logout, user status)
- `issues.mock.ts`: Issues-related mock responses (listing issues, individual issue details)
- `knowledge.mock.ts`: Knowledge base-related mock responses (KB tree, articles, revisions)
- `settings.mock.ts`: User settings-related mock responses

## Usage

Import the mock data directly from the feature-specific files:

```typescript
// Import specific mock data from feature files
import { mockKBGetListingResponse } from '../data/knowledge.mock';
import { mockGetIssuesResponse } from '../data/issues.mock';
```

## Adding New Mock Data

When adding new mock data:

1. Identify the appropriate feature file based on the API endpoint it simulates
2. Add your mock data to that file
3. Export it with a descriptive name following the `mock[Feature][Action]Response` pattern
4. Update the corresponding handler file to import from the feature-specific mock file

## Central Exports

For convenience, all mock data is re-exported through:

- `index.ts`: Central export point for all mock data 