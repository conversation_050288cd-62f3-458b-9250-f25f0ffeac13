/**
 * Settings mock data responses
 */

export const mockGetSettingsResponse = {
    success: true,
    relay_views: {
        totalCount: 25,
        results: [
            {
                id: '2GAPQM',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-16 13:25:31',
                updated: '2023-09-16 13:25:48',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '4H2F8S',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: ['H3F-36U'],
                label: 'MY VIEW 1',
                type: 3,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-09-25 02:36:09',
                updated: '2023-09-26 04:57:40',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '4T5366',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-16 13:31:25',
                updated: '2023-09-16 13:31:25',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '5GM2VT',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-15 08:28:12',
                updated: '2023-09-15 08:28:12',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '7FF8KK',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Smarttech Sponsor',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'sponsor_partners_id',
                        filter_operator: 'eq',
                        filter_field_lbl: 'Sponsor Organization',
                        filter_operator_lbl: 'is equal to',
                        filter_compare_field: 'H3F',
                        filter_compare_field_lbl: 'SmartTech Technologies',
                    },
                    {
                        filter_field: 'owner_partners_teams_id',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Owner Team',
                        filter_operator_lbl: 'is not in',
                        filter_compare_field: [
                            'H3F-YEN',
                            'H3F-Q9A',
                            'H3F-DRT',
                            'H3F-N5Q',
                            'H3F-RH6',
                            'H3F-JSR',
                            'H3F-XWE',
                        ],
                        filter_compare_field_lbl: [
                            'SmartTech Technical Support',
                            'Sales Demo Team',
                            'SmartTech Sales',
                            'SmartTech Training',
                            'Smarttech Deployment',
                            'Smarttech Installs',
                            'Smarttech Finops',
                        ],
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-11-07 16:38:13',
                updated: '2023-11-07 16:38:13',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '83ATP6',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: '686a9542-6fbc-46c1-8f3a-a1f667421c19',
                team_ids: [],
                label: "All grouped by mimi's journey",
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: 'journey_stage',
                filters: [
                    {
                        filter_compare_field: '686a9542-6fbc-46c1-8f3a-a1f667421c19',
                        filter_compare_field2_lbl: '',
                        filter_compare_field_lbl: '',
                        filter_field: 'journey_id',
                        filter_field_lbl: 'Journey',
                        filter_operator: 'eq',
                        filter_operator_lbl: 'is equal to',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2022-10-21 14:07:53',
                updated: '2022-10-21 14:07:53',
                permSet: null,
                metadata: null,
                _highlighted: false,
                _highlightmap: {},
                cardFields: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '84KL3E',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'TestingG',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: 'status',
                filters: [
                    {
                        filter_field: 'created',
                        filter_operator: 'pastdays',
                        filter_field_lbl: 'Created Date',
                        filter_operator_lbl: 'in last # days',
                        filter_compare_field: '5=>5',
                        filter_compare_field_lbl: ' - ',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2025-02-02 23:30:57',
                updated: '2025-02-07 07:30:29',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                cardFields: [
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 0,
                        type: 1,
                        field: 'display_name',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Case Name',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 1,
                        type: 1,
                        field: 'scheduled_time_ts',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'datetime',
                        lbl: 'Scheduled Time (Customer Timezone)',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 2,
                        type: 1,
                        field: 'c__tags',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Tags',
                    },
                ],
                kanban_allow: true,
                kanban_cols: [
                    {
                        title: 'New',
                        group_property: 'status',
                        group_value: 1,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 1,
                            },
                        ],
                    },
                    {
                        title: 'Ready',
                        group_property: 'status',
                        group_value: 2,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 2,
                            },
                        ],
                    },
                    {
                        title: 'Scheduling',
                        group_property: 'status',
                        group_value: 3,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 3,
                            },
                        ],
                    },
                    {
                        title: 'Scheduled',
                        group_property: 'status',
                        group_value: 4,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 4,
                            },
                        ],
                    },
                    {
                        title: 'En Route',
                        group_property: 'status',
                        group_value: 5,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 5,
                            },
                        ],
                    },
                    {
                        title: 'In Progress',
                        group_property: 'status',
                        group_value: 6,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 6,
                            },
                        ],
                    },
                    {
                        title: 'Resolved',
                        group_property: 'status',
                        group_value: 7,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 7,
                            },
                        ],
                    },
                    {
                        title: 'Pending Close',
                        group_property: 'status',
                        group_value: 9,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 9,
                            },
                        ],
                    },
                    {
                        title: 'Closed',
                        group_property: 'status',
                        group_value: 10,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 10,
                            },
                        ],
                    },
                    {
                        title: 'Waiting',
                        group_property: 'status',
                        group_value: 79,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 79,
                            },
                        ],
                    },
                    {
                        title: 'Canceled',
                        group_property: 'status',
                        group_value: 89,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 89,
                            },
                        ],
                    },
                    {
                        title: 'Deleted',
                        group_property: 'status',
                        group_value: 99,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 99,
                            },
                        ],
                    },
                ],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: '869CGM',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: '',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-09-15 07:56:52',
                updated: '2023-09-15 07:56:52',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'C7KAPA',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: 'status',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-06-26 07:39:43',
                updated: '2023-08-02 03:14:07',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                cardFields: [
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 0,
                        type: 1,
                        field: 'display_name',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Case Name',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 1,
                        type: 1,
                        field: 'scheduled_time_ts',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'datetime',
                        lbl: 'Scheduled Time (Customer Timezone)',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 2,
                        type: 1,
                        field: 'c__tags',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Tags',
                    },
                ],
                kanban_allow: true,
                kanban_cols: [
                    {
                        title: 'New',
                        group_property: 'status',
                        group_value: 1,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 1,
                            },
                        ],
                    },
                    {
                        title: 'Ready',
                        group_property: 'status',
                        group_value: 2,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 2,
                            },
                        ],
                    },
                    {
                        title: 'Scheduling',
                        group_property: 'status',
                        group_value: 3,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 3,
                            },
                        ],
                    },
                    {
                        title: 'Scheduled',
                        group_property: 'status',
                        group_value: 4,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 4,
                            },
                        ],
                    },
                    {
                        title: 'En Route',
                        group_property: 'status',
                        group_value: 5,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 5,
                            },
                        ],
                    },
                    {
                        title: 'In Progress',
                        group_property: 'status',
                        group_value: 6,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 6,
                            },
                        ],
                    },
                    {
                        title: 'Resolved',
                        group_property: 'status',
                        group_value: 7,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 7,
                            },
                        ],
                    },
                    {
                        title: 'Pending Close',
                        group_property: 'status',
                        group_value: 9,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 9,
                            },
                        ],
                    },
                    {
                        title: 'Closed',
                        group_property: 'status',
                        group_value: 10,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 10,
                            },
                        ],
                    },
                    {
                        title: 'Waiting',
                        group_property: 'status',
                        group_value: 79,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 79,
                            },
                        ],
                    },
                    {
                        title: 'Canceled',
                        group_property: 'status',
                        group_value: 89,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 89,
                            },
                        ],
                    },
                    {
                        title: 'Deleted',
                        group_property: 'status',
                        group_value: 99,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 99,
                            },
                        ],
                    },
                ],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'DBBDL8',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: 'owner_partners_teams_id',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-17 23:01:24',
                updated: '2023-09-20 03:34:49',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                cardFields: [
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 0,
                        type: 1,
                        field: 'display_name',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Case Name',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 1,
                        type: 1,
                        field: 'scheduled_time_ts',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'datetime',
                        lbl: 'Scheduled Time (Customer Timezone)',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 2,
                        type: 1,
                        field: 'c__tags',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Tags',
                    },
                ],
                kanban_allow: true,
                kanban_cols: [
                    {
                        title: 'Manager',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-76W',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-76W',
                            },
                        ],
                    },
                    {
                        title: 'SmartTech Sales',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-DRT',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-DRT',
                            },
                        ],
                    },
                    {
                        title: 'Test 2 ABC',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-FCW',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-FCW',
                            },
                        ],
                    },
                    {
                        title: "Jeff's Team SMART",
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-G4M',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-G4M',
                            },
                        ],
                    },
                    {
                        title: 'New Team',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-L6R',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-L6R',
                            },
                        ],
                    },
                    {
                        title: 'SmartTech Training',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-N5Q',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-N5Q',
                            },
                        ],
                    },
                    {
                        title: 'Sales Demo Team (Test)',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-Q9A',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-Q9A',
                            },
                        ],
                    },
                    {
                        title: 'Smarttech Deployment',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-RH6',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-RH6',
                            },
                        ],
                    },
                    {
                        title: 'Smarttech Finops',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-XWE',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-XWE',
                            },
                        ],
                    },
                    {
                        title: 'SmartTech Technical Support',
                        group_property: 'owner_partners_teams_id',
                        group_value: 'H3F-YEN',
                        allow_drag: true,
                        allow_drop: true,
                        filters: [
                            {
                                property: 'owner_partners_teams_id',
                                operator: 'eq',
                                value: 'H3F-YEN',
                            },
                        ],
                    },
                ],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'EYRXSW',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Created Date',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'created',
                        filter_operator: 'between',
                        filter_field_lbl: 'Created Date',
                        filter_operator_lbl: 'is equal to',
                        filter_compare_field: '2023-08-29T18:30:00.000Z=>2023-08-30T18:30:00.000Z',
                        filter_compare_field_lbl: '08/30/2023 ',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-08-23 06:21:45',
                updated: '2023-08-23 06:22:15',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'HQGXM5',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Open Chats',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'created',
                        filter_operator: 'between',
                        filter_field_lbl: 'Created Date',
                        filter_operator_lbl: 'is equal to',
                        filter_compare_field: '2023-08-29T18:30:00.000Z=>2023-08-30T18:30:00.000Z',
                        filter_compare_field_lbl: '08/30/2023 ',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2022-12-12 17:02:39',
                updated: '2023-08-23 06:22:09',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'LM8HVF',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: ['H3F-36U', 'H3F-DRT'],
                label: 'My view 23',
                type: 3,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-09-25 03:06:04',
                updated: '2023-09-25 06:40:16',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'M2UR4V',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Custom Testing field 1',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'testing_field_1',
                        filter_operator: '%%',
                        filter_field_lbl: 'Testing field 1',
                        filter_operator_lbl: 'contains',
                        filter_compare_field: 'Test',
                        filter_compare_field_lbl: 'Test',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-06-12 11:10:26',
                updated: '2023-06-12 11:10:26',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'M6XWAV',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Risk Reviews',
                type: 3,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: 'status',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-08-07 08:37:44',
                updated: '2023-08-08 01:18:50',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                cardFields: [
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 0,
                        type: 1,
                        field: 'display_name',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Case Name',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 1,
                        type: 1,
                        field: 'scheduled_time_ts',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'datetime',
                        lbl: 'Scheduled Time (Customer Timezone)',
                    },
                    {
                        card_id: 'issues',
                        creator_user_id: null,
                        position: 2,
                        type: 1,
                        field: 'c__tags',
                        data: null,
                        created: '',
                        updated: '',
                        metadata: null,
                        conditions: [],
                        object: 'issues',
                        fieldType: 'text',
                        lbl: 'Tags',
                    },
                ],
                kanban_allow: true,
                kanban_cols: [
                    {
                        title: 'New',
                        group_property: 'status',
                        group_value: 1,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 1,
                            },
                        ],
                    },
                    {
                        title: 'Ready',
                        group_property: 'status',
                        group_value: 2,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 2,
                            },
                        ],
                    },
                    {
                        title: 'Scheduling',
                        group_property: 'status',
                        group_value: 3,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 3,
                            },
                        ],
                    },
                    {
                        title: 'Scheduled',
                        group_property: 'status',
                        group_value: 4,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 4,
                            },
                        ],
                    },
                    {
                        title: 'En Route',
                        group_property: 'status',
                        group_value: 5,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 5,
                            },
                        ],
                    },
                    {
                        title: 'In Progress',
                        group_property: 'status',
                        group_value: 6,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 6,
                            },
                        ],
                    },
                    {
                        title: 'Resolved',
                        group_property: 'status',
                        group_value: 7,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 7,
                            },
                        ],
                    },
                    {
                        title: 'Pending Close',
                        group_property: 'status',
                        group_value: 9,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 9,
                            },
                        ],
                    },
                    {
                        title: 'Closed',
                        group_property: 'status',
                        group_value: 10,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 10,
                            },
                        ],
                    },
                    {
                        title: 'Waiting',
                        group_property: 'status',
                        group_value: 79,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 79,
                            },
                        ],
                    },
                    {
                        title: 'Canceled',
                        group_property: 'status',
                        group_value: 89,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 89,
                            },
                        ],
                    },
                    {
                        title: 'Deleted',
                        group_property: 'status',
                        group_value: 99,
                        allow_drag: false,
                        allow_drop: false,
                        filters: [
                            {
                                property: 'status',
                                operator: 'eq',
                                value: 99,
                            },
                        ],
                    },
                ],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'MF5WGL',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-09-15 08:06:57',
                updated: '2025-02-10 20:36:07',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'Q824Z6',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-15 08:37:40',
                updated: '2023-09-15 08:37:40',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'QPXUC7',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-16 13:19:30',
                updated: '2023-09-16 13:19:44',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'R3MMDT',
                owner_org_id: 'H3F',
                creator_users_id: '6RQ',
                journey_id: null,
                team_ids: [],
                label: 'Case Source - SMS',
                type: 3,
                status: 1,
                object: 'issues',
                sort_object: 'created_desc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'source',
                        filter_operator: 'eq',
                        filter_field_lbl: 'Case Source',
                        filter_operator_lbl: 'is equal to',
                        filter_compare_field: 'sms',
                        filter_compare_field_lbl: 'SMS',
                    },
                ],
                columns: {},
                layout: 'grid',
                created: '2022-03-23 06:47:15',
                updated: '2022-11-22 10:13:42',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'R6JP5Q',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Work Orders',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'type',
                        filter_operator: 'eq',
                        filter_field_lbl: 'Case Type',
                        filter_operator_lbl: 'is equal to',
                        filter_compare_field: '3',
                        filter_compare_field_lbl: 'Work Order',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-11-07 16:18:45',
                updated: '2023-11-07 16:18:45',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'SYFJ8V',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Custom Testing field 1',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'testing_field_1',
                        filter_operator: '%%',
                        filter_field_lbl: 'Testing field 1',
                        filter_operator_lbl: 'contains',
                        filter_compare_field: 'test',
                        filter_compare_field_lbl: 'test',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-06-12 11:23:35',
                updated: '2023-06-12 11:23:35',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'TBNMAC',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: 'card',
                created: '2023-09-20 23:55:40',
                updated: '2023-09-21 02:38:55',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'TPYFNX',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'The Owners',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'owner_partners_teams_id',
                        filter_operator: 'eq',
                        filter_field_lbl: 'Owner Team',
                        filter_operator_lbl: 'is equal to',
                        filter_compare_field: 'H3F-YEN',
                        filter_compare_field_lbl: 'SmartTech Technical Support',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-10-30 15:27:35',
                updated: '2023-10-30 15:27:35',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'TQBM7N',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-15 07:58:57',
                updated: '2023-09-15 07:58:57',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                id: 'V6N6DA',
                owner_org_id: 'H3F',
                creator_users_id: '3ET',
                journey_id: null,
                team_ids: [],
                label: 'Status',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_asc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: 'card',
                created: '2023-09-16 13:14:33',
                updated: '2023-09-16 13:14:33',
                permSet: null,
                metadata: null,
                c__journey_id: null,
                _highlighted: false,
                _highlightmap: {},
                kanban_allow: false,
                kanban_cols: [],
                _canWrite: true,
                _uiAccess: {
                    edit: true,
                    delete: true,
                    clone: true,
                    merge: false,
                },
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_issues',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All Cases',
                type: 1,
                status: 1,
                object: 'issues',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_open_issues',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'Open Cases',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: 'status',
                filters: [
                    {
                        filter_field: 'status',
                        filter_operator: 'ni',
                        filter_field_lbl: 'Status',
                        filter_operator_lbl: 'not in',
                        filter_compare_field: [7, 10, 89, 99],
                        filter_compare_field_lbl: 'Closed',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_unassigned_issues',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All Unassigned Cases',
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [
                    {
                        filter_field: 'unclaimed',
                        filter_operator: 'eq',
                        filter_field_lbl: 'Owner User',
                        filter_operator_lbl: 'is null',
                        filter_compare_field: 1,
                        filter_compare_field_lbl: '',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_unassigned_team_issues',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: "My Team's Unassigned Cases",
                type: 2,
                status: 1,
                object: 'issues',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: 'owner_partners_teams_id',
                filters: [
                    {
                        filter_field: 'unclaimed_all',
                        filter_operator: 'eq',
                        filter_field_lbl: 'Owner User',
                        filter_operator_lbl: 'is null',
                        filter_compare_field: 1,
                        filter_compare_field_lbl: '',
                        filter_compare_field2_lbl: '',
                    },
                ],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_members',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'members',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_members_locations',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'members_locations',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_members_users',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'members_users',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_partners',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'partners',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_partners_teams',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'partners_teams',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_members_devices',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'members_devices',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
            {
                _guid_concatenator: '-',
                _guid_length: 6,
                _guid_prefix: null,
                _notification_logs: [],
                params: {
                    snapEnabled: true,
                    reloadOnSave: true,
                    entityCacheEnabled: true,
                    hasArchive: false,
                    hasUploadFiles: false,
                    hasTags: false,
                    hasMetadata: false,
                },
                metadata: null,
                id: 'all_members_devices_dict',
                owner_org_id: 'H3F',
                creator_users_id: '',
                journey_id: null,
                team_ids: [],
                label: 'All',
                type: 1,
                status: 1,
                object: 'members_devices_dict',
                sort_object: 'updated_desc',
                sort_dir: '',
                group_by: '',
                filters: [],
                columns: {},
                layout: '',
                created: '',
                updated: '',
                permSet: null,
                _has_detail: true,
            },
        ],
    },
    current_server_time: '2025-04-15T14:26:10+00:00',
}; 