import { setupServer } from 'msw/node';
import { handlers } from './handlers';
import { afterAll, afterEach, beforeAll } from 'vitest';

/**
 * Setup MSW server for testing
 */
export const server = setupServer(...handlers);

/**
 * Setup MSW server for all tests in a test file
 * Use this in setup files for tests that need mock API responses
 */
export function setupMswForTests() {
    // Start the server before all tests
    beforeAll(() => {
        server.listen({ onUnhandledRequest: 'error' });
    });

    // Reset handlers after each test (important for test isolation)
    afterEach(() => {
        server.resetHandlers();
    });

    // Close server after all tests
    afterAll(() => {
        server.close();
    });
}
