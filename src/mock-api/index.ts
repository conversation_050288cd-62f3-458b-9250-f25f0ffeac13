import { worker, resetHandlers } from './browser';
import { FIXTURE_CHANGE_EVENT } from './fixtures-manager';
import { reloadAllFixtures } from './data/fixtures';

// Store cleanup function
let cleanup: (() => void) | null = null;

/**
 * Configure and start the MSW worker
 */
export async function setupMocks(options = { enabled: true }) {
    if (options.enabled) {
        // Start the worker
        await worker.start({
            onUnhandledRequest: 'bypass', // Don't warn about unhandled requests
        });
        console.info('🔶 Mock Service Worker initialized - API requests will be intercepted');
        // Log more visible message to console
        console.log('%c Mock API ENABLED - All API requests are being intercepted ', 
                    'background: #FFC107; color: #000; font-weight: bold; padding: 4px; border-radius: 2px;');
        // Store the cleanup function
        cleanup = () => {
            console.info('🔶 Mock Service Worker stopped');
            worker.stop();
        };
        return cleanup;
    }
    console.info('🔶 Mock Service Worker is DISABLED - Using real API endpoints');
    console.log('%c Mock API DISABLED - Using real API endpoints ', 
                'background: #F44336; color: #fff; font-weight: bold; padding: 4px; border-radius: 2px;');
    return () => {
        // No-op if not enabled
    };
}

// Listen for fixture changes to reset the worker
if (typeof window !== 'undefined') {
    window.addEventListener(FIXTURE_CHANGE_EVENT, async (event) => {
        console.log('🔄 Fixture change detected:', 
            (event as CustomEvent).detail?.fixtureId);
        
        try {
            // First reload all fixtures to clear cache
            await reloadAllFixtures();
            
            // Then reset handlers to use freshly loaded fixtures
            await resetHandlers();
            
            console.info('✅ Mock API updated with new fixture data');
        } catch (error) {
            console.error('❌ Error updating MSW with new fixture:', error);
        }
    });
}
