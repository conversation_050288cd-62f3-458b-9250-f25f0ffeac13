<script setup lang="ts">
import <PERSON>SelectField from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoPopover from '@services/ui-component-library/components/BravoPopover.vue';
import Popover from 'primevue/popover';
import FroalaEditor from 'froala-editor';

import { ref, computed, watch } from 'vue';

const tokenPopover = ref<any>(null);

const props = defineProps<{
  editor: any;
  tokens: Array<{ id: string; label: string }>;
  initialtokenId: string | null;
}>();

const emit = defineEmits<{
  show: [];
  hide: [];
}>();

const selectedTokenId = ref<string | null>(null);

const buttonLabel = computed(() => {
  return props.initialtokenId ? 'Update Token' : 'Insert Token';
});

// Watch for changes to initialtokenId to set the selected token
watch(() => props.initialtokenId, (newVal) => {
  if (newVal) {
    const tokenId = newVal;
    selectedTokenId.value = tokenId;
  }
}, { immediate: true });

// const show = () => {
//   const toolBarEL = props.editor.$tb.find('[data-cmd="insertToken"]')[0];
//   if (tokenPopover.value) {
//     tokenPopover.value.show(new Event('click'), toolBarEL);
//   }
// };

// Helper function to find the target element
const findTargetElement = (mode: string = 'insert', token: any = null) => {
  let targetEl = null;
  
  if (mode === 'edit' && token) {
    targetEl = props.editor.$el.find(`[data-token=${token}]`)[0];
  }
  
  if (!targetEl) {
    targetEl = props.editor.$tb.find('[data-cmd="insertToken"]')[0];
  }
  
  return targetEl;
};

const show = (mode: string, token: any) => {
  const targetEl = findTargetElement(mode, token);
  
  if (tokenPopover.value && targetEl) {
    tokenPopover.value.show({
      target: targetEl,
      currentTarget: targetEl,
    });
  }
};

const hide = () => {
  if (tokenPopover.value) {
    tokenPopover.value.hide();
  }
};

const toggle = () => {
  const targetEl = findTargetElement();
  
  if (tokenPopover.value && targetEl) {
    tokenPopover.value.toggle({
      target: targetEl,
      currentTarget: targetEl,
    });
  }
};

const onShow = () => {
  // Set initial token when popover opens
  if (props.initialtokenId) {
    const tokenId = props.initialtokenId;
    selectedTokenId.value = tokenId;
  }
  emit('show');
};

const onHide = () => {
  // Clean up when popover closes
  selectedTokenId.value = null;
  emit('hide');
};

const onInsertClick = () => {
  const fullToken = props.tokens.find((t) => t.id === selectedTokenId.value);
 
  if (fullToken) {
    FroalaEditor.PLUGINS.token().insertToken(
      props.editor.$el.find(`[data-token=${props.initialtokenId}]`)[0],
      props.editor, 
      fullToken.id, 
      fullToken.label
    );
  }
  
  hide();
};

const onCancelClick = () => {
  hide();
};

// Expose methods for parent component to control the popover
defineExpose({
  show,
  hide,
  toggle,
});
</script>

<template>
  <BravoPopover
    ref="tokenPopover"
    dismissable
    position="right"
    class="w-[320px] max-h-[280px] overflow-hidden shadow-xl rounded-xl bg-white border border-gray-100"
    @show="onShow"
    @hide="onHide"
  >
    <div class="relative">

      <!-- Content -->
      <div class="px-5 py-4 space-y-4 max-h-[150px] overflow-y-auto">
        <!-- Select Field with enhanced styling -->
        <div class="space-y-2">
          <label for="token-select" class="block text-sm font-medium text-gray-700">
            Available Tokens
          </label>
          <div class="relative">
            <BravoSelectField 
              v-model="selectedTokenId" 
              :options="props.tokens" 
              id="token-select" 
              data-test-id="token-select"
              option-label="label" 
              option-value="id" 
              placeholder="Select a token..."
              class="w-full"
            />
          </div>
        </div>
      </div>

      <!-- Footer Actions -->
      <div class="px-2 py-4 bg-white border-t border-gray-100 flex justify-between items-center gap-3">
        <BravoButton 
          label="Cancel" 
          severity="secondary" 
          text 
          @click="onCancelClick"
          class="text-gray-600 hover:text-gray-800 hover:bg-gray-100 px-4 py-2 rounded-lg font-medium transition-colors"
        />
        
        <BravoButton 
          :label="buttonLabel" 
          severity="primary"
          @click="onInsertClick" 
          :disabled="!selectedTokenId"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors shadow-sm"
        />
      </div>
    </div>
  </BravoPopover>
</template>

<style scoped>
/* Custom scrollbar styling */
.max-h-\[150px\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[150px\]::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.max-h-\[150px\]::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.max-h-\[150px\]::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>