<template>
  <div class="loading-container">
    <svg class="loading-logo" width="50" height="50" viewBox="0 0 24 24" fill="none">
      <path
        class="logo-path path-1"
        d="M12.2749 4.90746C12.7408 4.09014 13.6007 3.58681 14.5311 3.58681H23.6066L21.5659 0H14.495C12.324 0 10.3176 1.17444 9.23057 3.08154L5.68942 9.29421L7.7301 12.881L12.2749 4.90746Z"
        stroke="currentColor"
        stroke-width="1"
        fill="none"
      />
      <path
        class="logo-path path-2"
        d="M14.6357 5.00331H18.7171L22.2553 11.2163C23.3438 13.1277 23.3433 15.4836 22.2541 17.3946L18.7171 23.6H14.6357L19.1753 15.6358C19.644 14.8135 19.6419 13.7994 19.1719 12.9778C18.2273 11.3268 17.2882 9.67255 16.349 8.01833L16.348 8.01641L16.3469 8.01466L16.3434 8.00837C15.7746 7.00642 15.2058 6.00449 14.6357 5.00331Z"
        stroke="currentColor"
        stroke-width="1"
        fill="none"
      />
      <path
        class="logo-path path-3"
        d="M8.83535 15.0099C7.90393 15.0099 7.04331 14.5055 6.5778 13.6868L2.04068 5.7074L0 9.29421L3.53404 15.5095C4.62024 17.4198 6.62837 18.5967 8.80167 18.5967H15.8683L17.909 15.0099H8.83535Z"
        stroke="currentColor"
        stroke-width="1"
        fill="none"
      />
    </svg>
    <div v-if="showText" class="loading-text">
      <slot>Loading...</slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  showText: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.loading-text {
  color: var(--color-text);
  font-size: 0.875rem;
}

.loading-logo {
  transform-origin: center;
  animation: rotate 3s ease-in-out infinite;
  animation-delay: 2.1s;
  color: var(--color-primary, #0084ff);
}

.logo-path {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: draw 0.7s ease-in-out forwards;
}

.path-1 { animation-delay: 0s; }
.path-2 { animation-delay: 0.7s; }
.path-3 { animation-delay: 1.4s; }

@keyframes rotate {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes draw {
  to {
    stroke-dashoffset: 0;
    fill: currentColor;
  }
}
</style> 