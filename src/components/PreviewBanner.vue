<script lang="ts" setup>
import Button from '@services/ui-component-library/components/BravoButton.vue';
import Dialog from '@services/ui-component-library/components/BravoDialog.vue';
import Message from '@services/ui-component-library/components/BravoMessage.vue';
import { useStorage } from '@vueuse/core';
import { ref } from 'vue';

// Use VueUse's useStorage to manage the preview_build localStorage
const previewBuild = useStorage('preview_build', null);

// Modal visibility state
const showModal = ref(false);

// Show the confirmation modal
const showConfirmModal = () => {
    showModal.value = true;
};

// Function to clear preview and refresh
const returnToMainBuild = () => {
    previewBuild.value = null; // Clear the localStorage key
    window.location.reload(); // Perform full page refresh
};
</script>
<template>
    <div v-if="previewBuild" class="preview-banner flex justify-center">
        <Message severity="warn" :closable="false">
            <div class="banner-content">
                <span>Previewing build: {{ previewBuild }}</span>
                <Button label="Return to Main Build" severity="secondary" size="small" @click="showConfirmModal" />
            </div>
        </Message>
    </div>

    <!-- Confirmation Modal -->
    <Dialog
        v-model:visible="showModal"
        header="Confirm Return to Main Build"
        :modal="true"
        :dismissable-mask="true"
        :style="{ width: '25rem' }"
    >
        <p>Are you sure you want to return to the main build? Any unsaved changes may be lost.</p>
        <template #footer>
            <Button label="Cancel" severity="secondary" text @click="showModal = false" />
            <Button label="Confirm" severity="primary" @click="returnToMainBuild" />
        </template>
    </Dialog>
</template>
<style scoped>
.preview-banner {
    position: sticky;
    margin-top: 1rem;
    z-index: 1000;
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}
</style>
