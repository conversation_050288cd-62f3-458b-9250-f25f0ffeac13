<template>
  <div class="knowledge-tree-example">
    <h2>Knowledge Tree</h2>
    
    <div v-if="loading" class="loading">
      Loading knowledge tree...
    </div>
    
    <div v-else-if="error" class="error">
      Error loading knowledge tree: {{ error }}
    </div>
    
    <div v-else>
      <ul class="tree">
        <li v-for="node in knowledgeTree" :key="node.id">
          <span>{{ node.text }}</span>
          <span v-if="node.article_cnt">({{ node.article_cnt }} articles)</span>
          
          <ul v-if="node.children && node.children.length">
            <li v-for="child in node.children" :key="child.id">
              {{ child.text }}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useKnowledgeAPI } from '@/composables/services';
import type { KnowledgeNode } from '@/services/KnowledgeAPI';

// Component state
const knowledgeTree = ref<KnowledgeNode[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);

// Get the knowledge API composable
const knowledgeAPI = useKnowledgeAPI();

// Load knowledge tree on mount
onMounted(async () => {
  try {
    knowledgeTree.value = await knowledgeAPI.fetchKnowledgeTree();
    loading.value = false;
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error occurred';
    loading.value = false;
    console.error('Failed to load knowledge tree:', err);
  }
});
</script>

<style scoped>
.knowledge-tree-example {
  padding: 1rem;
}

.loading, .error {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.loading {
  background-color: #f0f0f0;
}

.error {
  background-color: #fff0f0;
  color: #d00;
}

.tree {
  list-style-type: none;
  padding-left: 0;
}

.tree li {
  padding: 0.5rem 0;
}

.tree ul {
  list-style-type: none;
  padding-left: 1.5rem;
}
</style> 