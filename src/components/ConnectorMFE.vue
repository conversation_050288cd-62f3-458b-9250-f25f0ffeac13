<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { mfeLoader } from '../services/MFELoaderService'
import { mfeAdapter } from '../services/MFEAdapterService'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import { useUserStore } from '@/stores/user'

const props = defineProps<{
  orgId: string,
  connectorId: string,
  instanceId: string,
  staticResources: string[],
  componentName: string,
  context?: Record<string, any>
}>()

const userStore = useUserStore()
const isLoading = ref(true)
const error = ref<string | null>(null)

function getEnvironmentName() {
  const host = window.location.host
  if (host.includes('local-env') || host.includes('localhost')) return 'local-env'
  if (host.includes('stage')) return 'stage'
  if (host.includes('preprod')) return 'preprod'
  if (host.includes('cert')) return 'cert'
  if (host.includes('release')) return 'release'
  if (host.includes('prod') || host.includes('goboomtown.com')) return 'prod'
  return 'stage' // fallback
}

function getCDNBaseUrl() {
  const env = getEnvironmentName()
  const cdnPathMap: Record<string, string> = {
    'local-env': 'https://cdn.stage.goboomtown.com',
    'stage': 'https://cdn.stage.goboomtown.com',
    'release': 'https://cdn.release.goboomtown.com',
    'preprod': 'https://cdn.preprod.goboomtown.com',
    'cert': 'https://cdn.cert.goboomtown.com',
    'prod': 'https://cdn.goboomtown.com',
    'app': 'https://cdn.goboomtown.com',
    'goboomtown': 'https://cdn.goboomtown.com',
  }
  return cdnPathMap[env] || cdnPathMap['stage']
}

function getConnectorsCDNBaseUrl() {
  const env = getEnvironmentName()
  const cdnPathMap: Record<string, string> = {
    'local-env': 'https://cdn-market.stage.goboomtown.com',
    'stage': 'https://cdn-market.stage.goboomtown.com',
    'release': 'https://cdn-market.release.goboomtown.com',
    'preprod': 'https://cdn-market.preprod.goboomtown.com',
    'cert': 'https://cdn-market.cert.goboomtown.com',
    'prod': 'https://cdn-market.goboomtown.com',
    'app': 'https://cdn-market.goboomtown.com',
    'goboomtown': 'https://cdn-market.goboomtown.com',
  }
  return cdnPathMap[env] || cdnPathMap['stage']
}

function resolveResourceUrl(url: string): string {
  if (url.startsWith('cdn:')) {
    return getCDNBaseUrl() + url.substring('cdn:'.length)
  } else if (url.startsWith('connectorsCdn:')) {
    return getConnectorsCDNBaseUrl() + url.substring('connectorsCdn:'.length)
  } else {
    return url
  }
}

function isCssResource(resource: string) {
  return resource.endsWith('.css')
}
function isJsResource(resource: string) {
  return resource.endsWith('.js')
}

function buildMfeContext() {
  const context = {
    orgId: userStore.userData?.group_id || userStore.userData?.orgId || props.orgId,
    userId: userStore.userData?.id,
    teamId: userStore.userData?._teams_ids || userStore.userTeams,
    displayMode: 'output',
    instanceId: props.instanceId,
    feature: 'cases.embeddedApp.v1',
    eventHandler: function(event: any, params: any) {
      console.log('ConnectorMFE: Event received:', event, params)
      if (event.action === 'configure') {
        const title = params.displayName || 'Configure connector';
        const widget = params.componentName || 'connectors';
        const resources = params.staticResources || {};
        const instanceToConfigure = {
          instanceId: params.instanceId,
          connectorId: params.connectorId
        };
        // Handle the configuration
        console.log('Configuring connector:', {
          title,
          widget,
          resources,
          instanceToConfigure
        });
      }
    },
    ...(props.context || {})
  }
  
  console.log('ConnectorMFE: Built context:', context)
  return context
}

async function loadConnectorMFE() {
  isLoading.value = true
  error.value = null
  try {
    console.log('ConnectorMFE: Loading connector with props:', {
      orgId: props.orgId,
      connectorId: props.connectorId,
      instanceId: props.instanceId,
      staticResources: props.staticResources,
      componentName: props.componentName
    })
    
    // Map staticResources to correct URLs
    const resolvedResources = props.staticResources.map(resolveResourceUrl)
    const cssResources = resolvedResources.filter(isCssResource)
    const jsResources = resolvedResources.filter(isJsResource)
    
    console.log('ConnectorMFE: Resolved resources:', {
      css: cssResources,
      js: jsResources
    })
    
    // Load CSS and JS resources using the MFE loader
    console.log('ConnectorMFE: About to load CSS resources:', cssResources)
    await Promise.all(cssResources.map(css => {
      console.log('ConnectorMFE: Loading CSS:', css)
      return mfeLoader.loadStyle(css)
    }))
    
    console.log('ConnectorMFE: About to load JS resources:', jsResources)
    
    // Check what's available before loading
    console.log('ConnectorMFE: Before loading - window keys containing "connector":', 
      Object.keys(window).filter(key => key.toLowerCase().includes('connector')))
    
    await Promise.all(jsResources.map(js => {
      console.log('ConnectorMFE: Loading JS:', js)
      return mfeLoader.loadScript(js)
    }))
    
    // Check what's available after loading
    console.log('ConnectorMFE: After loading - window keys containing "connector":', 
      Object.keys(window).filter(key => key.toLowerCase().includes('connector')))
    console.log('ConnectorMFE: After loading - window keys containing "apilibrary":', 
      Object.keys(window).filter(key => key.toLowerCase().includes('apilibrary')))
    console.log('ConnectorMFE: After loading - window keys containing "ovation":', 
      Object.keys(window).filter(key => key.toLowerCase().includes('ovation')))
    
    console.log('ConnectorMFE: All resources loaded, waiting for adapter...')
    
    // Add listener to see when widgets get registered
    const registrationListener = (e: CustomEvent) => {
      console.log('ConnectorMFE: Widget registered:', e.detail.name, 'Looking for:', props.componentName)
      console.log('ConnectorMFE: Widget details:', e.detail)
      if (e.detail.name === props.componentName) {
        console.log('ConnectorMFE: ✅ Found matching widget!')
      } else {
        console.log('ConnectorMFE: ❌ Widget name mismatch')
      }
    }
    document.addEventListener('registerDynamicWidget', registrationListener)
    
    // Give JS files time to register widgets
    console.log('ConnectorMFE: Waiting 2 seconds for widget registration...')
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('ConnectorMFE: Done waiting, checking for widgets...')
    
    // Wait for the adapter to be ready
    await waitForDynamicWidgetAdapter()
    
    console.log('ConnectorMFE: Adapter ready, checking registered widgets...')
    console.log('ConnectorMFE: Looking for widget:', props.componentName)
    
    console.log('ConnectorMFE: Mounting widget with component name:', props.componentName)
    
    // Build the proper context structure that the adapter expects
    const widgetContext = {
      context: buildMfeContext(),
      config: {
        orgId: props.orgId,
        connectorId: props.connectorId,
        instanceId: props.instanceId
      },
      adminHttpClient: mfeAdapter.httpClient
    }
    
    console.log('ConnectorMFE: Widget context:', widgetContext)
    
    // Mount the MFE using the provided componentName with timeout
    const mountPromise = mfeAdapter.mountWidget(
      props.componentName,
      '#connector-mfe-iframe',
      widgetContext
    )
    
    // Add a timeout to prevent hanging indefinitely
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Widget "${props.componentName}" failed to register within 30 seconds`))
      }, 30000)
    })
    
    await Promise.race([mountPromise, timeoutPromise])
    
    console.log('ConnectorMFE: Successfully mounted widget')
    
    // Clean up the registration listener
    document.removeEventListener('registerDynamicWidget', registrationListener)
  } catch (e) {
    console.error('Failed to load connector MFE:', e)
    console.error('ConnectorMFE: Props were:', {
      orgId: props.orgId,
      connectorId: props.connectorId,
      instanceId: props.instanceId,
      staticResources: props.staticResources,
      componentName: props.componentName
    })
    error.value = 'Failed to load application. Please try refreshing the page.'
  } finally {
    isLoading.value = false
  }
}

function waitForDynamicWidgetAdapter(): Promise<void> {
  return new Promise((resolve) => {
    if (window.DynamicWidgetAdapter) {
      resolve();
      return;
    }

    document.addEventListener('DynamicWidgetAdapterLoaded', () => {
      resolve();
    }, { once: true });
  });
}

onMounted(() => {
  loadConnectorMFE()
})
watch(() => [props.connectorId, props.instanceId, props.staticResources, props.componentName], loadConnectorMFE)
</script>

<template>
  <div class="connector-mfe-view relative">
    <BravoProgressSpinner v-show="isLoading" />
    <div v-show="!isLoading" id="connector-mfe-iframe"></div>
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<style scoped>
.connector-mfe-view {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  height: 100%;
  width: 100%;
  min-height: 200px;
  overflow: auto;
}
.error-message {
  color: var(--red-500);
  text-align: center;
  padding: 2rem;
}
#connector-mfe-iframe {
  flex: 1;
  min-height: 0;
  width: 100%;
}
</style> 