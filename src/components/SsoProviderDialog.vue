<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';

interface SsoProvider {
  logo: string | null;
  name: string;
  description: string;
  url: string;
}

const props = defineProps<{
  visible: boolean;
  providers: SsoProvider[];
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'provider-selected', url: string): void;
}>();

const handleSelect = (url: string) => {
  emit('provider-selected', url);
  emit('update:visible', false);
};

const dialogVisible = ref(props.visible);

watchEffect(() => {
  dialogVisible.value = props.visible;
});

const onDialogVisibleChange = (value: boolean) => {
  emit('update:visible', value);
};
</script>

<template>
  <BravoDialog
    v-model:visible="dialogVisible"
    @update:visible="onDialogVisibleChange"
    modal
    header="Choose Authentication Provider"
    :style="{ width: '500px' }"
    :closable="true"
    class="sso-provider-dialog"
  >
    <div class="provider-list">
      <p class="dialog-description">
        Please select an authentication provider to continue:
      </p>
      
      <div 
        v-for="provider in providers" 
        :key="provider.name" 
        class="provider-item"
        @click="handleSelect(provider.url)"
      >
        <div class="provider-icon">
          <img v-if="provider.logo" :src="provider.logo" :alt="`${provider.name} logo`" class="provider-logo" />
          <div v-else class="provider-logo-placeholder">
            {{ provider.name.charAt(0).toUpperCase() }}
          </div>
        </div>
        <div class="provider-info">
          <h3 class="provider-name">{{ provider.name }}</h3>
          <p class="provider-description">{{ provider.description }}</p>
        </div>
        <div class="provider-action">
          <BravoButton
            icon="pi pi-arrow-right"
            severity="secondary"
            text
            aria-label="Select provider"
          />
        </div>
      </div>
    </div>
    
    <template #footer>
      <BravoButton 
        label="Cancel" 
        severity="secondary" 
        @click="emit('update:visible', false)" 
      />
    </template>
  </BravoDialog>
</template>

<style scoped>
.provider-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dialog-description {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

.provider-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--surface-card, #ffffff);
  border: 1px solid var(--surface-border, #dee2e6);
  cursor: pointer;
  transition: all 0.2s ease;
}

.provider-item:hover {
  background-color: var(--surface-hover, #f8f9fa);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.provider-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.provider-logo {
  max-width: 100%;
  max-height: 100%;
}

.provider-logo-placeholder {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color, #4338ca);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: bold;
  font-size: 1.2rem;
}

.provider-info {
  flex: 1;
}

.provider-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #212529);
}

.provider-description {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6c757d);
}

.provider-action {
  margin-left: auto;
}
</style> 