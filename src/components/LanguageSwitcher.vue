<template>
  <div class="language-switcher">
    <select :value="currentLocale" @change="changeLanguage" aria-label="Select language">
      <option v-for="(name, code) in availableLocales" :key="code" :value="code">
        {{ name }}
      </option>
    </select>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { setLocale } from '../i18n'

const { locale } = useI18n()

const currentLocale = computed(() => locale.value)

const availableLocales = {
  en: 'English',
  es: 'Español'
}

const changeLanguage = (event: Event) => {
  const target = event.target as HTMLSelectElement
  const newLocale = target.value
  setLocale(newLocale)
}
</script>

<style scoped>
.language-switcher {
  display: inline-block;
  margin-left: 1rem;
}

.language-switcher select {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: white;
  cursor: pointer;
}
</style> 