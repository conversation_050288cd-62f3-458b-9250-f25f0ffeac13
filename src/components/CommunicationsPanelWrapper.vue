<script setup lang="ts">
import { ref, onMounted, withDefaults, defineProps } from 'vue'
import { useCasesStore } from '@/stores/cases'
import { useCommunicationsStore } from '@/modules/comms/stores/communications'
import CommunicationPanel from '@/modules/comms/components/CommunicationPanel.vue'
import ParticipantsPanel from '@/modules/comms/components/ParticipantsPanel.vue'
import ProgressSpinner from 'primevue/progressspinner'
import type { Issue } from '@/composables/services/useIssuesAPI'

interface Props {
  issueId: string
  showParticipants?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showParticipants: true
})

const casesStore = useCasesStore()
const communicationsStore = useCommunicationsStore()

const isLoading = ref(true)
const error = ref<string | null>(null)
const currentCase = ref<Issue | null>(null)

const fetchCase = async () => {
  try {
    console.log('🚀 CommunicationsPanelWrapper: Fetching case:', props.issueId)
    
    // Fetch the issue data using the main cases store
    await casesStore.fetchCurrentIssue(props.issueId)
    currentCase.value = casesStore.currentIssue
    
    console.log('✅ CommunicationsPanelWrapper: Case fetched successfully:', currentCase.value?.id)
    
  } catch (err) {
    console.error('❌ CommunicationsPanelWrapper: Failed to fetch case:', err)
    error.value = err instanceof Error ? err.message : 'Failed to fetch case'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchCase()
})
</script>

<template>
  <div class="communications-panel-wrapper">
    <!-- Loading state -->
    <div 
      v-if="isLoading"
      class="flex items-center justify-center h-full bg-white"
    >
      <div class="text-center">
        <ProgressSpinner 
          style="width: 50px; height: 50px;" 
          strokeWidth="4" 
          animationDuration="1.5s"
        />
        <div class="mt-4 text-gray-600">
          Loading case...
        </div>
        <div class="mt-2 text-sm text-gray-500">
          Issue: <strong>{{ props.issueId }}</strong>
        </div>
      </div>
    </div>

    <!-- Error state -->
    <div 
      v-else-if="error"
      class="flex items-center justify-center h-full bg-white"
    >
      <div class="text-center text-red-600">
        <div class="text-lg font-medium">Error</div>
        <div class="mt-2">{{ error }}</div>
      </div>
    </div>

    <!-- Communications panel and participants panel side-by-side -->
    <div 
      v-else-if="currentCase"
      class="flex h-full"
    >
      <!-- Communications panel (dynamic width based on participants visibility) -->
      <div 
        class="flex-none relative"
        :style="{ width: props.showParticipants ? '60%' : '100%' }"
      >
        <CommunicationPanel :case="currentCase" />
      </div>
      
      <!-- Participants panel (40% width, conditionally shown) -->
      <div 
        v-if="props.showParticipants"
        class="flex-none" 
        style="width: 40%;"
      >
        <ParticipantsPanel :communication="communicationsStore.selectedComm" />
      </div>
    </div>

    <!-- No case state -->
    <div 
      v-else
      class="flex items-center justify-center h-full bg-white"
    >
      <div class="text-center text-gray-500">
        <i class="pi pi-exclamation-triangle text-4xl mb-4 text-gray-300"></i>
        <p>No case found</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.communications-panel-wrapper {
  height: 100%;
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  flex-basis: 1200px;
}
</style> 