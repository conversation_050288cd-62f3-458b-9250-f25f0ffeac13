<template>
  <div class="localized-message">
    <h3>{{ $t('common.welcome') }}</h3>
    <p>{{ $t('nav.home') }} | {{ $t('nav.settings') }} | {{ $t('nav.profile') }}</p>
    <div class="buttons">
      <button>{{ $t('common.save') }}</button>
      <button>{{ $t('common.cancel') }}</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

// Access i18n instance if needed for more complex usage
const { t, locale } = useI18n()
</script>

<style scoped>
.localized-message {
  max-width: 600px;
  margin: 2rem auto;
  padding: 1rem;
  border: 1px solid var(--surface-200, #e9ecef);
  border-radius: 4px;
  text-align: center;
}

.buttons {
  margin-top: 1rem;
}

button {
  margin: 0 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary-500, #007bff);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: var(--primary-600, #0069d9);
}
</style> 