import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import SsoProviderDialog from '../SsoProviderDialog.vue';

describe('SsoProviderDialog.vue', () => {
  const mockProviders = [
    {
      logo: null,
      name: 'SAML Identity Provider',
      description: 'SAML protected single sign-on/logout',
      url: 'https://saml.example.com'
    },
    {
      logo: 'https://example.com/oauth-logo.png',
      name: 'OAuth Provider',
      description: 'OAuth 2.0 authentication',
      url: 'https://oauth.example.com'
    }
  ];

  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(SsoProviderDialog, {
      props: {
        visible: true,
        providers: mockProviders
      },
      global: {
        stubs: {
          BravoDialog: {
            template: '<div><slot></slot><slot name="footer"></slot></div>',
            props: ['visible']
          },
          BravoButton: {
            template: '<button class="bravo-button"><slot></slot></button>',
            props: ['label', 'severity', 'text', 'icon']
          }
        }
      }
    });
  });

  it('renders the dialog with provider list', () => {
    expect(wrapper.findAll('.provider-item').length).toBe(2);
  });

  it('displays provider logo placeholder when logo is null', () => {
    const firstProviderItem = wrapper.findAll('.provider-item')[0];
    expect(firstProviderItem.find('.provider-logo-placeholder').exists()).toBe(true);
    expect(firstProviderItem.find('.provider-logo-placeholder').text()).toBe('S');
  });

  it('displays provider logo when logo is provided', () => {
    const secondProviderItem = wrapper.findAll('.provider-item')[1];
    expect(secondProviderItem.find('img.provider-logo').exists()).toBe(true);
    expect(secondProviderItem.find('img.provider-logo').attributes('src')).toBe('https://example.com/oauth-logo.png');
  });

  it('displays provider name and description', () => {
    const firstProviderItem = wrapper.findAll('.provider-item')[0];
    expect(firstProviderItem.find('.provider-name').text()).toBe('SAML Identity Provider');
    expect(firstProviderItem.find('.provider-description').text()).toBe('SAML protected single sign-on/logout');
  });

  it('emits provider-selected event with URL when provider is clicked', async () => {
    const firstProviderItem = wrapper.findAll('.provider-item')[0];
    await firstProviderItem.trigger('click');
    
    expect(wrapper.emitted()['provider-selected']).toBeTruthy();
    expect(wrapper.emitted()['provider-selected'][0]).toEqual(['https://saml.example.com']);
  });

  it('emits update:visible event when cancel button is clicked', async () => {
    await wrapper.find('.bravo-button').trigger('click');
    
    expect(wrapper.emitted()['update:visible']).toBeTruthy();
    expect(wrapper.emitted()['update:visible'][0]).toEqual([false]);
  });
}); 