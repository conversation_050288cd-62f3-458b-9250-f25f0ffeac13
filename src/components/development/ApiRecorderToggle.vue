<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { isApiRecorderEnabled, setApiRecorderEnabled, clearApiRecordings } from '../../utils/apiRecorder';

const enabled = ref(false);
const renderRecorderToggle = import.meta.env.VITE_ALLOW_MSW;

function toggleRecorder() {
    enabled.value = !enabled.value;
    setApiRecorderEnabled(enabled.value);
}

function clearRecordings() {
    clearApiRecordings();
    alert('API recordings cleared');
}

onMounted(() => {
    enabled.value = isApiRecorderEnabled();
});
</script>

<template>
    <div v-if="renderRecorderToggle" class="api-recorder-toggle">
        <button @click="toggleRecorder" :class="{ 'recorder-enabled': enabled }">
            API Recorder: {{ enabled ? 'ON' : 'OFF' }}
        </button>
        <button v-if="enabled" @click="clearRecordings" class="clear-button">Clear</button>
    </div>
</template>

<style scoped>
.api-recorder-toggle {
    position: fixed;
    bottom: 10px;
    right: 140px; /* Position next to the MSW toggle */
    z-index: 9999;
    display: flex;
    gap: 5px;
}

.api-recorder-toggle button {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #9c27b0;
    color: white;
    font-weight: bold;
    border: none;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.api-recorder-toggle button:hover {
    opacity: 1;
}

.api-recorder-toggle button.recorder-enabled {
    background-color: #673ab7;
}

.clear-button {
    background-color: #ff9800 !important;
}
</style>
