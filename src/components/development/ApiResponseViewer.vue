<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { getRecordedApiResponses, isApiRecorderEnabled } from '../../utils/apiRecorder';
import type { RecordedApiResponse } from '../../utils/apiRecorder';

const props = defineProps({
    hideButton: {
        type: Boolean,
        default: false
    }
});

const responses = ref<RecordedApiResponse[]>([]);
const selectedResponse = ref<RecordedApiResponse | null>(null);
const isOpen = ref(false);
const filter = ref('');
const renderRespViewer = import.meta.env.VITE_ALLOW_MSW;

// Computed filtered responses
const filteredResponses = computed(() => {
    if (!filter.value) return responses.value;

    const searchTerm = filter.value.toLowerCase();
    return responses.value.filter(
        (r) =>
            r.functionName.toLowerCase().includes(searchTerm) ||
            r.url.toLowerCase().includes(searchTerm) ||
            r.method.toLowerCase().includes(searchTerm)
    );
});

// Compute button text based on whether the viewer is open and if there are responses
const buttonText = computed(() => {
    if (isOpen.value) {
        return 'Hide Responses';
    }

    if (responses.value.length === 0) {
        return isApiRecorderEnabled() ? 'No Responses Yet' : 'View Responses';
    }

    return `View Responses (${responses.value.length})`;
});

// Format date from ISO string
function formatDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.toLocaleString();
}

// Toggle the viewer panel
function toggleViewer() {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
        loadResponses();
    }
}

// Open the viewer panel
function openViewer() {
    if (!isOpen.value) {
        isOpen.value = true;
        loadResponses();
    }
}

// Close the viewer panel
function closeViewer() {
    isOpen.value = false;
}

// Load responses from localStorage
function loadResponses() {
    responses.value = getRecordedApiResponses();
}

// Refresh responses every 5 seconds when open
let refreshInterval: number | null = null;

// Watch open state to start/stop refresh interval
watch(isOpen, (newValue) => {
    if (newValue) {
        refreshInterval = window.setInterval(loadResponses, 5000);
    } else {
        if (refreshInterval !== null) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }
});

// Initial load on component mount
onMounted(() => {
    loadResponses();
});

// Function to copy URL to clipboard
function copyToClipboard(text: string, event: Event) {
    event.stopPropagation(); // Prevent triggering the parent click event
    navigator.clipboard
        .writeText(text)
        .then(() => {
            const target = event.target as HTMLElement;
            const originalText = target.textContent;
            target.textContent = 'Copied!';

            setTimeout(() => {
                target.textContent = originalText;
            }, 1500);
        })
        .catch((err) => {
            console.error('Failed to copy URL: ', err);
        });
}
</script>

<template>
    <div v-if="renderRespViewer" class="api-response-viewer">
        <button
            v-if="!props.hideButton"
            @click="toggleViewer"
            class="toggle-button"
            :disabled="!isOpen && responses.length === 0 && !isApiRecorderEnabled()"
        >
            {{ buttonText }}
        </button>

        <div v-if="isOpen" class="viewer-panel">
            <div class="viewer-header">
                <h3>Recorded API Responses</h3>
                <input type="text" v-model="filter" placeholder="Filter responses..." class="filter-input" />
                <button @click="loadResponses" class="refresh-button">↻</button>
                <button @click="toggleViewer" class="close-button">×</button>
            </div>

            <div class="viewer-content">
                <div v-if="!isApiRecorderEnabled()" class="recorder-not-enabled">
                    <p>
                        API Recorder is not enabled. Enable it using the "API Recorder" button to start recording API
                        responses.
                    </p>
                </div>
                <template v-else>
                    <div class="response-list">
                        <div
                            v-for="response in filteredResponses"
                            :key="response.id"
                            @click="selectedResponse = response"
                            class="response-item"
                            :class="{ active: selectedResponse?.id === response.id }"
                        >
                            <div class="response-function">{{ response.functionName }}</div>
                            <div class="response-url-container">
                                <div class="response-url">{{ response.url }}</div>
                                <button
                                    class="copy-button"
                                    @click="copyToClipboard(response.url, $event)"
                                    title="Copy URL to clipboard"
                                >
                                    Copy
                                </button>
                            </div>
                            <div class="response-meta">
                                <span class="response-method">{{ response.method }}</span>
                                <span class="response-time">{{ formatDate(response.timestamp) }}</span>
                            </div>
                        </div>
                        <div v-if="filteredResponses.length === 0" class="no-responses">No responses recorded yet</div>
                    </div>

                    <div class="response-details">
                        <div v-if="selectedResponse" class="response-json">
                            <div class="response-json-header">
                                <span>Response Data</span>
                                <div class="header-buttons">
                                    <button
                                        class="copy-button"
                                        @click="
                                            copyToClipboard(JSON.stringify(selectedResponse.response, null, 2), $event)
                                        "
                                        title="Copy complete response data"
                                    >
                                        Copy Response
                                    </button>
                                    <button
                                        class="copy-button"
                                        @click="copyToClipboard(JSON.stringify(selectedResponse, null, 2), $event)"
                                        title="Copy complete API response object including metadata"
                                    >
                                        Copy Full Object
                                    </button>
                                </div>
                            </div>

                            <div class="response-summary">
                                <div class="summary-item">
                                    <span class="summary-label">URL:</span>
                                    <div class="summary-value url-value">
                                        {{ selectedResponse.url }}
                                        <button
                                            class="copy-button-small"
                                            @click="copyToClipboard(selectedResponse.url, $event)"
                                            title="Copy URL"
                                        >
                                            Copy
                                        </button>
                                    </div>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Function:</span>
                                    <span class="summary-value">{{ selectedResponse.functionName }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Method:</span>
                                    <span class="summary-value method-value">{{ selectedResponse.method }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Time:</span>
                                    <span class="summary-value">{{ formatDate(selectedResponse.timestamp) }}</span>
                                </div>
                            </div>

                            <pre>{{ JSON.stringify(selectedResponse.response, null, 2) }}</pre>
                        </div>
                        <div v-else class="no-selection">Select a response to view details</div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<style scoped>
.api-response-viewer {
    position: fixed;
    bottom: 50px;
    right: 10px;
    z-index: 9998;
}

.toggle-button {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #673ab7;
    color: white;
    font-weight: bold;
    border: none;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.toggle-button:hover {
    opacity: 1;
}

.toggle-button:disabled {
    background-color: #9e9e9e;
    cursor: not-allowed;
    opacity: 0.6;
}

.viewer-panel {
    position: fixed;
    bottom: 80px;
    right: 10px;
    width: 80vw;
    height: 70vh;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.viewer-header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background-color: #673ab7;
    color: white;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.viewer-header h3 {
    margin: 0;
    flex: 1;
}

.filter-input {
    padding: 6px 12px;
    border-radius: 4px;
    border: none;
    margin-right: 10px;
    width: 250px;
}

.refresh-button,
.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    margin-left: 10px;
}

.viewer-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.response-list {
    width: 350px;
    overflow-y: auto;
    border-right: 1px solid #eee;
    background-color: #f8f9fa;
}

.response-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.response-item:hover {
    background-color: #f1f3f5;
}

.response-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid #673ab7;
}

.response-function {
    font-weight: bold;
    margin-bottom: 4px;
}

.response-url-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.response-url {
    flex: 1;
    font-size: 0.85rem;
    color: #666;
    word-break: break-all;
    white-space: normal;
    line-height: 1.4;
    padding: 4px;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    background-color: #f5f5f5;
    cursor: text;
    user-select: all;
}

.copy-button {
    font-size: 0.7rem;
    padding: 2px 6px;
    background-color: #673ab7;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.copy-button:hover {
    opacity: 1;
}

.response-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
}

.response-method {
    color: #673ab7;
    font-weight: bold;
}

.response-time {
    color: #888;
}

.response-details {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #fff;
}

.response-json {
    font-family: monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-word;
}

.no-selection,
.no-responses {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #888;
    font-style: italic;
}

.recorder-not-enabled {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    padding: 20px;
    text-align: center;
    color: #d32f2f;
    font-weight: bold;
}

.response-json-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 8px 0;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.header-buttons {
    display: flex;
    gap: 8px;
}

.response-summary {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.summary-item {
    display: flex;
    margin-bottom: 8px;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-label {
    width: 80px;
    font-weight: bold;
    color: #555;
}

.summary-value {
    flex: 1;
}

.url-value {
    word-break: break-all;
    display: flex;
    align-items: center;
    gap: 8px;
}

.method-value {
    font-weight: bold;
    color: #673ab7;
}

.copy-button-small {
    font-size: 0.7rem;
    padding: 1px 4px;
    background-color: #673ab7;
    color: white;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.copy-button-small:hover {
    opacity: 1;
}
</style>
