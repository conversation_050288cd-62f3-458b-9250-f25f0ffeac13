<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { isMswEnabled, setMswEnabled, getCurrentFixtureId, setCurrentFixture, MOCK_FIXTURES, type MockFixture, FIXTURE_CHANGE_EVENT } from '../../mock-api/fixtures-manager';
import { isApiRecorderEnabled, setApiRecorderEnabled, clearApiRecordings, getRecordedApiResponses } from '../../utils/apiRecorder';

// Define props
const props = defineProps({
    apiViewerRef: {
        type: Object,
        default: null
    }
});

const enabled = ref(false);
const apiRecorderEnabled = ref(false);
const isDev = import.meta.env.DEV;
const showModal = ref(false);
const currentFixture = ref<number>(1);
const router = useRouter();
const showToast = ref(false);
const toastMessage = ref('');

// Store original values to detect changes
const originalEnabled = ref(false);
const originalFixture = ref<number>(1);
const originalApiRecorderEnabled = ref(false);

// Track current form values
const formEnabled = ref(false);
const formFixture = ref<number>(1);
const formApiRecorderEnabled = ref(false);

// Computed property to check if any changes were made
const hasChanges = computed(() => {
    return formEnabled.value !== originalEnabled.value || 
           formFixture.value !== originalFixture.value ||
           formApiRecorderEnabled.value !== originalApiRecorderEnabled.value;
});

// Computed property to check if MSW state was toggled
const mswToggled = computed(() => {
    return formEnabled.value !== originalEnabled.value;
});

// Computed property to check if fixture was changed
const fixtureChanged = computed(() => {
    return formFixture.value !== originalFixture.value;
});

// Computed property to check if API recorder state was toggled
const apiRecorderToggled = computed(() => {
    return formApiRecorderEnabled.value !== originalApiRecorderEnabled.value;
});

// Computed property to check if there are API responses to view
const hasApiResponses = computed(() => {
    return getRecordedApiResponses().length > 0;
});

// Watch formEnabled to automatically disable API recorder when Mock API is enabled
watch(formEnabled, (newValue) => {
    if (newValue === true && formApiRecorderEnabled.value === true) {
        formApiRecorderEnabled.value = false;
    }
});

function openModal() {
    // Reset form values to current values
    formEnabled.value = enabled.value;
    formFixture.value = currentFixture.value;
    formApiRecorderEnabled.value = apiRecorderEnabled.value;
    
    // Store original values
    originalEnabled.value = enabled.value;
    originalFixture.value = currentFixture.value;
    originalApiRecorderEnabled.value = apiRecorderEnabled.value;
    
    showModal.value = true;
}

function closeModal() {
    showModal.value = false;
}

function showToastNotification(message: string) {
    toastMessage.value = message;
    showToast.value = true;
    
    // Hide toast after 3 seconds
    setTimeout(() => {
        showToast.value = false;
    }, 7000);
}

function clearRecordings() {
    clearApiRecordings();
    showToastNotification('API recordings cleared');
}

function viewApiResponses() {
    if (props.apiViewerRef) {
        props.apiViewerRef.openViewer();
        closeModal();
    }
}

function applyChanges() {
    // Only apply changes if there are any
    if (hasChanges.value) {
        // Ensure API recorder is disabled if Mock API is enabled
        if (formEnabled.value && formApiRecorderEnabled.value) {
            formApiRecorderEnabled.value = false;
        }
        
        console.log('Applying changes:', { 
            enabled: formEnabled.value, 
            fixture: formFixture.value,
            apiRecorderEnabled: formApiRecorderEnabled.value,
            mswToggled: mswToggled.value,
            fixtureChanged: fixtureChanged.value,
            apiRecorderToggled: apiRecorderToggled.value
        });
        
        // Update the actual values
        enabled.value = formEnabled.value;
        currentFixture.value = formFixture.value;
        apiRecorderEnabled.value = formApiRecorderEnabled.value;
        
        // Apply changes to local storage
        setMswEnabled(enabled.value);
        setApiRecorderEnabled(apiRecorderEnabled.value);
        
        // Close the modal
        closeModal();
        
        // Only reload the page if MSW was toggled on/off
        if (mswToggled.value) {
            router.go(0);
        } else if (fixtureChanged.value) {
            // Set the fixture after modal is closed to trigger the event
            setCurrentFixture(formFixture.value);
            showToastNotification('Fixture updated! Data will refresh on next API call.');
            console.log('🔄 Fixture changed to:', formFixture.value);
        } else if (apiRecorderToggled.value) {
            showToastNotification(`API Recorder ${formApiRecorderEnabled.value ? 'enabled' : 'disabled'}`);
        }
    }
}

function cancelChanges() {
    // Just close the modal without applying changes
    closeModal();
}

function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text).then(() => {
        showToastNotification(`Copied to clipboard: ${text}`);
    });
}

onMounted(() => {
    enabled.value = isMswEnabled();
    formEnabled.value = enabled.value;
    originalEnabled.value = enabled.value;
    
    currentFixture.value = getCurrentFixtureId();
    formFixture.value = currentFixture.value;
    originalFixture.value = currentFixture.value;
    
    apiRecorderEnabled.value = isApiRecorderEnabled();
    formApiRecorderEnabled.value = apiRecorderEnabled.value;
    originalApiRecorderEnabled.value = apiRecorderEnabled.value;
    
    // Listen for fixture change events
    window.addEventListener(FIXTURE_CHANGE_EVENT, (event) => {
        const fixtureId = (event as CustomEvent).detail?.fixtureId;
        console.log('🔄 Fixture change event received:', fixtureId);
    });
});
</script>

<template>
    <div v-if="isDev" class="msw-toggle">
        <button @click="openModal" class="msw-toggle-button" :class="{ 'msw-enabled': enabled }">
            <span>
                Mock API 
                <i class="recording-indicator" :class="{ 'recording-active': apiRecorderEnabled }"></i>
                <i class="pi" :class="enabled ? 'pi-bolt' : 'pi-power-off'"></i>
            </span>
        </button>

        <!-- Toast notification -->
        <div v-if="showToast" class="msw-toast">
            {{ toastMessage }}
        </div>

        <!-- Modal -->
        <div v-if="showModal" class="msw-modal-overlay" @click.self="cancelChanges">
            <div class="msw-modal" @click.stop>
                <div class="msw-modal-header">
                    <h2>Mock API Configuration</h2>
                    <button class="msw-modal-close" @click="cancelChanges">&times;</button>
                </div>

                <!-- Add credentials info block -->
                <div v-if="formEnabled" class="mock-credentials-info">
                    <h3>Mock Login Credentials</h3>
                    <div class="credentials-container">
                        <div class="credential-item">
                            <div><strong>Email:</strong> <EMAIL></div>
                            <div><strong>Password:</strong> B00mtown1!</div>
                            <button class="copy-button" @click="copyToClipboard('<EMAIL>')">Copy Email</button>
                            <button class="copy-button" @click="copyToClipboard('B00mtown1!')">Copy Password</button>
                        </div>
                    </div>
                    <p class="credentials-note">These credentials will work with the mock API when enabled.</p>
                </div>

                <div class="msw-modal-body">
                    <!-- Mock API Toggle switch -->
                    <div class="msw-toggle-container">
                        <label class="msw-toggle-label">
                            <span>Mock API</span>
                            <div class="toggle-switch">
                                <input type="checkbox" v-model="formEnabled">
                                <span class="slider"></span>
                            </div>
                        </label>
                    </div>

                    <!-- API Recorder Toggle switch -->
                    <div class="msw-toggle-container">
                        <label class="msw-toggle-label" :class="{ 'disabled': formEnabled }">
                            <span>API Recorder</span>
                            <div class="toggle-switch" :title="formEnabled ? 'API recording can only be enabled when Mock API is off' : ''">
                                <input type="checkbox" v-model="formApiRecorderEnabled" :disabled="formEnabled">
                                <span class="slider" :class="{ 'disabled': formEnabled }"></span>
                            </div>
                        </label>
                    </div>
                    
                    <!-- View Responses button -->
                    <div class="view-responses-container">
                        <button 
                            :disabled="!hasApiResponses"
                            @click="clearRecordings" 
                            class="msw-button clear-recordings-button"
                            title="Clear all recorded API responses"
                        >
                            Clear
                        </button>
                        <button 
                            @click="viewApiResponses" 
                            class="msw-button view-responses-button"
                            :disabled="!hasApiResponses && !formApiRecorderEnabled"
                        >
                            View Responses
                        </button>
                    </div>

                    <!-- Fixture selector -->
                    <div class="msw-fixture-selector">
                        <h3>Select Mock Data Fixture:</h3>
                        <div class="msw-fixture-options">
                            <div 
                                v-for="fixture in MOCK_FIXTURES" 
                                :key="fixture.id"
                                class="msw-fixture-option"
                                :class="{ 'selected': formFixture === fixture.id }"
                                @click="formFixture = fixture.id"
                            >
                                <div class="msw-fixture-details">
                                    <h4>{{ fixture.name }}</h4>
                                    <p v-if="fixture.description">{{ fixture.description }}</p>
                                </div>
                                <div class="msw-fixture-checkmark" v-if="formFixture === fixture.id">
                                    ✓
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="msw-modal-footer">
                    <div class="msw-button-group">
                        <button class="msw-modal-button msw-cancel-button" @click="cancelChanges">Cancel</button>
                        <button 
                            class="msw-modal-button msw-apply-button" 
                            @click="applyChanges"
                            :disabled="!hasChanges"
                        >
                            Apply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.msw-toggle {
    position: fixed;
    bottom: 10px;
    right: 10px;
    z-index: 9999;
}

.msw-toggle-button {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f44336;
    color: white;
    font-weight: bold;
    border: none;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.msw-toggle-button:hover {
    opacity: 1;
}

.msw-toggle-button.msw-enabled {
    background-color: #4caf50;
}

/* Recording indicator */
.recording-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #888;
    margin-left: 8px;
    position: relative;
    top: -1px;
}

.recording-indicator.recording-active {
    background-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.7);
}

/* Toast notification */
.msw-toast {
    position: fixed;
    bottom: 60px;
    right: 10px;
    background-color: #333;
    color: white;
    padding: 12px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10001;
    max-width: 300px;
    animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(20px); }
}

/* Modal styles */
.msw-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.msw-modal {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.msw-modal-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.msw-modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: #333;
}

.msw-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #777;
}

.msw-modal-body {
    padding: 16px;
}

.msw-modal-footer {
    padding: 16px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

.msw-button-group {
    display: flex;
    gap: 10px;
}

.msw-modal-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.msw-cancel-button {
    background-color: #f5f5f5;
    color: #333;
}

.msw-apply-button {
    background-color: #4caf50;
    color: white;
}

.msw-apply-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Toggle switch styles */
.msw-toggle-container {
    margin: 0 0 24px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.msw-toggle-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4caf50;
}

input:focus + .slider {
    box-shadow: 0 0 1px #4caf50;
}

input:checked + .slider:before {
    transform: translateX(30px);
}

/* Disabled toggle state */
.msw-toggle-label.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.slider.disabled {
    background-color: #ddd;
}

.api-recorder-actions {
    display: flex;
    gap: 8px;
}

.view-responses-container {
    margin: -12px 0 24px 0;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.clear-recordings-button,
.view-responses-button {
    padding: 4px 8px;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
}

.clear-recordings-button {
    background-color: #ff9800;
}

.view-responses-button {
    background-color: #673ab7;
}

.view-responses-button:disabled,
.clear-recordings-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Fixture selector styles */
.msw-fixture-selector {
    margin-top: 20px;
}

.msw-fixture-selector h3 {
    margin: 0 0 12px;
    font-size: 1rem;
    color: #333;
}

.msw-fixture-options {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.msw-fixture-option {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.msw-fixture-option:last-child {
    border-bottom: none;
}

.msw-fixture-option:hover {
    background-color: #f9f9f9;
}

.msw-fixture-option.selected {
    background-color: #e8f5e9;
}

.msw-fixture-details h4 {
    margin: 0 0 4px;
    font-size: 1rem;
    color: #333;
}

.msw-fixture-details p {
    margin: 0;
    font-size: 0.875rem;
    color: #666;
}

.msw-fixture-checkmark {
    color: #4caf50;
    font-weight: bold;
    font-size: 1.25rem;
}

.mock-credentials-info {
    background-color: #e3f2fd;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    border-left: 4px solid #2196f3;
}

.mock-credentials-info h3 {
    margin-top: 0;
    color: #0d47a1;
    font-size: 1rem;
}

.credentials-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.credential-item {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    font-family: monospace;
    background: #fff;
    padding: 8px;
    border-radius: 4px;
}

.credential-item strong {
    color: #0d47a1;
}

.credentials-note {
    font-size: 0.85rem;
    margin-top: 8px;
    color: #555;
}

.copy-button {
    background-color: #e0e0e0;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    margin-left: 4px;
}

.copy-button:hover {
    background-color: #bdbdbd;
}
</style>
