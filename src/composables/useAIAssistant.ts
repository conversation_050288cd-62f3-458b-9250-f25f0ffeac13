import { ref } from 'vue'
import * as summarizerApi from '@/composables/services/useSummarizerApi'
import type { Message } from '@/modules/comms/types'

export function useAIAssistant() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Convert communication messages to API format
  const convertMessagesToAPIFormat = (messages: Message[]): summarizerApi.Message[] => {
    return messages.map(msg => ({
      senderId: msg.senderId || 'unknown',
      content: msg.content || ''
    }))
  }

  // Summarize conversation
  const summarizeConversation = async (messages: Message[]): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const apiMessages = convertMessagesToAPIFormat(messages)
      const response = await summarizerApi.summarizeConversation({ messages: apiMessages })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to summarize conversation'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Generate reply
  const generateReply = async (messages: Message[]): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const apiMessages = convertMessagesToAPIFormat(messages)
      const response = await summarizerApi.generateReply({ messages: apiMessages })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to generate reply'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Reformat response
  const reformatResponse = async (content: string): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.reformatResponse({ content })
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to reformat response'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // General chat completion
  const chatCompletion = async (request: summarizerApi.GeneralOpenAIRequest): Promise<string> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await summarizerApi.chatCompletion(request)
      return response.result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to complete chat request'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    summarizeConversation,
    generateReply,
    reformatResponse,
    chatCompletion
  }
} 