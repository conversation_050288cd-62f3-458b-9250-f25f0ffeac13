import { ref } from 'vue';

export interface LinkPreview {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  isLoading: boolean;
  error?: string;
}

export const useLinkPreview = () => {
  const previews = ref<Map<string, LinkPreview>>(new Map());

  // Extract URLs from text
  const extractUrls = (text: string): string[] => {
    const urlRegex = /(https?:\/\/[^\s<>"{}|\\^`[\]]+)/gi;
    const matches = text.match(urlRegex);
    return matches || [];
  };

  // Fetch metadata for a URL using real metadata extraction
  const fetchLinkPreview = async (url: string): Promise<LinkPreview> => {
    // Check if we already have this preview
    if (previews.value.has(url)) {
      return previews.value.get(url)!;
    }

    // Create initial preview with loading state
    const preview: LinkPreview = {
      url,
      isLoading: true
    };
    
    previews.value.set(url, preview);

    try {
      // Try to fetch real metadata first
      let metadata;
      try {
        metadata = await fetchRealMetadata(url);
      } catch (realError) {
        console.warn('Real metadata fetch failed, falling back to mock data:', realError);
        // Fall back to mock data if real fetch fails
        metadata = await mockFetchMetadata(url);
      }
      
      const updatedPreview: LinkPreview = {
        url,
        title: metadata.title,
        description: metadata.description,
        image: metadata.image,
        siteName: metadata.siteName,
        isLoading: false
      };

      previews.value.set(url, updatedPreview);
      return updatedPreview;

    } catch (error) {
      const errorPreview: LinkPreview = {
        url,
        isLoading: false,
        error: 'Failed to load preview'
      };
      
      previews.value.set(url, errorPreview);
      return errorPreview;
    }
  };

  // Fetch real metadata using Microlink API
  const fetchRealMetadata = async (url: string) => {
    const microlinkUrl = `https://api.microlink.io?url=${encodeURIComponent(url)}&screenshot=false&video=false`;
    
    const response = await fetch(microlinkUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.status !== 'success' || !data.data) {
      throw new Error('Invalid response from metadata service');
    }

    const metadata = data.data;
    
    return {
      title: metadata.title || metadata.og?.title || 'Untitled',
      description: metadata.description || metadata.og?.description || '',
      image: metadata.image?.url || metadata.og?.image || metadata.logo?.url,
      siteName: metadata.publisher || metadata.og?.site_name || new URL(url).hostname
    };
  };

  // Mock function to simulate fetching metadata (fallback when real API fails)
  const mockFetchMetadata = async (url: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Extract domain for site name
    const domain = new URL(url).hostname.replace('www.', '');

    // Simplified mock data as fallback
    return {
      title: `${domain.charAt(0).toUpperCase() + domain.slice(1)}`,
      description: `Content from ${domain}`,
      image: `https://picsum.photos/400/200?random=${Math.floor(Math.random() * 100)}`,
      siteName: domain.charAt(0).toUpperCase() + domain.slice(1)
    };
  };

  // Get preview for a URL
  const getPreview = (url: string): LinkPreview | undefined => {
    return previews.value.get(url);
  };

  // Fetch previews for all URLs in a message
  const fetchPreviewsForMessage = async (content: string): Promise<LinkPreview[]> => {
    const urls = extractUrls(content);
    const previewPromises = urls.map(url => fetchLinkPreview(url));
    return Promise.all(previewPromises);
  };

  return {
    previews,
    extractUrls,
    fetchLinkPreview,
    getPreview,
    fetchPreviewsForMessage
  };
}; 