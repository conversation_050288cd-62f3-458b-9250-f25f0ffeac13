import { useUserStore } from '@/stores/user';
import { computed, reactive } from 'vue';

/**
 * Composable for checking user permissions
 * Uses reactive patterns to efficiently cache permission results
 */
export function usePermissions() {
    const userStore = useUserStore();

    // Cache permission results in reactive object
    const permissions = reactive({
        article: {
            // Knowledge article permissions
            view: computed(() => userStore.hasPermission('kb_view')),
            add: computed(() => userStore.hasPermission('kb_add')),
            edit: computed(() => userStore.hasPermission('kb_edit')),
            delete: computed(() => userStore.hasPermission('kb_del')),
            publish: computed(() => userStore.hasPermission('kb_publish')),
            template: computed(() => userStore.hasPermission('manage_templates')),
        },
        dashboard: {
            // Dashboard/Analytics permissions
            view: computed(() => userStore.hasPermission('dashboards_view')),
            manage: computed(() => userStore.hasPermission('manage_dashboards')),
        },
        // Additional permission groups can be added here
        // For example:
        // issue: { ... },
        // settings: { ... },
    });

    // Helper methods for more semantic usage
    const can = {
        // Article permissions with more readable function names
        viewArticle: () => permissions.article.view,
        addArticle: () => permissions.article.add,
        editArticle: () => permissions.article.edit,
        deleteArticle: () => permissions.article.delete,
        publishArticle: () => permissions.article.publish,
        manageTemplates: () => permissions.article.template,
        
        // Dashboard permissions
        viewDashboards: () => permissions.dashboard.view,
        manageDashboards: () => permissions.dashboard.manage,
    };

    return {
        // Return both the raw permissions object and the semantic helper methods
        permissions,
        can,
    };
}
