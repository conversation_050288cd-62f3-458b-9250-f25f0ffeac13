import { ref } from 'vue';
import type { Ref } from 'vue'
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';

/**
 * Type definitions for file manager
 */
export interface FileItem {
  id: number | string;
  name: string;
  type: string;
  url: string;
  size: number;
  dateUploaded?: Date;
  [key: string]: any; // Allow additional properties
}

export interface FileManagerPageChangeEvent {
  page: number;
  pageSize: number;
  activeTab: string;
  searchQuery: string;
}

/**
 * Composable for managing file uploads and file listing
 * @param objectId The ID of the object the files are associated with
 * @param objectType The type of object the files are associated with (e.g., 'kb_library')
 */
export function useFileManager(objectId: Ref<string | undefined>, objectType: string = 'kb_library') {
  // Initialize the KnowledgeAPI composable
  const knowledgeAPI = useKnowledgeAPI();
  
  // State
  const showFileManager = ref(false);
  const activeTab = ref('images');
  const uploadProgress = ref(0);
  const fileManagerItems = ref<any[]>([]);
  const currentPage = ref(1);
  const totalFileCount = ref(0);
  const isLoadingFiles = ref(false);
  const searchQuery = ref('');

  // File type mapping for filtering
  const typeFilterMap: Record<string, string> = {
    'images': 'image/',
    'videos': 'video/',
    'files': 'application/' // Documents and other files
  };

  /**
   * Fetch files for the current tab, page, and search query
   * @param tabValue The active tab ('images', 'videos', 'files')
   * @param page The current page number
   * @param pageSize The number of items per page
   * @param query The search query
   */
  const fetchFilesForTab = async (
    tabValue: string,
    page: number = 1,
    pageSize: number = 12,
    query: string = ''
  ) => {
    if (!objectId.value) {
      console.warn('Cannot fetch files: No object ID provided');
      return;
    }

    isLoadingFiles.value = true;

    try {
      const typePattern = typeFilterMap[tabValue as keyof typeof typeFilterMap] || '';

      // Build filter array
      const filters: Array<{ property: string; value: any; operator?: string }> = [
        { property: 'object', value: objectType},
        { property: 'object_id', value: objectId.value },
        { property: 'tag', value: 'managed' }
      ];

      // Add type filter based on tab
      if (typePattern) {
        filters.push({ property: 'type', operator: '%%', value: typePattern });
      }

      // Add search query if provided
      if (query) {
        filters.push({ property: 'name', operator: '%%', value: query });
      }

      // Calculate start index for pagination
      const start = (page - 1) * pageSize;

      const result = await knowledgeAPI.fetchKnowledgeFileManager({
        filter: filters,
        page: page,
        start: start,
        limit: pageSize,
        query: query || undefined
      });

      // Process the items to ensure they have all required properties
      const processedItems = (result.items || []).map(item => {
        // Make sure each item has the required properties
        const processedItem: FileItem = {
          id: (item as any).id || (item as any).file_id || Math.random().toString(36).substring(2, 9),
          name: (item as any).name || (item as any).file_name || 'Unknown file',
          type: (item as any).type || (item as any).file_type || 'application/octet-stream',
          url: (item as any).url || (item as any).file_url || '',
          size: (item as any).size || (item as any).file_size || 0,
          dateUploaded: (item as any).dateUploaded || (item as any).created || new Date()
        };

        // Add any other properties from the original item
        for (const key in item) {
          if (key !== 'id' && key !== 'name' && key !== 'type' &&
              key !== 'url' && key !== 'size' && key !== 'dateUploaded') {
            (processedItem as any)[key] = (item as any)[key];
          }
        }

        return processedItem;
      });

      fileManagerItems.value = processedItems;
      totalFileCount.value = result.totalCount || 0;
      console.log(`Loaded ${fileManagerItems.value.length} files for tab ${tabValue}, page ${page}, total: ${totalFileCount.value}`);
      console.log('Processed items:', processedItems);
    } catch (error) {
      console.error('Error fetching files:', error);
      fileManagerItems.value = [];
      totalFileCount.value = 0;
    } finally {
      isLoadingFiles.value = false;
    }
  };

  /**
   * Handle file manager pagination, search, and tab changes
   * @param event The page change event from the FileManager component
   */
  const handleFileManagerPageChange = async (event: FileManagerPageChangeEvent) => {
    console.log('File manager page change event:', event);
    currentPage.value = event.page;
    activeTab.value = event.activeTab;
    searchQuery.value = event.searchQuery;

    await fetchFilesForTab(event.activeTab, event.page, event.pageSize, event.searchQuery);
  };

  /**
   * Upload a file to the server
   * @param file The file to upload
   * @param onSuccess Optional callback for successful upload
   * @param onError Optional callback for upload error
   */
  const uploadFile = async (
    file: File,
    onSuccess?: () => void,
    onError?: (error: Error) => void
  ) => {
    if (!objectId.value) {
      const error = new Error('Cannot upload file: No object ID provided');
      if (onError) onError(error);
      else console.error(error);
      return;
    }

    // Initialize progress
    uploadProgress.value = 1;
    const fileTag = 'managed';

    try {
      // Upload the file with progress tracking
      await knowledgeAPI.postFileManagerFile(
        file,
        objectType,
        objectId.value,
        fileTag,
        (progress: number) => {
          uploadProgress.value = progress;
        }
      );

      // Set to 100% when complete
      uploadProgress.value = 100;

      // Refresh the file list after successful upload
      await fetchFilesForTab(activeTab.value, currentPage.value, 12, searchQuery.value);

      // Reset progress after a delay
      setTimeout(() => {
        uploadProgress.value = 0;
      }, 1000);

      if (onSuccess) onSuccess();
    } catch (error) {
      uploadProgress.value = 0;
      console.error('Upload failed:', error);
      if (onError && error instanceof Error) onError(error);
    }
  };

  /**
   * Delete a file from the server
   * @param file The file to delete
   * @param removeFromArticles Whether to remove the file from articles
   * @param onSuccess Optional callback for successful deletion
   * @param onError Optional callback for deletion error
   */
  const deleteFile = async (
    file: FileItem,
    removeFromArticles: boolean = false,
    onSuccess?: () => void,
    onError?: (error: Error) => void
  ) => {
    if (!objectId.value) {
      const error = new Error('Cannot delete file: No object ID provided');
      if (onError) onError(error);
      else console.error(error);
      return;
    }

    try {
      // Use the KnowledgeAPI to delete the file
      await knowledgeAPI.deleteFileManagerFile(
        file.id,
        objectType,
        objectId.value,
        removeFromArticles
      );

      // Refresh the file list after successful deletion
      await fetchFilesForTab(activeTab.value, currentPage.value, 12, searchQuery.value);

      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Delete failed:', error);
      if (onError && error instanceof Error) onError(error);
    }
  };

  return {
    // State
    showFileManager,
    activeTab,
    uploadProgress,
    fileManagerItems,
    currentPage,
    totalFileCount,
    isLoadingFiles,
    searchQuery,

    // Methods
    fetchFilesForTab,
    handleFileManagerPageChange,
    uploadFile,
    deleteFile
  };
}
