import { computed, type Ref, type ComputedRef } from 'vue'
import type { Issue } from '@/composables/services/useIssuesAPI'

export interface TokenMap {
  [key: string]: string | undefined
}

export function useCannedResponseTokens(issue: Issue | null | Ref<Issue | null> | ComputedRef<Issue | null>) {
  // Create a computed token map based on the current issue
  const tokenMap = computed<TokenMap>(() => {
    // Unwrap the reactive value
    const currentIssue = typeof issue === 'object' && issue !== null && 'value' in issue ? issue.value : issue
    if (!currentIssue) return {}

    const memberUser = currentIssue.memberUser || {}
    const member = currentIssue.member || {}
    const location = currentIssue.location || {}

    return {
      // Customer User tokens
      CUSTOMER_USER_FIRST_NAME: memberUser.first_name || '',
      CUSTOMER_USER_LAST_NAME: memberUser.last_name || '',
      CUSTOMER_USER_FULL_NAME: memberUser.full_name || 
        (memberUser.first_name && memberUser.last_name 
          ? `${memberUser.first_name} ${memberUser.last_name}` 
          : ''),
      CUSTOMER_USER_EMAIL: memberUser.email || member.email || '',
      CUSTOMER_USER_SMS: memberUser.sms_number || '',

      // Issue tokens
      ISSUE_REFERENCE_NUMBER: currentIssue.reference_num || '',
      ISSUE_PRODUCT: currentIssue.c__technology_label || '',
      ISSUE_SCHEDULED_DATE: currentIssue.scheduled_time || '',
      ISSUE_CUSTOMER_SURVEY: currentIssue.survey_name || '',
      ISSUE_PRODUCT_STATUS: currentIssue.c__d_status || '',

      // Additional commonly used tokens
      CUSTOMER_NAME: currentIssue.c__name || currentIssue.c__contact_name || '',
      CUSTOMER_PHONE: currentIssue.c__contact_sms || location.phone || '',
      CUSTOMER_EMAIL: memberUser.email || member.email || '',
      CASE_ID: currentIssue.id || '',
      CASE_REFERENCE: currentIssue.reference_num || '',
      CASE_TITLE: currentIssue.display_name || '',
      LOCATION_NAME: location.name || location.site_name || '',
      LOCATION_PHONE: currentIssue.c__location_phone || location.phone || '',
      OWNER_NAME: currentIssue.c__owner_user_name || '',
      OWNER_EMAIL: currentIssue.c__owner_user_email || '',
      TECHNICIAN_NAME: currentIssue.c__technician_name || '',
      COMPANY_NAME: currentIssue.c__owner_partner_name || '',
    }
  })

  /**
   * Replace tokens in a text string with actual values
   * @param text The text containing tokens like {CUSTOMER_USER_FIRST_NAME}
   * @returns The text with tokens replaced by actual values
   */
  const replaceTokens = (text: string): string => {
    if (!text) return ''

    try {
      let result = text
      const tokens = tokenMap.value

      // Replace each token with its corresponding value
      Object.entries(tokens).forEach(([token, value]) => {
        const tokenPattern = new RegExp(`\\{${token}\\}`, 'g')
        result = result.replace(tokenPattern, value || '')
      })

      return result
    } catch (error) {
      console.error('Error replacing tokens:', error)
      return text // Return original text if there's an error
    }
  }

  /**
   * Get all available tokens for reference
   * @returns Array of available token names
   */
  const getAvailableTokens = (): string[] => {
    return Object.keys(tokenMap.value).map(token => `{${token}}`)
  }

  /**
   * Process a canned response content by replacing tokens
   * @param content The HTML content from the canned response
   * @returns The content with tokens replaced
   */
  const processCannedResponse = (content: string): string => {
    return replaceTokens(content)
  }

  return {
    tokenMap,
    replaceTokens,
    getAvailableTokens,
    processCannedResponse
  }
} 