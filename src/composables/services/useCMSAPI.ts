import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface SchemaField {
  layout_id: string;
  creator_user_id: string | null;
  position: number;
  type: number;
  field: string;
  data: any;
  created: string;
  updated: string;
  metadata: any;
  c__creator_user_id: string | null;
  c__condition: string;
  _highlighted: boolean;
  _highlightmap: Record<string, any>;
  conditions: any[];
  object: string;
  fieldType?: string;
  decimalPlaces?: number;
  required: boolean;
  requiredResolve: boolean;
  readOnly: boolean;
  lbl: string;
  options?: Array<{
    id: string;
    lbl: string;
    val: string;
    default: boolean;
  }>;
}

export interface CMSLayout {
  id: string;
  partners_id: string;
  creator_user_id: string | null;
  object: string;
  title: string;
  description: string | null;
  data: any;
  created: string;
  updated: string;
  metadata: any;
  c__creator_user_id: string | null;
  c__description: string | null;
  _highlighted: boolean;
  _highlightmap: Record<string, any>;
  _has_detail: boolean;
  isTemplate: boolean;
  c__d_object: string;
  schemaFields: SchemaField[];
  _canWrite: boolean;
  _uiAccess: {
    edit: boolean;
    delete: boolean;
    clone: boolean;
    merge: boolean;
  };
  previewObjectId?: string;
}

export interface CMSLayoutsResponse {
  success: boolean;
  message?: string;
  partners_layouts?: {
    results: CMSLayout[];
    totalCount: number;
  };
  current_server_time: string;
}

export interface FetchLayoutsParams {
  page?: number;
  start?: number;
  limit?: number;
  filter?: Array<{
    property: string;
    value: string;
  }>;
}

/**
 * Composable that provides access to CMS API functions.
 */
export function useCMSAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async fetchLayouts(params: FetchLayoutsParams = {}): Promise<{ results: CMSLayout[]; totalCount: number }> {
      console.log('CMSAPI: Fetching layouts with params:', params);
      
      const data = await httpClient.get<CMSLayoutsResponse>('admin/v4/cms/', {
        'sAction': 'layoutsListing',
        'page': params.page?.toString() || '1',
        'start': params.start?.toString() || '0',
        'limit': params.limit?.toString() || '25',
        'filter': JSON.stringify(params.filter || [
          { property: 'id', value: '_no_filter_' },
          { property: 'partners_id', value: '_no_filter_' },
          { property: 'object', value: '_no_filter_' }
        ])
      });

      console.log('CMSAPI: Layouts response:', data);
      
      if (data.success && data.partners_layouts?.results) {
        return {
          results: data.partners_layouts.results,
          totalCount: data.partners_layouts.totalCount || 0
        };
      }
      
      throw new Error(data.message || 'Failed to fetch CMS layouts');
    }
  };
} 