import { getHttpClient } from '@/services/httpClientProvider'
import { useAuthStore } from '@/stores/auth'
import type {
  CommParticipant,
  CommData,
  ContactData,
  GetCommParams,
  GetCommResponse,
  UpdateCommPresenceParams,
  UpdateCommPresenceResponse
} from '@/modules/inbox/types/communications'

// Email sending types
export interface EmailReplyData {
  to: string;
  toLabel: string;
  cc?: string[];
  subject: string;
  replyBody: string;
  from: string;
  from_name: string;
  from_email: string;
  origParts?: any[];
}

export interface SendEmailParams {
  id: string; // Communication ID
  commId: string;
  from_email: string;
  replyBody: string;
  subject: string;
  reply: EmailReplyData;
  files?: File[];
  file_tag?: string;
}

export interface SendEmailResponse {
  success: boolean;
  // TODO: Define the actual response structure when we know what the API returns
  [key: string]: any;
}

// Communication label update types
export interface UpdateCommLabelParams {
  id: string;
  comm_label: string;
}

export interface UpdateCommLabelResponse {
  success: boolean;
  comm: CommData;
}

// Chat file upload types
export interface PutFileParams {
  id: string; // Communication ID
  file_tag: string;
  message: string;
  files: File[];
}

export interface PutFileAttachment {
  name: string;
  size: number;
  type: string;
  id: string;
  preview: string;
  url: string;
}

export interface PutFilePayload {
  timestamp: string;
  ttl: null;
  message: string;
  context: {
    from: string;
    from_name: string;
    from_nickname: string;
    from_avatar: string;
    to: string;
    to_name: string;
    to_nickname: string;
    to_user: null;
    to_avatar: string;
  };
  attachment: PutFileAttachment;
  email: Record<string, unknown>;
  kb: null;
  revision: null;
  actions: any[];
  secret: string;
  channel: string;
  notificationsEnable: boolean;
}

export interface PutFileResponse {
  success: boolean;
  payloads: PutFilePayload[];
  current_server_time: string;
}

export function useCommsAPI() {
  const authStore = useAuthStore()
  const httpClient = getHttpClient(authStore)

  return {
    /**
     * Get communication details including external_id (XMPP room JID)
     */
    async getComm(params: GetCommParams): Promise<CommData> {
      try {
        const queryParams = {
          sAction: 'itemGet',
          id: params.id,
          ...(params.is_enter && { is_enter: 'true' })
        }

        const response = await httpClient.get<GetCommResponse>('/admin/v4/comm/', queryParams)
        
        if (!response.success) {
          throw new Error(`Failed to get communication: ${response}`)
        }

        return response.comm
      } catch (error) {
        console.error('Error fetching communication:', error)
        throw error
      }
    },

    /**
     * Update communication presence (for leaving rooms)
     */
    async updateCommPresence(params: UpdateCommPresenceParams): Promise<CommData> {
      try {
        const formData = new URLSearchParams()
        formData.append('comm', JSON.stringify([{
          presence: params.presence,
          id: params.id
        }]))
        formData.append('stringify', 'false')

        const response = await httpClient.post<UpdateCommPresenceResponse>(
          '/admin/v4/comm/',
          formData,
          { sAction: 'itemPut' },
          { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
        )
        
        if (!response.success) {
          throw new Error(`Failed to update communication presence: ${response}`)
        }

        return response.comm
      } catch (error) {
        console.error('Error updating communication presence:', error)
        throw error
      }
    },

    /**
     * Update communication label
     */
    async updateCommLabel(params: UpdateCommLabelParams): Promise<CommData> {
      try {
        const formData = new URLSearchParams()
        formData.append('comm', JSON.stringify([{
          id: params.id,
          comm_label: params.comm_label
        }]))
        formData.append('stringify', 'false')

        const response = await httpClient.post<UpdateCommLabelResponse>(
          '/admin/v4/comm/',
          formData,
          { sAction: 'itemPut' },
          { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
        )
        
        if (!response.success) {
          throw new Error(`Failed to update communication label: ${response}`)
        }

        return response.comm
      } catch (error) {
        console.error('Error updating communication label:', error)
        throw error
      }
    },

    /**
     * Send an email with optional attachments
     */
    async sendEmail(params: SendEmailParams): Promise<SendEmailResponse> {
      try {
        const formData = new FormData()
        
        // Add required fields
        formData.append('id', params.id)
        formData.append('commId', params.commId)
        formData.append('from_email', params.from_email)
        formData.append('replyBody', params.replyBody)
        formData.append('subject', params.subject)
        formData.append('reply', JSON.stringify(params.reply))
        
        // Add optional file tag
        if (params.file_tag) {
          formData.append('file_tag', params.file_tag)
        }
        
        // Add files if provided
        if (params.files && params.files.length > 0) {
          params.files.forEach((file, index) => {
            formData.append(`file_${index}`, file)
          })
        }

        const response = await httpClient.post<SendEmailResponse>(
          '/admin/v4/comm/',
          formData,
          { sAction: 'rep' },
          { 'content-type': 'multipart/form-data' }
        )
        
        if (!response.success) {
          throw new Error(`Failed to send email: ${response}`)
        }

        return response
      } catch (error) {
        console.error('Error sending email:', error)
        throw error
      }
    },

    /**
     * Upload files for chat communication
     */
    async putFile(params: PutFileParams): Promise<PutFileResponse> {
      try {
        const formData = new FormData()
        
        // Add required fields
        formData.append('id', params.id)
        formData.append('file_tag', params.file_tag)
        formData.append('message', params.message)
        
        // Add files with indexed naming
        params.files.forEach((file, index) => {
          formData.append(`file_${index}`, file)
        })
        
        // Add fileId array for each file
        params.files.forEach((file) => {
          formData.append('fileId[]', file.name)
        })

        const response = await httpClient.post<PutFileResponse>(
          '/api/comm/',
          formData,
          { sAction: 'putFile' },
          { 'content-type': 'multipart/form-data' }
        )
        
        if (!response.success) {
          throw new Error(`Failed to upload files: ${response}`)
        }

        return response
      } catch (error) {
        console.error('Error uploading files:', error)
        throw error
      }
    }
  }
}

// Re-export types for convenience
export type {
  CommParticipant,
  CommData,
  ContactData,
  GetCommParams,
  GetCommResponse,
  UpdateCommPresenceParams,
  UpdateCommPresenceResponse
} 