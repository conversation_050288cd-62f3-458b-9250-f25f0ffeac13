import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface SSOCheckResponse {
    success: boolean;
    canAuthenticateInternal: boolean;
    canAuthenticateExternal: boolean;
    externalIdPs: Array<{
        logo: string | null;
        name: string;
        description: string;
        url: string;
    }>;
    current_server_time: string;
}

export interface UserProfileData {
    id: string;
    first_name: string;
    last_name?: string;
    email?: string;
    image?: string;
    nickname?: string;
    phone_number?: string;
    phone_ext?: string;
    time_zone?: string;
    country_id?: string;
    [key: string]: any;
}

export interface UserResponse {
    success: boolean;
    message?: string;
    data?: any;
    users?: any;
}

export interface AvatarUploadResponse {
    success: boolean;
    uploads_files_ids: string[];
    file_keys: string[];
    file_tags: string[];
    file_names: string[];
    file_names2: string[];
    file_types: string[];
    link: string;
    links: string[];
    url_avatar: string;
    current_server_time: string;
    message?: string;
}

export interface ChangePasswordData {
    id: string;
    currentPassword: string;
    newPassword: string;
}

/**
 * Composable that provides access to User API functions.
 * This safely initializes the httpClient with authStore only when called
 * inside components or other composables after Pinia is ready.
 */
export function useUserAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async checkSSO(email: string): Promise<SSOCheckResponse> {
            const response = await fetch(
                `${import.meta.env.VITE_BOOMTOWN_API_HOST}/admin/v4/core/?sAction=getUserAuth`,
                {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                        'x-boomtown-client-instance-id': authStore.clientInstanceId,
                        'x-boomtown-csrf-token': authStore.csrfToken,
                        'x-requested-with': 'XMLHttpRequest',
                    },
                    body: `email=${encodeURIComponent(email)}`,
                    credentials: 'omit',
                }
            );

            if (!response.ok) {
                throw new Error('Failed to check SSO status');
            }

            return response.json();
        },

        async updateUserProfile(profileData: UserProfileData): Promise<UserResponse> {
            try {
                // Create URLSearchParams object for form-encoded data
                const payload = new URLSearchParams();
                payload.append('users', JSON.stringify([profileData]));
                payload.append('stringify', 'false');

                const data = await httpClient.post<UserResponse>(
                    'admin/v4/users/',
                    payload,
                    {
                        sAction: 'put',
                    },
                    {
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    }
                );

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to update user profile');
            } catch (error) {
                console.error('Error updating user profile:', error);
                throw error;
            }
        },

        async uploadAvatar(file: Blob, userId: string): Promise<AvatarUploadResponse> {
            try {
                // Refresh the CSRF token to ensure we have the latest one
                try {
                    await authStore.refreshCsrfToken();
                    console.log('CSRF token refreshed before avatar upload');
                } catch (tokenError) {
                    console.warn('Failed to refresh CSRF token before upload:', tokenError);
                    // Continue with the current token
                }

                // Create FormData object
                const formData = new FormData();
                
                // Add required form fields based on the cURL example
                formData.append('object', 'users');
                formData.append('object_id', userId);
                formData.append('file_tag', 'avatar');
                formData.append('_csrf_token', httpClient.csrf);
                formData.append('_client_instance_id', httpClient.instanceId);
                formData.append('file', file, 'avatar.jpg');
                formData.append('note', 'null');

                console.log('Uploading avatar for user:', userId, 'with CSRF token:', httpClient.csrf);

                // Use fetch to upload the avatar
                const response = await fetch(`/admin/v4/files/?sAction=putFile`, {
                    method: 'POST',
                    headers: {
                        'x-boomtown-client-instance-id': httpClient.instanceId,
                        'x-boomtown-csrf-token': httpClient.csrf,
                        'x-request-id': crypto.randomUUID(),
                    },
                    body: formData,
                    credentials: 'include',
                    mode: 'cors'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Avatar upload response:', data);

                if (data.success) {
                    return data as AvatarUploadResponse;
                }

                throw new Error(data.message || 'Failed to upload avatar');
            } catch (error) {
                console.error('Error uploading avatar:', error);
                throw error;
            }
        },
        
        async getPartnerMetadata(): Promise<any> {
            return httpClient.get<any>('admin/v4/core/?sAction=metaPartners');
        },

        async changePassword(passwordData: ChangePasswordData): Promise<UserResponse> {
            try {
                // Create URLSearchParams object for form-encoded data
                const payload = new URLSearchParams();
                payload.append('currentPassword', passwordData.currentPassword);
                payload.append('newPassword', passwordData.newPassword);
                payload.append('userId', passwordData.id);

                const data = await httpClient.post<UserResponse>(
                    'admin/v4/users/?sAction=changePassword',
                    payload,
                    {},
                    {
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    }
                );

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to change password');
            } catch (error) {
                console.error('Error changing password:', error);
                throw error;
            }
        }
    };
}
