// Get the connectors service URL from environment variable, fallback to local proxy path
const CONNECTORS_SERVICE_URL = import.meta.env.VITE_CONNECTORS_SERVICE_URL || '/service-connectors-stage'

function generateUUID() {
  // Simple UUID v4 generator
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

export async function fetchConnectors(orgId: string) {
  const requestId = generateUUID()
  const response = await fetch(`${CONNECTORS_SERVICE_URL}/connectors/instances?orgId=${encodeURIComponent(orgId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.cases/col4:ui',
      'x-boomtown-request-id': requestId,
      // Add any other required headers here
    },
    method: 'GET',
    credentials: 'omit',
  });
  if (!response.ok) throw new Error('Failed to fetch connectors');
  return await response.json();
} 