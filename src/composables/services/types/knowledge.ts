export interface KnowledgeNode {
    id: string;
    text: string;
    leaf: boolean;
    expanded?: boolean;
    children?: KnowledgeNode[];
    iconCls?: string;
    qtip?: string;
    article_cnt?: number;
    isLibrary?: boolean;
    _uiAccess?: object;
    root_kb_id?: string;
}

export interface KnowledgeItem {
    id: string;
    title: string;
    content: string;
    sub_title?: string;
    type?: string;
    status?: string;
    access?: string;
    updatedAt?: string;
    tags?: string[];
    body?: string;
    metadata?: {
        created_at?: string;
        updated_at?: string;
        tags?: string[];
        author?: string;
        category?: string;
    };
}

export interface KnowledgeListingResponse {
    success: boolean;
    message?: string;
    data?: {
        items: KnowledgeItem[];
        totalCount: number;
    };
    kb?: {
        results: KnowledgeItem[];
        totalCount: number;
    };
    upload_files?: {
        results: any[];
        totalCount: number;
    };
}

export interface KnowledgeResponse {
    success: boolean;
    message?: string;
    children?: KnowledgeNode[];
    data?: KnowledgeItem;
}

export interface FetchKnowledgeParams {
    filter?: Array<{
        property: string;
        value: any;
        operator?: string;
    }>;
    node?: string;
}

export interface ListingParams {
    page?: number;
    start?: number;
    limit?: number;
    query?: string;
    sort?: string;
    filter?: Array<{
        property: string;
        value: any;
        operator?: string;
    }>;
}

export interface KnowledgeRevision {
    id: string;
    body?: string;
    _copy_revision?: boolean;
    _publish?: boolean;
    status?: number;
}

export interface RelatedArticle {
    id: string;
    lbl: string;
    title: string;
    body?: string;
    category?: string;
    updated?: string;
    created?: string;
    status?: string;
    url?: string;
}

export interface RelatedArticlesResponse {
    success: boolean;
    message?: string;
    data?: {
        items: RelatedArticle[];
        totalCount: number;
    };
    pl__related_articles?: Array<RelatedArticle>;
}

export interface RelatedArticlesResult {
    items: RelatedArticle[];
    totalCount: number;
}

export interface ArticleRevision {
    id: string;
    kb_id: string;
    title?: string;
    body?: string;
    body_rendered?: string;
    state?: string;
    created?: string;
    updated?: string;
    created_by?: string;
    revision_number?: number;
    isDraft?: boolean;
    isPublished?: boolean;
    isArchived?: boolean;
    isActive?: boolean;
    status?: number;
    kb_active_draft_id?: string;
}

export interface ArticleRevisionsResponse {
    success: boolean;
    message?: string;
    data?: {
        items: ArticleRevision[];
        totalCount: number;
    };
    kb_revisions?: {
        results: ArticleRevision[];
        totalCount: number;
    };
}

export interface RevisionResponse {
    success: boolean;
    message?: string;
    data?: any;
    kb?: any;
    items?: any[];
    kb_ids?: string[];
}

export interface KnowledgeListingResult {
    items: KnowledgeItem[];
    totalCount: number;
}

export interface FileUploadResponse {
    success: boolean;
    message?: string;
    data?: {
        id: string;
        name: string;
        type: string;
        size: number;
        url?: string;
        created?: string;
        updated?: string;
        [key: string]: any;
    };
    [key: string]: any;
}

export interface ArticleRevisionsResult {
    items: ArticleRevision[];
    totalCount: number;
}

export interface KnowledgeLibrary {
    id: string;
    name: string;
    description?: string;
    partner_id?: string;
    short_name?: string;
    val?: string;
    lbl?: string;
    sub_title?: string;
    owner_partner_id?: string;
    url?: string;
}

export interface LibrariesResponse {
    success: boolean;
    message?: string;
    data?: {
        results: KnowledgeLibrary[];
        totalCount: number;
    };
}

export interface KnowledgeLabel {
    id: string;
    title: string;
    parent_id?: string;
    root_kb_id?: string;
    short_name?: string;
    sub_title?: string;
    status?: number;
    owner_partner_id?: string;
    leaf?: boolean;
    parentId?: string | null;
    updated?: string;
    created?: string;
    _merge?: string;
    merge_ids?: string;
    url?: string;
}

export interface KnowledgeEvent {
    id: string;
    kb_id: string;
    user_id?: string;
    type?: string;
    status?: string;
    created?: string;
    updated?: string;
    user_name?: string;
    description?: string;
    revision_id?: string;
    icon?: string;
    color?: string;
    c__d_type?: string;
    c__users_full_name?: string;
    diff_log?: string;
}

export interface EventsResponse {
    success: boolean;
    message?: string;
    data?: {
        items: KnowledgeEvent[];
        totalCount: number;
    };
    kb_log?: {
        results: KnowledgeEvent[];
        totalCount: number;
    };
}

export interface KnowledgeEventsResult {
    items: KnowledgeEvent[];
    totalCount: number;
}

export interface LabelsResponse {
    success: boolean;
    message?: string;
    data?: any;
    kb_labels?: KnowledgeLabel[];
    labels?: KnowledgeLabel[];
    kb_label_ids?: string[];
}

export interface TeamAccess {
    id?: string;
    name?: string;
    description?: string;
    val: string;
    lbl: string;
}

export interface TeamsResponse {
    success: boolean;
    message?: string;
    data?: {
        results: TeamAccess[];
        totalCount: number;
    };
    pl__teams?: TeamAccess[];
    pl__partners_teams_link?: TeamAccess[];
    current_server_time?: string;
}

export interface KnowledgeBaseArticle {
    val: string;
    id: string;
    lbl: string;
    sub_title?: string | null;
    short_name?: string;
    root_parent_id?: string;
    owner_partner_id?: string;
    partner_ids?: string[];
    internal_team_ids?: string[];
    visibility?: number;
    c__d_visibility?: string;
    c__d_status?: string;
    keywords?: string | null;
    updated?: string;
    url?: string;
}

export interface KnowledgeBasesResponse {
    success: boolean;
    message?: string;
    pl__kbs?: KnowledgeBaseArticle[];
}

export interface KBTreeItem {
    id: string | number;
    lbl: string;
    parent_id?: string | number;
    leaf?: boolean;
}

export interface KBTreeParams {
    node?: string;
    query?: string;
    filters?: Array<{ property: string; value: any }>;
}
