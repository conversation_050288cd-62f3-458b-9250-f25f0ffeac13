import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';
import type { Product, ProductListResponse } from '@/types/partner';

/**
 * Composable that provides access to Partner API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after <PERSON><PERSON> is ready.
 */
export function usePartnerAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchProducts(): Promise<ProductListResponse> {
            try {
                const response = await httpClient.get('admin/v4/partners/', {
                    sAction: 'listingTemplate',
                    page: '1',
                    start: '0',
                    limit: '1000'
                });
                return response as ProductListResponse;
            } catch (error) {
                console.error('❌ PartnerAPI: Error fetching products:', error);
                throw error;
            }
        },

        async fetchPartnerTeams(ecosystem: boolean = false): Promise<any> {
            try {
                const response = await httpClient.get('admin/v4/core/', {
                    sAction: 'metaPartnersTeams'
                });
                return response;
            } catch (error) {
                console.error('❌ PartnerAPI: Error fetching partner teams:', error);
                throw error;
            }
        }
    };
} 