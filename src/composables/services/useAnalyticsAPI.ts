import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface AnalyticsLoginResponse {
    success: boolean;
    jwt: string;
    dashboard_uri: string;
    current_server_time: string;
}

/**
 * Composable that provides access to Analytics API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */

export function useAnalyticsAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchGoodDataLogin(): Promise<AnalyticsLoginResponse> {
            try {
                const response = await httpClient.post('admin/v4/core/?sAction=gooddatacloudlogin');
                return response as AnalyticsLoginResponse;
            } catch (error) {
                console.error('❌ AnalyticsAPI: Error fetching GoodData login:', error);
                throw error;
            }
        }
    };
} 