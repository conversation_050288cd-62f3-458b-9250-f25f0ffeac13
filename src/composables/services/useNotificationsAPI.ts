// Get the notifications service URL from environment variable, fallback to local proxy path
const NOTIFICATIONS_SERVICE_URL = import.meta.env.VITE_NOTIFICATIONS_SERVICE_URL || '/service-notifications-stage'

// Types
export interface NotificationTemplate {
  id: string
  name: string
  description?: string
  type: 'email' | 'sms' | 'push' | 'in-app'
  content: string
  metadata?: Record<string, unknown>
}

export interface NotificationRecipient {
  id: string
  type: 'user' | 'team' | 'email' | 'phone'
  value: string
}

export interface NotificationRequest {
  templateId?: string
  type: 'email' | 'sms' | 'push' | 'in-app'
  recipients: NotificationRecipient[]
  subject?: string
  content?: string
  metadata?: Record<string, unknown>
  priority?: 'low' | 'normal' | 'high'
}

export interface Notification {
  id: string
  templateId?: string
  type: 'email' | 'sms' | 'push' | 'in-app'
  recipients: NotificationRecipient[]
  subject?: string
  content: string
  metadata?: Record<string, unknown>
  status: 'pending' | 'sent' | 'failed'
  createdAt: string
  updatedAt: string
  sentAt?: string
  error?: string
}

// API Functions
export async function sendNotification(notification: NotificationRequest): Promise<Notification> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/notifications`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(notification),
  })
  if (!response.ok) throw new Error('Failed to send notification')
  return await response.json()
}

export async function getNotificationStatus(notificationId: string): Promise<Notification> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/notifications/${encodeURIComponent(notificationId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'GET',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to get notification status')
  return await response.json()
}

export async function getNotificationTemplates(): Promise<NotificationTemplate[]> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/templates`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'GET',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to get notification templates')
  return await response.json()
}

export async function getNotificationTemplate(templateId: string): Promise<NotificationTemplate> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/templates/${encodeURIComponent(templateId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'GET',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to get notification template')
  return await response.json()
}

export async function createNotificationTemplate(template: Omit<NotificationTemplate, 'id'>): Promise<NotificationTemplate> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/templates`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(template),
  })
  if (!response.ok) throw new Error('Failed to create notification template')
  return await response.json()
}

export async function updateNotificationTemplate(
  templateId: string, 
  updates: Partial<Omit<NotificationTemplate, 'id'>>
): Promise<NotificationTemplate> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/templates/${encodeURIComponent(templateId)}`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'PUT',
    credentials: 'omit',
    body: JSON.stringify(updates),
  })
  if (!response.ok) throw new Error('Failed to update notification template')
  return await response.json()
}

export async function deleteNotificationTemplate(templateId: string): Promise<void> {
  const response = await fetch(`${NOTIFICATIONS_SERVICE_URL}/templates/${encodeURIComponent(templateId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.notifications/ui',
    },
    method: 'DELETE',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to delete notification template')
} 