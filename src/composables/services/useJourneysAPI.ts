// Get the journeys service URL from environment variable, fallback to local proxy path
const JOURNEYS_SERVICE_URL = import.meta.env.VITE_JOURNEYS_SERVICE_URL || '/service-journeys-stage'

// Types
export interface JourneyStage {
  id: string
  title: string
  description?: string
  order: number
  status: string
}

export interface Journey {
  id: string
  title: string
  description?: string
  stages: JourneyStage[]
  createdAt: string
  updatedAt: string
  status: string
}

export interface FetchJourneysParams {
  orgId?: string
  teamId?: string
  userId?: string
  status?: string
  search?: string
  page?: number
  limit?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface CreateJourneyRequest {
  title: string
  description?: string
  stages?: Omit<JourneyStage, 'id'>[]
}

export interface UpdateJourneyRequest {
  title?: string
  description?: string
  stages?: Omit<JourneyStage, 'id'>[]
  status?: string
}

// API Functions
export async function fetchJourneys(params?: FetchJourneysParams): Promise<Journey[]> {
  const queryParams = new URLSearchParams()
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })
  }

  const queryString = queryParams.toString()
  const url = `${JOURNEYS_SERVICE_URL}/v2/journeys${queryString ? `?${queryString}` : ''}`

  const response = await fetch(url, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'GET',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to fetch journeys')
  return await response.json()
}

export async function fetchJourneyById(journeyId: string): Promise<Journey> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/journeys/${encodeURIComponent(journeyId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'GET',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to fetch journey')
  return await response.json()
}

export async function createJourney(journey: CreateJourneyRequest): Promise<Journey> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/journeys`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(journey),
  })
  if (!response.ok) throw new Error('Failed to create journey')
  return await response.json()
}

export async function updateJourney(journeyId: string, updates: UpdateJourneyRequest): Promise<Journey> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/journeys/${encodeURIComponent(journeyId)}`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'PUT',
    credentials: 'omit',
    body: JSON.stringify(updates),
  })
  if (!response.ok) throw new Error('Failed to update journey')
  return await response.json()
}

export async function deleteJourney(journeyId: string): Promise<void> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/journeys/${encodeURIComponent(journeyId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'DELETE',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to delete journey')
}

// Case Journey Management
export async function assignJourneyToCase(caseId: string, journeyId: string): Promise<void> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/cases/${encodeURIComponent(caseId)}/journeys`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify({ journeyId }),
  })
  if (!response.ok) throw new Error('Failed to assign journey to case')
}

export async function updateCaseJourneyStage(caseId: string, journeyId: string, stageId: string): Promise<void> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/cases/${encodeURIComponent(caseId)}/journeys/${encodeURIComponent(journeyId)}/stages/${encodeURIComponent(stageId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'PUT',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to update case journey stage')
}

export async function removeCaseJourney(caseId: string, journeyId: string): Promise<void> {
  const response = await fetch(`${JOURNEYS_SERVICE_URL}/v2/cases/${encodeURIComponent(caseId)}/journeys/${encodeURIComponent(journeyId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.journeys/ui',
    },
    method: 'DELETE',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to remove journey from case')
} 