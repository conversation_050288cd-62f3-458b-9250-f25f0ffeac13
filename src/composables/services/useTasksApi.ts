import type { Task } from '@/types/task'

export interface CreateTaskResponse {
  success: boolean
  createdTask?: Task
  task?: Task
}

// Get the tasks service URL from environment variable, fallback to local proxy path
const TASKS_SERVICE_URL = import.meta.env.VITE_TASKS_SERVICE_URL || '/service-tasks-stage'

export async function fetchCaseTasks(caseId: string): Promise<Task[]> {
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks?caseId=${encodeURIComponent(caseId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:tasks',
    },
    method: 'GET',
    credentials: 'omit',
  });
  if (!response.ok) throw new Error('Failed to fetch case tasks');
  return await response.json();
}

export async function fetchUserTasks(userId: string) {
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks?userId=${encodeURIComponent(userId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:mywork',
    },
    method: 'GET',
    credentials: 'omit',
  });
  if (!response.ok) throw new Error('Failed to fetch user tasks');
  return await response.json();
}

export async function fetchTeamTasks(teamId: string) {
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks?teamId=${encodeURIComponent(teamId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:mywork',
    },
    method: 'GET',
    credentials: 'omit',
  });
  if (!response.ok) throw new Error('Failed to fetch team tasks');
  return await response.json();
}

export async function fetchTaskById(taskId: string): Promise<Task> {
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks/${encodeURIComponent(taskId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:mywork',
    },
    method: 'GET',
    credentials: 'omit',
  })
  if (!response.ok) throw new Error('Failed to fetch task')
  return await response.json()
}

export async function updateTaskStatus(task: Task, status: string): Promise<Task> {
  // Only send the fields required by the backend
  const { id, name, description, dueDate, deleted, links } = task
  const updatedTask = {
    id,
    name,
    description,
    dueDate: dueDate && dueDate.trim() !== '' ? dueDate : null,
    deleted,
    links,
    status,
  }
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks/${encodeURIComponent(task.id)}`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:mywork',
    },
    method: 'PUT',
    credentials: 'omit',
    body: JSON.stringify(updatedTask),
  })
  if (!response.ok) throw new Error('Failed to update task status')
  const responseJson = await response.json()
  return responseJson.createdTask || responseJson.task || responseJson
}

export async function createTask(taskData: Partial<Task>): Promise<Task> {
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks`, {
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:tasks',
    },
    method: 'POST',
    credentials: 'omit',
    body: JSON.stringify(taskData),
  })
  
  if (!response.ok) throw new Error('Failed to create task')
  
  const result: CreateTaskResponse = await response.json()
  
  if (!result.success) {
    throw new Error('Failed to create task')
  }
  
  return result.createdTask || result.task!
}

export async function fetchAllTasks(orgId: string) {
  const response = await fetch(`${TASKS_SERVICE_URL}/tasks?orgId=${encodeURIComponent(orgId)}`, {
    headers: {
      'accept': 'application/json',
      'x-ovationcxm-source': 'com.ovationcxm.tasks/ui:mywork',
    },
    method: 'GET',
    credentials: 'omit',
  });
  if (!response.ok) throw new Error('Failed to fetch all tasks');
  return await response.json();
} 