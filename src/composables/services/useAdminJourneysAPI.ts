import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface JourneyStage {
  id: string;
  title: string;
  stage: string;
  position: number;
}

export interface Journey {
  id: string;
  partners_id: string;
  creator_user_id: string | null;
  card_id: string | null;
  title: string;
  description: string;
  data: Record<string, any>;
  status: number;
  created: string;
  updated: string;
  metadata: any;
  c__creator_user_id: string | null;
  c__card_id: string | null;
  _highlighted: boolean;
  _highlightmap: Record<string, any>;
  _has_detail: boolean;
  stages: JourneyStage[];
  c__d_object: string;
  _canWrite: boolean;
  _uiAccess: {
    edit: boolean;
    delete: boolean;
    clone: boolean;
    merge: boolean;
  };
  origin: string;
}

export interface JourneysListingResponse {
  success: boolean;
  partners_journeys: {
    totalCount: number;
    results: Journey[];
  };
  current_server_time: string;
}

/**
 * Composable that provides access to Admin Journeys API functions.
 */
export function useAdminJourneysAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async fetchJourneys(partnersId: string = 'H3F'): Promise<Journey[]> {
      console.log('AdminJourneysAPI: Fetching journeys for partner:', partnersId);
      
      try {
        const data = await httpClient.get<JourneysListingResponse>('admin/v4/journeys/', {
          'sAction': 'journeysListing',
          'limit': '100',
          'page': '1',
          'start': '0',
          'sort': JSON.stringify([{ property: 'title', direction: 'ASC' }]),
          'filter': JSON.stringify([{ property: 'partners_id', value: partnersId }])
        });

        console.log('AdminJourneysAPI: Fetch journeys response:', data);
        
        if (data.success && data.partners_journeys?.results) {
          return data.partners_journeys.results;
        }
        
        return [];
      } catch (error) {
        console.error('AdminJourneysAPI: Error fetching journeys:', error);
        throw error;
      }
    }
  };
} 