import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface CreateCaseSummaryParams {
  case_id: string;
}

export interface CreateCaseSummaryResponse {
  success: boolean;
  response: {
    message: string;
    summary_id: string;
  };
  current_server_time: string;
}

/**
 * Composable that provides access to ML API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pi<PERSON> is ready.
 */
export function useMLAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async createCaseSummary(params: CreateCaseSummaryParams): Promise<CreateCaseSummaryResponse> {
      console.log('MLAPI: Creating case summary for case:', params.case_id);
      
      const payload = new URLSearchParams();
      payload.append('case_id', params.case_id);
      payload.append('stringify', 'false');
      
      const data = await httpClient.post<CreateCaseSummaryResponse>(
        'admin/v4/ml/',
        payload,
        { sAction: 'proxyCaseSummary' },
        { 'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      );

      console.log('MLAPI: Case summary response:', data);
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.response?.message || 'Failed to create case summary');
    }
  };
} 