import { getHttpClient } from '@/services/httpClientProvider'
import { useAuthStore } from '@/stores/auth'

export interface User {
  id: string
  object: string
  object_id: string
  role_id: string
  email: string
  first_name: string
  last_name: string
  nickname: string
  role: number
  status: number
  time_zone: string
  sms_number: string
  phone_number: string
  phone_ext: string
  avatar: string
  notes: string
  login_count: number
  updated: string
  created: string
  last_login: string
  first_login: boolean
  perms: string[]
}

export interface UsersListResponse {
  success: boolean
  users: {
    totalCount: number
    results: User[]
  }
}

export interface FetchUserListParams {
  page?: number
  start?: number
  limit?: number
  sort?: Array<{ property: string; direction: string }>
  teamId?: string // Optional team filter
  organizationId?: string // Optional organization filter
}

export function useUsersAPI() {
  const authStore = useAuthStore()
  const httpClient = getHttpClient(authStore)

  async function fetchUserList(params: FetchUserListParams = {}): Promise<UsersListResponse> {
    const {
      page = 1,
      start = 0,
      limit = 25,
      sort = [{ property: 'email', direction: 'ASC' }],
      teamId,
      organizationId
    } = params

    // Build filter array
    const filters = [
      { property: 'id', value: '_no_filter_' },
      { property: 'object', value: 'partners' },
      { property: 'role_id', value: '_no_filter_' }
    ]

    // Add organization filter if provided
    if (organizationId) {
      filters.push({ property: 'object_id', value: organizationId })
    }

    // Add team filter if provided
    if (teamId) {
      filters.push({ property: 'project_partner_users', value: teamId })
    }

    const response = await httpClient.get<UsersListResponse>('admin/v4/users/', {
      sAction: 'listing',
      page: page.toString(),
      start: start.toString(),
      limit: limit.toString(),
      sort: JSON.stringify(sort),
      filter: JSON.stringify(filters)
    })
    
    if (!response.success) {
      throw new Error('Failed to fetch users')
    }

    return response
  }

  return {
    fetchUserList
  }
} 