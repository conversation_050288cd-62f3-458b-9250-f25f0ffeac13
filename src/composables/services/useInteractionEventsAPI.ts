import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface InteractionEvent {
    category: { id: string; label: string };
    body: { type: string; data: string };
    created: string;
    event_data?: any;
}

export interface FetchInteractionEventsParams {
    page?: number;
    limit?: number;
    start?: number;
    issueId?: string;
    sort?: Array<{
        property: string;
        direction: 'ASC' | 'DESC';
    }>;
    filter?: Array<{
        property: string;
        value: any;
        operator?: string;
    }>;
}

export interface InteractionEventsResponse {
    success: boolean;
    message?: string;
    log_feed?: {
        results: InteractionEvent[];
    };
}

/**
 * Composable that provides access to Interaction Events API functions.
 * This safely initializes the httpClient with authStore only when called
 * inside components or other composables after Pinia is ready.
 */
export function useInteractionEventsAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchInteractionEvents(params: FetchInteractionEventsParams): Promise<InteractionEvent[]> {
            console.log('InteractionEventsAPI: Fetching events', params);

            try {
                // Build the filter array, always including the issue ID filter if provided
                let filters = params.filter || [];
                if (params.issueId) {
                    // Add filters for source_object_id and source_object
                    filters = [
                        ...filters,
                        { property: 'source_object_id', value: params.issueId },
                        { property: 'source_object', value: 'issues' },
                    ];
                }

                const data = await httpClient.get<InteractionEventsResponse>('admin/v4/interaction_events/', {
                    sAction: 'listing',
                    page: params.page?.toString() || '1',
                    start: params.start?.toString() || '0',
                    limit: params.limit?.toString() || '25',
                    sort: params.sort
                        ? JSON.stringify(
                              params.sort.map((s) => ({
                                  property: s.property,
                                  direction: s.direction,
                              }))
                          )
                        : '[{"property":"created","direction":"DESC"}]',
                    filter: JSON.stringify(filters),
                });

                console.log('InteractionEventsAPI: Events response:', data);

                if (data.success && data.log_feed?.results) {
                    return data.log_feed.results;
                }

                throw new Error(data.message || 'Failed to fetch interaction events');
            } catch (error) {
                console.error('❌ InteractionEventsAPI: Error fetching events:', error);
                throw error;
            }
        },
    };
}
