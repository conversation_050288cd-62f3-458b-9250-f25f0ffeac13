<script setup lang="ts">
import { RouterView } from 'vue-router';
import LoadingLogo from './components/LoadingLogo.vue';
import LanguageSwitcher from './components/LanguageSwitcher.vue';
import LocalizedMessage from './components/LocalizedMessage.vue';
import MockApiConfigurator from './components/development/MockApiConfigurator.vue';
import ApiResponseViewer from './components/development/ApiResponseViewer.vue';
import { ref, onMounted } from 'vue';
import { useUserStore } from './stores/user';
import { useAuthStore } from './stores/auth';
import { getHttpClient } from './services/httpClientProvider';
import { fetchClientInstanceId } from '@/utils/clientInstance';
import PreviewBanner from './components/PreviewBanner.vue';
import Toast from 'primevue/toast';
import Message from 'primevue/message';
import { useOnlineStatus } from './composables/useOnlineStatus';
import { mfeLoader } from './services/MFELoaderService';
import { useRouter } from 'vue-router';

const userStore = useUserStore();
const authStore = useAuthStore();
const apiViewerRef = ref();
const loading = ref(true);
const { isOnline } = useOnlineStatus();
const router = useRouter();

onMounted(async () => { 
    try {
        // 1. Initialize the client instance ID
        console.debug('Initializing client instance ID...');
        const clientId = fetchClientInstanceId();
        
        // 2. Initialize the auth store
        console.debug('Initializing auth store...');
        await authStore.initialize();
        
        // 3. Get HTTP client and inject auth store
        console.debug('Initializing HTTP client with auth store...');
        const httpClient = getHttpClient(authStore);
        await httpClient.initialize();
        
        // 4. Initialize the user store
        console.debug('Initializing user store...');
        await userStore.initialize();
        
        console.debug('App initialization complete. Auth status:', authStore.isAuthenticated);
    } catch (error) {
        console.error('Failed to initialize application:', error);
    } finally {
        loading.value = false;
    }
});
</script>

<template>
    <Toast />
    <PreviewBanner />
    <div class="app">
        <Message v-if="!isOnline" severity="warn" :closable="false" class="offline-banner">
            <i class="pi pi-wifi" style="margin-right: 0.5rem"></i>
            You are currently offline. Some functionality may not work as expected.
        </Message>
        <div v-if="loading" class="app-loading">
            <LoadingLogo />
        </div>
        <template v-else>
            <!-- <LocalizedMessage /> -->
            <RouterView />
        </template>

        <!-- Development tools -->
        <ApiResponseViewer ref="apiViewerRef" :hideButton="true" />
        <MockApiConfigurator :apiViewerRef="apiViewerRef" />
    </div>
</template>

<style scoped>
.app {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.app-header {
    display: flex;
    justify-content: flex-end;
    padding: 0.5rem 1rem;
    background-color: var(--surface-100, #f8f9fa);
    border-bottom: 1px solid var(--surface-200, #e9ecef);
}

.app-loading {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 100px;
}

.initializing {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--surface-50);
}

.loading-text {
    margin-top: 1rem;
    color: var(--surface-600);
    font-size: 0.875rem;
}

.logo {
    margin: 2rem auto;
}

nav {
    font-size: 14px;
    margin-top: 2rem;
}

nav a.router-link-exact-active {
    color: var(--color-text);
}

nav a.router-link-exact-active:hover {
    background-color: transparent;
}

nav a {
    display: inline-block;
    margin-right: 1rem;
    border-left: 1px solid var(--color-border);
}

.offline-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-radius: 0;
    text-align: center;
    padding: 0.75rem;
    background-color: var(--yellow-100);
    border-bottom: 1px solid var(--yellow-200);
    color: var(--yellow-900);
}
</style>
