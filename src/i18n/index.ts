import { createI18n } from 'vue-i18n'
import en from '../locales/en.json'
import es from '../locales/es.json'
import de from '../locales/de.json'

// Determine browser language or use stored preference
const getBrowserLocale = (): string => {
  // Use navigator language if available
  const navigatorLocale = navigator.language?.split('-')[0] || 'en'
  
  // Check if we support the browser locale, default to English if not supported
  return ['en', 'es', 'de'].includes(navigatorLocale) ? navigatorLocale : 'en'
}

// Try to get language from localStorage
const getStoredLocale = (): string | null => {
  return localStorage.getItem('locale')
}

// Determine the locale to use - first check localStorage, then browser setting, fallback to English
const locale = getStoredLocale() || getBrowserLocale() || 'en'

// Create i18n instance
export const i18n = createI18n({
  legacy: false, // you must set `false`, to use Composition API
  locale: locale,
  fallbackLocale: 'en',
  messages: {
    en,
    es,
    de
  },
  globalInjection: true, // inject $t and other helpers into Vue components
  silentTranslationWarn: process.env.NODE_ENV === 'production'
})

// Helper to set locale and save to localStorage for persistence
export const setLocale = (localeCode: 'en' | 'es' | 'de'): void => {
  i18n.global.locale.value = localeCode
  localStorage.setItem('locale', localeCode)
  document.querySelector('html')?.setAttribute('lang', localeCode)
}

// Set initial HTML lang attribute
document.querySelector('html')?.setAttribute('lang', locale) 