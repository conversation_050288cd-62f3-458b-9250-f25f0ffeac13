# Development Guidelines for CXM-UI

This document outlines the development guidelines and best practices for the CXM-UI project. Following these guidelines ensures consistency, maintainability, and high quality across the codebase.

## Vue 3 Best Practices

### Component Structure

1. **Use Single-File Components (SFC)** for all Vue components.
2. **Follow the multi-word component naming convention** to avoid conflicts with existing and future HTML elements.
3. **Use PascalCase for component names** in templates when using SFCs.
4. **Use self-closing components** when they don't have content.
5. **Order component attributes** consistently, with one attribute per line for components with multiple attributes.

### Component Organization

1. **Base components** (presentational, pure components) should have a prefix like `Base`, `App`, or `V`.
2. **Component names should be ordered from general to specific** (e.g., `SearchButtonClear` instead of `ClearSearchButton`).
3. **Keep components focused on a single responsibility** and split them when they grow too large.
4. **Use detailed prop definitions** with types and validation where appropriate.

### Styling

1. **Use component-scoped styling** with the `scoped` attribute in SFCs.
2. **Follow a consistent class naming convention** (e.g., BEM) for component styling.
3. **Use Tailwind CSS utility classes** for styling components.
4. **Avoid using global styles** except in the top-level `App` component and layout components.

### TypeScript Usage

1. **Define proper interfaces and types** for all component props, emits, and data.
2. **Use TypeScript's type system** to catch errors at compile time.
3. **Avoid using `any` type** unless absolutely necessary.
4. **Define return types for functions** to improve code readability and maintainability.

## Code Style and Formatting

1. **Follow the ESLint configuration** provided in the project.
2. **Use Prettier for code formatting** with the project's configuration.
3. **Maintain consistent indentation** (4 spaces as per the project's Prettier config).
4. **Limit line length** to 120 characters (as per the project's Prettier config).
5. **Use single quotes** for strings (as per the project's Prettier config).

## State Management

1. **Use Pinia for global state management**.
2. **Keep component state local** when it doesn't need to be shared.
3. **Organize stores by feature or domain**.
4. **Use computed properties** for derived state.
5. **Implement proper error handling** in actions.

## Internationalization

1. **Use vue-i18n for all user-facing text**.
2. **Organize translation keys** in a logical structure.
3. **Use nested keys** to organize translations (e.g., `common.welcome` instead of just `welcome`).
4. **Keep translation keys lowercase** and use underscores for spaces.
5. **Add translations for all supported languages** when adding new features.
6. **Use variables for dynamic content**: `$t('greeting', { name: userName })`.

## Testing

1. **Write unit tests for all components** using Vitest.
2. **Write E2E tests for critical user flows** using Playwright.
3. **Use component testing** for complex components.
4. **Mock external dependencies** in unit tests.
5. **Follow the AAA pattern** (Arrange, Act, Assert) in test cases.

## Performance Optimization

1. **Use lazy loading for routes** to reduce initial load time.
2. **Optimize component rendering** by using `v-once` or `v-memo` where appropriate.
3. **Use computed properties with proper dependencies** to avoid unnecessary recalculations.
4. **Implement proper list rendering** with `key` attribute for `v-for` directives.
5. **Avoid using `v-if` with `v-for`** on the same element.

## Security

1. **Sanitize user inputs** before processing or displaying them.
2. **Use proper authentication and authorization** mechanisms.
3. **Implement CSRF protection** for API requests.
4. **Follow the principle of least privilege** when accessing resources.
5. **Regularly update dependencies** to patch security vulnerabilities.

## Documentation

1. **Document components** using Storybook.
2. **Add JSDoc comments** for functions and methods.
3. **Keep the documentation up-to-date** when making changes.
4. **Document complex logic** with inline comments.
5. **Update README and other documentation files** when adding new features or changing existing ones.

## Git Workflow

1. **Follow a consistent branch naming convention** (e.g., `feature/`, `bugfix/`, `hotfix/`).
2. **Write descriptive commit messages** that explain the why, not just the what.
3. **Keep commits focused on a single change