import { MSWAdapter as test, expect, setFixtureId } from '../fixtures/msw-adapter.js';

let articleId: string;

test.describe('simple MSW test', () => {
    test.beforeEach(async ({ page, worker, http }) => {
        // Add a specific handler for the exact URL pattern we need
        // await worker.use(
        //   http.get('*/admin/v4/kb/', () => {
        //     return new Response(JSON.stringify({
        //       kb: {
        //         results: [
        //           { id: 'article-1', title: 'Test Article', content: 'This is a test article' }
        //         ]
        //       }
        //     }), {
        //       headers: { 'Content-Type': 'application/json' }
        //     });
        //   })
        // );
        
        // Navigate to the application
        await page.goto('/');
        
        // Get the article ID
        articleId = await page.evaluate(async () => {
          try {
            const response = await fetch('/admin/v4/kb/?sAction=listing');
            const data = await response.json();
            console.log('API response:', data);
            return data.kb?.results?.[0]?.id || '';
          } catch (e) {
            console.error('Error:', e);
            return '';
          }
        });
        
        console.log(`Article ID for tests: ${articleId}`);
    });

    test('MSW debug - verify interception is working', async ({ page, worker, http }) => {
        // Add a simple handler
        await worker.use(
          http.get('*/test-endpoint', () => {
            return new Response(JSON.stringify({ success: true }));
          })
        );
        
        // Navigate to a page
        await page.goto('/');
        
        // Try a simple fetch
        const result = await page.evaluate(async () => {
          try {
            console.log('Starting test fetch');
            const response = await fetch('/test-endpoint');
            console.log('Fetch completed');
            return await response.json();
          } catch (e) {
            console.error('Fetch failed:', e);
            return { error: (e as Error).message };
          }
        });
        
        console.log('Fetch result:', result);
        
        // Assert on the result
        expect(result).toHaveProperty('success', true);
      });
      
});

