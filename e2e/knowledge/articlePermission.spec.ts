import { test, expect } from '@playwright/test';

test.describe('Knowledge Article Permissions', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the user permissions API response
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: true
            }
          }
        })
      });
    });

    // Mock the knowledge tree API response
    await page.route('/api/v1/knowledge/tree', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '1',
            text: 'Test Library',
            leaf: false,
            isLibrary: true,
            _uiAccess: {
              edit: true,
              delete: true,
              clone: true,
              merge: true
            }
          }
        ])
      });
    });

    // Mock the knowledge listing API response
    await page.route('/api/v1/knowledge/listing', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [],
          total: 0
        })
      });
    });

    // Navigate to knowledge page
    await page.goto('/knowledge');
  });

  test('should show create article button when user has permission', async ({ page }) => {
    // Mock permissions with add_article: true
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: true
            }
          }
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Verify create article button is visible
    const createArticleButton = page.getByRole('button', { name: 'New Article' });
    await expect(createArticleButton).toBeVisible();
  });

  test('should hide create article button when user lacks permission', async ({ page }) => {
    // Mock permissions with add_article: false
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: false
            }
          }
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Verify create article button is not visible
    const createArticleButton = page.getByRole('button', { name: 'New Article' });
    await expect(createArticleButton).not.toBeVisible();
  });

  test('should show create label button when user has permission', async ({ page }) => {
    // Mock permissions with add_article: true
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: true
            }
          }
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Verify create label button is visible
    const createLabelButton = page.getByRole('button', { name: 'New Label' });
    await expect(createLabelButton).toBeVisible();
  });

  test('should hide create label button when user lacks permission', async ({ page }) => {
    // Mock permissions with add_article: false
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: false
            }
          }
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Verify create label button is not visible
    const createLabelButton = page.getByRole('button', { name: 'New Label' });
    await expect(createLabelButton).not.toBeVisible();
  });

  test('should open create article modal when button is clicked', async ({ page }) => {
    // Mock permissions with add_article: true
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: true
            }
          }
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Click create article button
    const createArticleButton = page.getByRole('button', { name: 'New Article' });
    await createArticleButton.click();

    // Verify modal is visible
    const modal = page.getByRole('dialog');
    await expect(modal).toBeVisible();
  });

  test('should open create label modal when button is clicked', async ({ page }) => {
    // Mock permissions with add_article: true
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: true
            }
          }
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Click create label button
    const createLabelButton = page.getByRole('button', { name: 'New Label' });
    await createLabelButton.click();

    // Verify modal is visible
    const modal = page.getByRole('dialog');
    await expect(modal).toBeVisible();
  });

  test('should handle permission API error gracefully', async ({ page }) => {
    // Mock permissions API to return an error
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal Server Error'
        })
      });
    });

    // Reload page to get new permissions
    await page.reload();

    // Verify create article button is not visible (default to no permission on error)
    const createArticleButton = page.getByRole('button', { name: 'New Article' });
    await expect(createArticleButton).not.toBeVisible();

    // Verify create label button is not visible (default to no permission on error)
    const createLabelButton = page.getByRole('button', { name: 'New Label' });
    await expect(createLabelButton).not.toBeVisible();
  });

  test('should show create template button when in templates view', async ({ page }) => {
    // Mock permissions with add_article: true
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: true
            }
          }
        })
      });
    });

    // Mock the knowledge listing API response for templates
    await page.route('/api/v1/knowledge/listing', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [],
          total: 0,
          type: 1 // Template type
        })
      });
    });

    // Navigate to templates view
    await page.goto('/knowledge?type=templates');

    // Verify create template button is visible
    const createTemplateButton = page.getByRole('button', { name: 'New Template' });
    await expect(createTemplateButton).toBeVisible();
  });

  test('should hide create template button when user lacks permission', async ({ page }) => {
    // Mock permissions with add_article: false
    await page.route('/api/v1/user/permissions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          permissions: {
            knowledge: {
              add_article: false
            }
          }
        })
      });
    });

    // Mock the knowledge listing API response for templates
    await page.route('/api/v1/knowledge/listing', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: [],
          total: 0,
          type: 1 // Template type
        })
      });
    });

    // Navigate to templates view
    await page.goto('/knowledge?type=templates');

    // Verify create template button is not visible
    const createTemplateButton = page.getByRole('button', { name: 'New Template' });
    await expect(createTemplateButton).not.toBeVisible();
  });
});
