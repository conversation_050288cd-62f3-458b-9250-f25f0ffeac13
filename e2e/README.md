# End-to-End Testing

This directory contains the end-to-end tests for the application using Playwright.

## Structure

- `*.spec.ts` - Test files
- `pages/` - Page Object Models (POMs)
- `fixtures/` - Test fixtures and utilities

## Page Object Model (POM)

We use the Page Object Model pattern to organize our tests:

- Each page or major component has a corresponding class in the `pages/` directory
- Each POM encapsulates the page structure and provides methods to interact with it
- Test files use these POM classes to write more readable and maintainable tests

## Running Tests

```bash
# Run all tests
npm run test:e2e

# Run tests in a specific file
npm run test:e2e -- e2e/login.spec.ts

# Run tests in headed mode (with browser visible)
npm run test:e2e -- --headed

# Run tests in a specific browser
npm run test:e2e -- --project=firefox
```

## Debugging Tests

```bash
# Run tests in debug mode
npm run test:e2e -- --debug

# Generate and open HTML report after tests
npm run test:e2e -- --reporter=html && npx playwright show-report
```

## Writing New Tests

1. If you're testing a new page, create a new Page Object Model in `pages/`
2. Create a new test file `your-feature.spec.ts`
3. Use the POM to interact with the page
4. Write assertions to verify expected behavior

Example:

```typescript
import { test, expect } from '@playwright/test';
import { YourPage } from './pages/YourPage';

test.describe('Your Feature', () => {
    let yourPage: YourPage;

    test.beforeEach(async ({ page }) => {
        yourPage = new YourPage(page);
        await yourPage.navigate();
    });

    test('should do something', async () => {
        await yourPage.doSomething();
        await expect(yourPage.result).toHaveText('Expected result');
    });
});
```

## Authentication

For tests that require authentication, use the authentication fixture:

```typescript
import { authenticatedTest } from './fixtures/auth.fixture';

authenticatedTest('test requiring auth', async ({ page }) => {
    // Test with a logged-in user
    await page.goto('/protected-page');
    // ... test assertions
});
```
