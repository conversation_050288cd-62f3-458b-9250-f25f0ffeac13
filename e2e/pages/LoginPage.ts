import { Page, expect, Locator } from '@playwright/test';

/**
 * Page Object Model for Login Page
 */
export class LoginPage {
    readonly page: Page;

    // Main sections
    readonly loginPage: Locator;
    readonly logoContainer: Locator;
    readonly logoImage: Locator;
    readonly bravoLoginScreen: Locator;
    readonly languageSelector: Locator;
    readonly loginErrorMessage: Locator;

    // Form elements
    readonly emailInput: Locator;
    readonly passwordInput: Locator;
    readonly rememberMeCheckbox: Locator;
    readonly submitButton: Locator;
    readonly errorMessage: Locator;

    // Language options
    readonly englishOption: Locator;
    readonly spanishOption: Locator;
    readonly germanOption: Locator;

    /**
     * Initializes a new instance of the LoginPage
     * @param page Playwright page instance
     */
    constructor(page: Page) {
        this.page = page;

        // Main sections
        this.loginPage = page.locator('[data-testid="login-page"]');
        this.logoContainer = page.locator('[data-testid="logo-container"]');
        this.logoImage = page.locator('[data-testid="logo-image"]');
        this.bravoLoginScreen = page.locator('[data-testid="bravo-login-screen"]');
        this.languageSelector = page.locator('[data-testid="language-selector"]');
 
        // Form elements - assuming BravoLoginScreen has these test IDs
        this.emailInput = page.locator('input[type="email"]');
        this.passwordInput = page.locator('input[type="password"]');
        this.rememberMeCheckbox = page.locator('input[type="checkbox"]');
        this.submitButton = page.locator('button[type="submit"]');
        this.errorMessage = page.locator('[data-testid="login-error-message"]');

        // Language options
        this.englishOption = page.locator('[data-testid="language-option-en"]');
        this.spanishOption = page.locator('[data-testid="language-option-es"]');
        this.germanOption = page.locator('[data-testid="language-option-de"]');

        // In the constructor:
        this.loginErrorMessage = page.locator('[data-testid="login-error-message"]');
    }

    /**
     * Navigates to the login page
     */
    async navigateToLogin() {
        await this.page.goto('/login');
        await this.waitForLoginPageLoad();
    }

    /**
     * Waits for the login page to load
     */
    async waitForLoginPageLoad() {
        await this.loginPage.waitFor();
        await this.bravoLoginScreen.waitFor();
    }

    /**
     * Performs login with the provided credentials
     * @param email User's email
     * @param password User's password
     * @param rememberMe Whether to check the "Remember Me" checkbox
     */
    async login(email: string, password: string, rememberMe: boolean = false) {
        await this.emailInput.fill(email);
        await this.passwordInput.fill(password);

        // Set remember me checkbox to desired state
        const isChecked = await this.rememberMeCheckbox.isChecked();
        if (isChecked !== rememberMe) {
            await this.rememberMeCheckbox.click();
        }

        // Make sure button is visible and enabled
        await this.submitButton.waitFor({ state: 'visible' });
        await expect(this.submitButton).toBeEnabled();
        
        // Wait for navigation to complete
        await this.page.waitForLoadState('networkidle');

        // Click and wait for response
        await Promise.all([
            this.submitButton.click(),
        ]);
    }

    /**
     * Changes the language of the login page
     * @param language The language code to switch to ('en', 'es', or 'de')
     */
    async changeLanguage(language: 'en' | 'es' | 'de') {
        switch (language) {
            case 'en':
                await this.englishOption.click();
                break;
            case 'es':
                await this.spanishOption.click();
                break;
            case 'de':
                await this.germanOption.click();
                break;
            default:
                throw new Error(`Unsupported language code: ${language}`);
        }
    }

    /**
     * Gets the currently selected language
     * @returns The code of the currently selected language
     */
    async getSelectedLanguage(): Promise<'en' | 'es' | 'de'> {
        if (await this.englishOption.getAttribute('class').then((c) => c?.includes('active'))) {
            return 'en';
        } else if (await this.spanishOption.getAttribute('class').then((c) => c?.includes('active'))) {
            return 'es';
        } else if (await this.germanOption.getAttribute('class').then((c) => c?.includes('active'))) {
            return 'de';
        }
        throw new Error('No language appears to be selected');
    }

    /**
     * Checks if an error message is displayed
     * @returns True if an error message is visible, false otherwise
     */
    async hasError(): Promise<boolean> {
        return this.errorMessage.isVisible();
    }

    /**
     * Gets the text of the error message
     * @returns The error message text
     */
    async getErrorMessage(): Promise<string> {
        return (await this.errorMessage.textContent()) || '';
    }
}
