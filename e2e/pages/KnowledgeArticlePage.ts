import { Page, expect, Locator } from '@playwright/test';

/**
 * Page Object Model for Knowledge Article View
 */
export class KnowledgeArticlePage {
    readonly page: Page;

    // Main sections
    readonly articleView: Locator;
    readonly articleMain: Locator;
    readonly articleSidebar: Locator;
    readonly topNav: Locator;
    readonly articleContent: Locator;

    // Top navigation elements
    readonly backButton: Locator;
    readonly pageTitle: Locator;
    readonly revisionSelector: Locator;
    readonly revisionDropdown: Locator;
    readonly selectedRevision: Locator;
    readonly editButtons: Locator;
    readonly articleMenuButton: Locator;
    readonly articleStatusButton: Locator;
    readonly editArticleButton: Locator;

    // Article content elements
    readonly articleTitle: Locator;
    readonly articleSubtitle: Locator;
    readonly articleMeta: Locator;
    readonly articleUpdated: Locator;
    readonly articleVisibility: Locator;

    // Edit mode elements
    readonly titleInput: Locator;
    readonly subtitleInput: Locator;
    readonly saveEditButton: Locator;
    readonly cancelEditButton: Locator;
    readonly articleEditor: Locator;

    // Sidebar elements
    readonly sidebarTitle: Locator;
    readonly minimizeSidebarButton: Locator;
    readonly expandSidebarButton: Locator;
    readonly detailsTab: Locator;
    readonly eventsTab: Locator;
    readonly detailsPanel: Locator;
    readonly eventsPanel: Locator;

    // Loading and error states
    readonly loadingSpinner: Locator;
    readonly errorMessage: Locator;
    readonly notFoundMessage: Locator;

    /**
     * Initializes a new instance of the KnowledgeArticlePage
     * @param page Playwright page instance
     */
    constructor(page: Page) {
        this.page = page;

        // Main sections
        this.articleView = page.locator('[data-testid="article-view"]');
        this.articleMain = page.locator('[data-testid="article-main"]');
        this.articleSidebar = page.locator('[data-testid="article-sidebar"]');
        this.topNav = page.locator('[data-testid="article-top-nav"]');
        this.articleContent = page.locator('[data-testid="article-content"]');

        // Top navigation elements
        this.backButton = page.locator('[data-testid="back-button"]');
        this.pageTitle = page.locator('[data-testid="article-page-title"]');
        this.revisionSelector = page.locator('[data-testid="revision-selector"]');
        this.revisionDropdown = page.locator('[data-testid="revision-dropdown"]');
        this.selectedRevision = page.locator('[data-testid="selected-revision"]');
        this.editButtons = page.locator('[data-testid="edit-buttons"]');
        this.articleMenuButton = page.locator('[data-testid="article-menu-button"]');
        this.articleStatusButton = page.locator('[data-testid="article-status-button"]');
        this.editArticleButton = page.locator('[data-testid="edit-article-button"]');

        // Article content elements
        this.articleTitle = page.locator('[data-testid="article-title"]');
        this.articleSubtitle = page.locator('[data-testid="article-subtitle"]');
        this.articleMeta = page.locator('[data-testid="article-meta"]');
        this.articleUpdated = page.locator('[data-testid="article-updated"]');
        this.articleVisibility = page.locator('[data-testid="article-visibility"]');

        // Edit mode elements
        this.titleInput = page.locator('[data-testid="title-input"]');
        this.subtitleInput = page.locator('[data-testid="subtitle-input"]');
        this.saveEditButton = page.locator('[data-testid="save-edit-button"]');
        this.cancelEditButton = page.locator('[data-testid="cancel-edit-button"]');
        this.articleEditor = page.locator('[data-testid="article-editor-froala"]');

        // Sidebar elements
        this.sidebarTitle = page.locator('[data-testid="sidebar-title"]');
        this.minimizeSidebarButton = page.locator('[data-testid="minimize-sidebar-button"]');
        this.expandSidebarButton = page.locator('[data-testid="expand-sidebar-button"]');
        this.detailsTab = page.locator('[data-testid="details-tab"]');
        this.eventsTab = page.locator('[data-testid="events-tab"]');
        this.detailsPanel = page.locator('[data-testid="details-panel"]');
        this.eventsPanel = page.locator('[data-testid="events-panel"]');

        // Loading and error states
        this.loadingSpinner = page.locator('[data-testid="article-loading"]');
        this.errorMessage = page.locator('[data-testid="article-error"]');
        this.notFoundMessage = page.locator('[data-testid="article-not-found"]');
    }

    /**
     * Navigates to an article by ID
     * @param articleId The ID of the article to navigate to
     */
    async navigateToArticle(articleId: string) {
        await this.page.goto(`/knowledge/articles/${articleId}`);
        await this.waitForArticleLoad();
    }

    /**
     * Waits for the article to load (spinner to disappear)
     */
    async waitForArticleLoad() {
        // Wait for either the article view to be visible or an error/not found message
        await this.page.waitForSelector(
            ['[data-testid="article-view"]', '[data-testid="article-error"]', '[data-testid="article-not-found"]'].join(
                ','
            )
        );

        // If we see the loading spinner, wait for it to disappear
        if (await this.loadingSpinner.isVisible()) {
            await this.loadingSpinner.waitFor({ state: 'hidden' });
        }
    }

    /**
     * Clicks the back button to navigate back to the knowledge home page
     */
    async navigateBack() {
        await this.backButton.click();
        // Wait for navigation to complete
        await this.page.waitForURL('**/knowledge');
    }

    /**
     * Toggles the sidebar between expanded and minimized states
     */
    async toggleSidebar() {
        if (await this.minimizeSidebarButton.isVisible()) {
            await this.minimizeSidebarButton.click();
        } else if (await this.expandSidebarButton.isVisible()) {
            await this.expandSidebarButton.click();
        }
    }

    /**
     * Switches to edit mode
     */
    async enterEditMode() {
        if (await this.editArticleButton.isVisible()) {
            await this.editArticleButton.click();
            try {
                // Wait for either title input or cancel button to confirm edit mode
                await Promise.race([
                    this.titleInput.waitFor({ state: 'visible', timeout: 10000 }),
                    this.cancelEditButton.waitFor({ state: 'visible', timeout: 10000 })
                ]);
                // Wait for editor to appear
                await this.articleEditor.waitFor();

                console.log('Successfully entered edit mode');
                
                // Log whether the editor is visible (for debugging)
                const isEditorVisible = await this.articleEditor.isVisible().catch(() => false);
                console.log(`Editor visibility state: ${isEditorVisible ? 'visible' : 'hidden'}`);
                
            } catch (error) {
                console.error('Timeout waiting for edit mode indicators:', error);
                throw new Error('Failed to enter edit mode - no edit indicators visible');
            }
        } else {
            console.log('Edit button is not visible, can\'t enter edit mode');
        }
    }

    /**
     * Cancels edit mode
     */
    async cancelEdit() {
        if (await this.cancelEditButton.isVisible()) {
            await this.cancelEditButton.click();
            
            // Wait for edit mode to exit - check for edit button reappearing
            try {
                console.log('Waiting for edit mode to exit');
                await this.editArticleButton.waitFor({ state: 'visible', timeout: 10000 });
                console.log('Successfully exited edit mode');
            } catch (error) {
                console.error('Timeout waiting for edit mode to exit:', error);
                
                // As a fallback, check if we're no longer in edit mode
                const titleInputGone = !(await this.titleInput.isVisible());
                if (titleInputGone) {
                    console.log('Title input is no longer visible, assuming edit mode exited');
                } else {
                    throw new Error('Failed to exit edit mode - still in edit state');
                }
            }
        }
    }

    /**
     * Saves the article while in edit mode
     */
    async saveArticle() {
        if (await this.saveEditButton.isVisible()) {
            await this.saveEditButton.click();
            
            // Wait for save operation to complete - check for edit button reappearing
            try {
                console.log('Waiting for save operation to complete');
                await this.editArticleButton.waitFor({ state: 'visible', timeout: 10000 });
                console.log('Save operation completed successfully');
            } catch (error) {
                console.error('Timeout waiting for save operation:', error);
                
                // As a fallback, check if we're no longer in edit mode
                const titleInputGone = !(await this.titleInput.isVisible());
                if (titleInputGone) {
                    console.log('Title input is no longer visible, assuming save completed');
                } else {
                    // Check if there's an error message visible
                    const errorVisible = await this.page.locator('[data-testid="save-error-message"]').isVisible()
                        || await this.page.locator('.p-toast-message-error').isVisible();
                        
                    if (errorVisible) {
                        throw new Error('Save operation failed - error message visible');
                    } else {
                        throw new Error('Failed to complete save operation - still in edit state');
                    }
                }
            }
        }
    }

    /**
     * Edits the article title
     * @param newTitle The new title text
     */
    async editTitle(newTitle: string) {
        // Enter edit mode directly rather than using enterEditMode method
        if (await this.editArticleButton.isVisible()) {
            await this.editArticleButton.click();
            
            // Wait for title input to be visible
            await this.titleInput.waitFor({ state: 'visible', timeout: 10000 });
            
            // Now fill in the title
            await this.titleInput.clear();
            await this.titleInput.fill(newTitle);
        } else {
            console.log('Edit button is not visible, cannot edit title');
        }
    }

    /**
     * Edits the article subtitle
     * @param newSubtitle The new subtitle text
     */
    async editSubtitle(newSubtitle: string) {
        // Enter edit mode directly rather than using enterEditMode method
        if (await this.editArticleButton.isVisible()) {
            await this.editArticleButton.click();
            
            // Wait for subtitle input to be visible
            await this.subtitleInput.waitFor({ state: 'visible', timeout: 10000 });
            
            // Now fill in the subtitle
            await this.subtitleInput.clear();
            await this.subtitleInput.fill(newSubtitle);
        } else {
            console.log('Edit button is not visible, cannot edit subtitle');
        }
    }

    /**
     * Edits the article content using the Froala editor
     * @param content The HTML content to set
     */
    async editContent(content: string) {
        // Enter edit mode directly rather than using enterEditMode method
        if (await this.editArticleButton.isVisible()) {
            await this.editArticleButton.click();
            
            // Wait for edit mode indicators
            await Promise.race([
                this.titleInput.waitFor({ state: 'visible', timeout: 10000 }),
                this.cancelEditButton.waitFor({ state: 'visible', timeout: 10000 })
            ]);
            
            // Set the content through JavaScript evaluation rather than relying on visible controls
            await this.page.evaluate((content) => {
                // Using any to bypass TypeScript checking for the Froala global object
                if (typeof (window as any).FroalaEditor !== 'undefined' && 
                    (window as any).FroalaEditor.instances && 
                    (window as any).FroalaEditor.instances.length > 0) {
                    (window as any).FroalaEditor.instances[0].html.set(content);
                    return true;
                }
                return false;
            }, content).catch(error => {
                console.error('Failed to set Froala editor content:', error);
            });
        } else {
            console.log('Edit button is not visible, cannot edit content');
        }
    }

    /**
     * Selects a revision from the dropdown
     * @param index The index of the revision to select (0 is the first)
     */
    async selectRevision(index: number) {
        await this.revisionDropdown.click();
        // Wait for dropdown to open
        await this.page.waitForSelector('.p-dropdown-panel');
        // Select the revision at the specified index
        const revisionOptions = this.page.locator('[data-testid^="revision-option-"]');
        await revisionOptions.nth(index).click();
        // Wait for content to update
        await this.page.waitForLoadState('networkidle');
    }

    /**
     * Switches to the events tab in the sidebar
     */
    async switchToEventsTab() {
        await this.eventsTab.click();
        await this.eventsPanel.waitFor({ state: 'visible' });
    }

    /**
     * Switches to the details tab in the sidebar
     */
    async switchToDetailsTab() {
        await this.detailsTab.click();
        await this.detailsPanel.waitFor({ state: 'visible' });
    }

    /**
     * Opens the article menu
     */
    async openArticleMenu() {
        await this.articleMenuButton.click();
        // Wait for menu to appear
        await this.page.waitForSelector('[data-testid="article-menu"]');
    }

    /**
     * Gets the current article status text
     * @returns The status text displayed in the status button
     */
    async getArticleStatus() {
        return await this.articleStatusButton.textContent();
    }
}
