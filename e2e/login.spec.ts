import { LoginPage } from './pages/LoginPage.js';
import { MSWAdapter as test, expect, syncMockApiIndicator } from './fixtures/msw-adapter.js';
import { mockUserStatusResponse, mockUserLoginResponse } from '../src/mock-api/data/auth.mock.js';
import { mockGetIssueResponse } from '../src/mock-api/data/issues.mock.js';
import { Request } from '@playwright/test';
import { fail } from 'assert';
import { http, HttpResponse, passthrough } from 'msw';

// Define result type to help TypeScript
type LoginResult = 
    | { status: 'success'; url: string }
    | { status: 'error'; message: string | null }
    | { status: 'timeout'; error: any };

const login_email = mockUserStatusResponse.users.email;
const login_password = 'password123';

// Test suite for the login functionality
test.describe('Login Page', () => {

    test.beforeEach(async ({ page }) => {
        if (process.env.PLAYWRIGHT_DEBUG_LOGS === '1') {
            page.on('console', msg => console.log(`Browser console: ${msg.text()}`));
        }
        //  navigate to the application
        await page.goto('/');
        await syncMockApiIndicator(page);
    });

    test('should display login page with all elements', async ({ page }) => {
        const loginPage = new LoginPage(page);
        // Navigate again after MSW setup
        await loginPage.navigateToLogin();
        
        // Verify all main sections are visible
        await expect(loginPage.loginPage).toBeVisible();
        await expect(loginPage.logoContainer).toBeVisible();
        await expect(loginPage.logoImage).toBeVisible();
        await expect(loginPage.bravoLoginScreen).toBeVisible();
        await expect(loginPage.languageSelector).toBeVisible();

        // Verify form elements are present
        await expect(loginPage.emailInput).toBeVisible();
        await expect(loginPage.passwordInput).toBeVisible();
        await expect(loginPage.rememberMeCheckbox).toBeVisible();
        await expect(loginPage.submitButton).toBeVisible();

        // Verify language options are present
        await expect(loginPage.englishOption).toBeVisible();
        await expect(loginPage.spanishOption).toBeVisible();
        await expect(loginPage.germanOption).toBeVisible();
    });

    test('should focus email input on page load', async ({ page }) => {
        const loginPage = new LoginPage(page);
        // Navigate again after MSW setup
        await loginPage.navigateToLogin();
        
        // Check if email input is focused
        await expect(loginPage.emailInput).toBeFocused();
    });

    test('should change language when language option is clicked', async ({ page }) => {
        const loginPage = new LoginPage(page);
        // Navigate again after MSW setup
        await loginPage.navigateToLogin();
        
        // Default language should be English (assuming default is English)
        expect(await loginPage.getSelectedLanguage()).toBe('en');

        // Change to Spanish
        await loginPage.changeLanguage('es');
        expect(await loginPage.getSelectedLanguage()).toBe('es');

        // Change to German
        await loginPage.changeLanguage('de');
        expect(await loginPage.getSelectedLanguage()).toBe('de');

        // Change back to English
        await loginPage.changeLanguage('en');
        expect(await loginPage.getSelectedLanguage()).toBe('en');
    });

    test('should submit login form with valid credentials', async ({ page, worker }) => {
        if (process.env.PLAYWRIGHT_DEBUG_LOGS === '1') {
            // Add network request logging for all traffic
            page.on('request', request => {
                console.log(`🔶 Request: ${request.method()} ${request.url()}`);
            });
            
            page.on('response', response => {
                console.log(`🔷 Response: ${response.status()} ${response.request().method()} ${response.url()}`);
                if (response.status() >= 400) {
                    response.text().then(body => {
                        console.log(`🔴 Error response body: ${body}`);
                    }).catch(e => {
                        console.log('Could not read response body:', e);
                    });
                }
            });
        }
        
        const loginPage = new LoginPage(page);
        
        // Navigate to login page
        await loginPage.navigateToLogin();
        
        console.log('👉 About to login with credentials:', login_email, '(password hidden)');
        
        // Login with valid credentials from our fixture
        await loginPage.login(login_email, login_password);

        // Wait for either successful navigation or error message
        const result = await Promise.race([
            // Success path - URL changes to inbox
            page.waitForURL(/.*\/inbox.*/, { timeout: 15000 })
                .then(() => ({ status: 'success', url: page.url() } as LoginResult)),
            
            // Error path - error message appears
            page.locator('[data-testid="login-error-message"], .login-error, .error-message')
                .waitFor({ state: 'visible', timeout: 15000 })
                .then(async () => ({ 
                    status: 'error', 
                    message: await page.locator('[data-testid="login-error-message"], .login-error, .error-message').textContent() 
                } as LoginResult))
        ]).catch(error => ({ status: 'timeout', error } as LoginResult));
        
        // Handle the result
        if (result.status === 'error') {
            console.error(`Login error displayed: ${(result as { message: string | null }).message}`);
            fail(`Login failed with error message: ${(result as { message: string | null }).message}`);
        } else if (result.status === 'timeout') {
            console.error('Login timed out waiting for navigation or error');
            fail(`Login timed out: ${(result as { error: any }).error}`);
        }
        
        // Success case
        console.log(`Successfully navigated to: ${(result as { url: string }).url}`);
        
        // Verify some UI element that should be present after login
        await expect(page.locator('[data-testid="root-layout"]').or(
                 page.locator('.root-layout')
        )).toBeVisible({ timeout: 10000 });
    });

    test('should show error for invalid credentials', async ({ page, worker }) => {
        const loginPage = new LoginPage(page);
        // No MSW adapter for this test - we'll use manual route interception
        
        // Log network requests to help debug
        if (process.env.PLAYWRIGHT_DEBUG_LOGS === '1') {
            page.on('request', (request: Request) => {
                console.log(`Request: ${request.method()} ${request.url()}`);
            });
        }
        
        // Set up route interception before navigation
        // Create a flag to track if the intercepted request was triggered
        let loginAttempted = false;

        // Override the auth/login endpoint for this test case to ensure we get an error
        await worker.use(
            http.post('*/admin/v4/core/index.php*', async ({ request }) => {
                const url = new URL(request.url);
                if (url.searchParams.get('sAction') === 'userLogin') {
                    loginAttempted = true;
                    console.log('🟢 Login request matched! Returning mock login response');
                    return HttpResponse.json(
                        {
                            success: false,
                            message: 'Invalid email or password'
                        },
                        { status: 401 }
                    );
                }
                console.log('❌ Not a login request, passing through');
                return passthrough();
            })
        );
        
        // Navigate to login page AFTER setting up route interception
        await loginPage.navigateToLogin();
        
        // Attempt to login with invalid credentials
        console.log('Attempting login with wrong credentials');
        await loginPage.login('<EMAIL>', 'wrongpassword');
        
        // Wait for any network activity to settle
        await page.waitForLoadState('networkidle');
        
        // Check if our route interception was triggered
        console.log(`Route interception triggered: ${loginAttempted}`);
        
        // We expect to still be on the login page (not redirected)
        // Verify the current URL contains "login"
        const currentUrl = page.url();
        console.log(`Current URL after login attempt: ${currentUrl}`);
        
        // Test passes if we're still on the login page
        // This is our primary assertion - we didn't get redirected, so login failed
        expect(currentUrl).toContain('/login');
        
        // Additional verification - check if the email input still has our test value
        const emailValue = await loginPage.emailInput.inputValue();
        console.log(`Email input value after login attempt: ${emailValue}`);
        expect(emailValue).toBe('<EMAIL>');
    });

    test('should validate form fields', async ({ page }) => {
        const loginPage = new LoginPage(page);
        // Navigate again after MSW setup
        await loginPage.navigateToLogin();
        
        // Try to submit empty form
        await loginPage.submitButton.click();
        await page.waitForLoadState('networkidle');

        // Should show validation errors
        await expect(loginPage.loginPage.getByText('Login request failed')).toBeVisible();

        // Fill only email and try to submit
        await loginPage.emailInput.fill('<EMAIL>');
        await loginPage.submitButton.click();
        await page.waitForLoadState('networkidle');
        await expect(loginPage.loginPage.getByText('Login request failed')).toBeVisible();

        // Fill only password and try to submit
        await loginPage.emailInput.clear();
        await loginPage.passwordInput.fill('password123');
        await loginPage.submitButton.click();
        await page.waitForLoadState('networkidle');
        await expect(loginPage.loginPage.getByText('Login request failed')).toBeVisible();
    });

    test.skip('should save remember me preference', async ({ context, page }) => {
        const loginPage = new LoginPage(page);
        // Navigate again after MSW setup
        await loginPage.navigateToLogin();
        
        // Login with remember me checked
        await loginPage.login('<EMAIL>', 'password123', true);

        // Check localStorage or cookies to verify remember me was saved
        // This will depend on how your application stores the remember me preference
        const cookies = await context.cookies();
        const hasRememberMeCookie = cookies.some((cookie) => cookie.name === 'remember_me');
        expect(hasRememberMeCookie).toBe(true);
    });
});
