import { test as base, expect, Page } from '@playwright/test';
import { http, HttpResponse, passthrough, delay } from 'msw';
import type { MockServiceWorker } from 'playwright-msw';
import { createWorkerFixture } from 'playwright-msw';
import { handlers } from '../../src/mock-api/handlers/index.js';

// Store fixture ID in memory
let currentFixtureId = 1;

// Create a function to set the fixture ID
export function setFixtureId(id: number): void {
    if (id !== currentFixtureId) {
        currentFixtureId = id;
    }
    console.log(`Set fixture ID to: ${id}`);
}

// Sync the UI state with error handling
export function syncMockApiIndicator(page: Page) {
    return page.evaluate(() => {
        try {
            // Check if localStorage is available
            if (typeof window.localStorage !== 'undefined') {
                // Set localStorage to match actual MSW state
                localStorage.setItem('msw-enabled', 'true');
                // Dispatch event to notify components
                window.dispatchEvent(new CustomEvent('msw-fixture-change', {
                    detail: { fixtureId: 1 }
                }));
            }
            return true;
        } catch (error) {
            // Gracefully handle localStorage access errors
            console.warn('⚠️ Could not sync mock API indicator:', error);
            return false;
        }
    }).catch(error => {
        // Handle page.evaluate errors
        console.warn('⚠️ Failed to execute page.evaluate for syncing mock indicator:', error);
        return false;
    });
}

// Create the extended test with MSW capabilities
export const MSWAdapter = base.extend<{
    worker: MockServiceWorker;
    http: typeof http;
}>({
    worker: createWorkerFixture(handlers),
    http
});

// Create a delay handler
export function createDelayHandler(urlPattern: string, delayMs: number) {
    return http.get(urlPattern, async () => {
        await delay(delayMs);
        return passthrough();
    });
}

// Re-export other useful components
export { expect, HttpResponse, delay }; 