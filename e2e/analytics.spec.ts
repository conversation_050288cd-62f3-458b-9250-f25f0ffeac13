import { test, expect } from '@playwright/test';

test.describe('Analytics', () => {
    test.beforeEach(async ({ page }) => {
        // Navigate to login page and login
        await page.goto('/login');
        
        // Fill in login credentials (using mock data)
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'password');
        await page.click('button[type="submit"]');
        
        // Wait for navigation to complete
        await page.waitForURL('/inbox');
    });

    test('should show analytics navigation when user has dashboards_view permission', async ({ page }) => {
        // Mock user permissions with dashboards_view
        await page.route('**/api/**', async (route) => {
            if (route.request().url().includes('user') || route.request().url().includes('auth')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        perms: ['dashboards_view', 'manage_dashboards']
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Reload to apply permissions
        await page.reload();
        
        // Verify analytics navigation item is visible
        const analyticsNav = page.locator('[data-testid="nav-analytics"], .nav-item:has-text("Analytics")');
        await expect(analyticsNav).toBeVisible();
    });

    test('should hide analytics navigation when user lacks dashboards_view permission', async ({ page }) => {
        // Mock user permissions without dashboards_view
        await page.route('**/api/**', async (route) => {
            if (route.request().url().includes('user') || route.request().url().includes('auth')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        perms: ['kb_view'] // Only knowledge permissions
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Reload to apply permissions
        await page.reload();
        
        // Verify analytics navigation item is not visible
        const analyticsNav = page.locator('[data-testid="nav-analytics"], .nav-item:has-text("Analytics")');
        await expect(analyticsNav).not.toBeVisible();
    });

    test('should navigate to analytics page and load dashboard', async ({ page }) => {
        // Mock analytics API response
        await page.route('**/api/core/**', async (route) => {
            if (route.request().url().includes('sAction=gooddatacloudlogin')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        jwt: 'mock-jwt-token',
                        dashboard_uri: 'https://example.gooddata.com/dashboards/embedded/#/workspace/test/dashboard/test',
                        current_server_time: new Date().toISOString()
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Click on the analytics navigation item
        await page.click('[data-testid="nav-analytics"], .nav-item:has-text("Analytics")');
        
        // Verify we're on the analytics page
        await page.waitForURL('/analytics');
        
        // Verify page title
        await expect(page.locator('h1:has-text("Analytics")')).toBeVisible();
        
        // Verify iframe is loaded
        await expect(page.locator('iframe[title="Analytics Dashboard"]')).toBeVisible();
    });

    test('should show management access badge for users with manage_dashboards permission', async ({ page }) => {
        // Mock user permissions with manage_dashboards
        await page.route('**/api/**', async (route) => {
            if (route.request().url().includes('user') || route.request().url().includes('auth')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        perms: ['dashboards_view', 'manage_dashboards']
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Mock analytics API response
        await page.route('**/api/core/**', async (route) => {
            if (route.request().url().includes('sAction=gooddatacloudlogin')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        jwt: 'mock-jwt-token',
                        dashboard_uri: 'https://example.gooddata.com/dashboards/embedded/#/workspace/test/dashboard/test',
                        current_server_time: new Date().toISOString()
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Navigate to analytics page
        await page.goto('/analytics');
        
        // Verify management access badge is shown
        await expect(page.locator('.management-badge:has-text("Management Access")')).toBeVisible();
    });

    test('should not show management access badge for users without manage_dashboards permission', async ({ page }) => {
        // Mock user permissions without manage_dashboards
        await page.route('**/api/**', async (route) => {
            if (route.request().url().includes('user') || route.request().url().includes('auth')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        perms: ['dashboards_view'] // Only view permission
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Mock analytics API response
        await page.route('**/api/core/**', async (route) => {
            if (route.request().url().includes('sAction=gooddatacloudlogin')) {
                await route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: true,
                        jwt: 'mock-jwt-token',
                        dashboard_uri: 'https://example.gooddata.com/dashboards/embedded/#/workspace/test/dashboard/test',
                        current_server_time: new Date().toISOString()
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Navigate to analytics page
        await page.goto('/analytics');
        
        // Verify management access badge is not shown
        await expect(page.locator('.management-badge:has-text("Management Access")')).not.toBeVisible();
    });

    test('should handle analytics API errors gracefully', async ({ page }) => {
        // Mock analytics API to return an error
        await page.route('**/api/core/**', async (route) => {
            if (route.request().url().includes('sAction=gooddatacloudlogin')) {
                await route.fulfill({
                    status: 500,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        success: false,
                        error: 'Internal Server Error'
                    })
                });
            } else {
                await route.continue();
            }
        });

        // Navigate to analytics page
        await page.goto('/analytics');
        
        // Verify error state is shown
        await expect(page.locator('.error-state')).toBeVisible();
        await expect(page.locator('button:has-text("Retry")')).toBeVisible();
    });

    test('should load analytics dashboard', async ({ page }) => {
        // Navigate to analytics page
        await page.goto('/analytics');
        
        // Wait for the loading state to complete
        await page.waitForSelector('.loading-state', { state: 'hidden', timeout: 10000 });
        
        // Verify the iframe is present
        await expect(page.locator('.analytics-iframe')).toBeVisible();
        
        // Verify the iframe has the correct src attribute structure
        const iframe = page.locator('.analytics-iframe');
        const src = await iframe.getAttribute('src');
        expect(src).toContain('gooddata.com');
        expect(src).toContain('dashboards/embedded');
        expect(src).toContain('apiTokenAuthentication=true');
    });

    test('should handle loading state', async ({ page }) => {
        // Navigate to analytics page
        await page.goto('/analytics');
        
        // Initially should show loading state
        await expect(page.locator('.loading-state')).toBeVisible();
        await expect(page.locator('.loading-spinner')).toBeVisible();
        await expect(page.locator('text=Loading analytics dashboard...')).toBeVisible();
    });

    test('should handle error state gracefully', async ({ page }) => {
        // This test would require mocking an API failure
        // For now, we'll just verify the error handling structure exists
        await page.goto('/analytics');
        
        // Wait for loading to complete
        await page.waitForSelector('.loading-state', { state: 'hidden', timeout: 10000 });
        
        // Verify retry button exists in case of error (though it shouldn't be visible in normal flow)
        const retryButton = page.locator('.retry-button');
        if (await retryButton.isVisible()) {
            await expect(retryButton).toContainText('Retry');
        }
    });

    test('should have analytics navigation item in main nav', async ({ page }) => {
        // Go to any page to check navigation
        await page.goto('/inbox');
        
        // Verify analytics navigation item exists
        const analyticsNav = page.locator('.nav-item:has-text("Analytics"), [data-testid="nav-analytics"]');
        await expect(analyticsNav).toBeVisible();
        
        // Verify it has the correct icon
        await expect(analyticsNav.locator('i.pi-chart-bar')).toBeVisible();
    });
}); 