import { setup } from '@storybook/vue3';
import { journeysUrl, tasksUrl } from '../src/common/keys';

import { library } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faChevronRight, faCircle, faForward, faPhoneVolume, faPlus, faSquareFull, faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

// import '../src/components/JourneyWorkflow/style.css';

setup((app) => {
  library.add(faCheck,faChevronRight,faCircle, faForward,faPhoneVolume,faPlus,faSquareFull,faXmark);
  app.component('font-awesome-icon', FontAwesomeIcon);
  
  app.provide(journeysUrl, 'http://localhost:8080');
  app.provide(tasksUrl, 'https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage');
});

export const parameters = {
  actions: { argTypesRegex: "^on[A-Z].*" },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  }
}