import pluginVue from 'eslint-plugin-vue';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';
import pluginVitest from '@vitest/eslint-plugin';
import pluginPlaywright from 'eslint-plugin-playwright';
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting';

// To allow more languages other than `ts` in `.vue` files, uncomment the following lines:
// import { configureVueProject } from '@vue/eslint-config-typescript'
// configureVueProject({ scriptLangs: ['ts', 'tsx'] })
// More info at https://github.com/vuejs/eslint-config-typescript/#advanced-setup

export default defineConfigWithVueTs(
    {
        name: 'app/files-to-lint',
        files: ['**/*.{ts,mts,tsx,vue}'],
    },

    {
        name: 'app/files-to-ignore',
        ignores: [
            '**/dist/**',
            '**/dist-ssr/**',
            '**/coverage/**',
            '**/tests/**',
            '**/__tests__/**',
            '**/node_modules/**',
            '**/dist/**',
            '**/dist-ssr/**',
            '**/coverage/**',
            '**/tests/**',
            '**/__tests__/**',
            '**/node_modules/**',
            '**/playwright-report/**',
            '.eslintrc.cjs',
            'vitest.config.ts',
            'vite.config.ts',
            'package.json',
            'package-lock.json',
            'tsconfig.json',
            'tsconfig.node.json',
            'tsconfig.app.json',
            'src/main.ts',
            'src/types/contracts/**/*',
            'e2e/**/*',
            'src/modules/froala/plugins/code/prism/**/*',
        ],
    },

    pluginVue.configs['flat/essential'],
    vueTsConfigs.recommended,

    {
        rules: {
            'vue/multi-word-component-names': 'off',
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-unused-vars': 'off',
        },
    },

    {
        ...pluginVitest.configs.recommended,
        files: ['src/**/__tests__/*'],
    },

    {
        ...pluginPlaywright.configs['flat/recommended'],
        files: ['e2e/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    },
    skipFormatting
);
