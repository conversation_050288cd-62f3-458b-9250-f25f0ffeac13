<!-- DeleteArticleModal.vue -->
<script setup lang="ts">
import { ref } from 'vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { useToast } from 'primevue/usetoast';
import { useConfirm } from 'primevue/useconfirm';
import { stripHtmlAndDecodeEntities } from '../utils/helpers';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps<{
    articleId?: string;
    articleTitle?: string;
}>();

const emit = defineEmits<{
    (e: 'deleted', articleId: string): void;
    (e: 'error', error: any): void;
}>();

const store = useKnowledgeStore();
const toast = useToast();
const confirm = useConfirm();
const isDeleting = ref(false);

// Method to show the delete confirmation dialog
const showConfirmation = () => {
    if (!props.articleId || !props.articleTitle) {
        console.error('Missing article ID or title for delete operation');
        return;
    }

    // Clean the article title if it contains HTML
    const cleanTitle = stripHtmlAndDecodeEntities(props.articleTitle);

    confirm.require({
        message: t('knowledge.delete_article_confirmation_message', { title: cleanTitle }),
        header: t('knowledge.delete_article_confirmation_title'),
        icon: 'pi pi-exclamation-triangle',
        acceptClass: 'p-button-danger',
        rejectClass: 'p-button-secondary',
        accept: () => deleteArticle(),
        reject: () => {
            // User rejected the confirmation, do nothing
        },
    });
};

// Method to handle article deletion
const deleteArticle = async () => {
    if (!props.articleId) return;

    isDeleting.value = true;

    try {
        const response = await store.deleteArticle(props.articleId);
        console.log('Delete response:', response);

        // Show success toast
        toast.add({
            severity: 'success',
            summary: t('knowledge.delete_article_success_title'),
            detail: t('knowledge.delete_article_success_message', { title: props.articleTitle }),
            life: 5000,
        });

        // Emit deleted event with article ID
        emit('deleted', props.articleId);
    } catch (error: any) {
        console.error('Error deleting article:', error);

        // Show error toast
        toast.add({
            severity: 'error',
            summary: t('knowledge.delete_article_error_title'),
            detail: t('knowledge.delete_article_error_message', { error: error.message || 'Unknown error' }),
            life: 5000,
        });

        // Emit error event
        emit('error', error);
    } finally {
        isDeleting.value = false;
    }
};

// Method to expose for external components to call
defineExpose({
    showConfirmation,
});
</script>

<template>
    <!-- Empty component - dialog is managed by the parent's BravoConfirmDialog -->
    <div class="delete-article-modal-container"></div>
</template>
