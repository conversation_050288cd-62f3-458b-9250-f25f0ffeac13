# Internationalization (i18n) Guide

This project uses [vue-i18n](https://vue-i18n.intlify.dev/) for internationalization. Below is a guide on how to use it effectively in your components.

## Available Languages

The application currently supports the following languages:
- English (en)
- Spanish (es)

## Adding New Translations

All translations are stored in JSON files in the `src/locales` directory. Each language has its own file, e.g., `en.json`, `es.json`.

To add new translation keys:
1. Add the new key to `src/locales/en.json` (primary language)
2. Add the same key with a translated value to all other language files
3. Update the TypeScript interface in `src/types/i18n.d.ts` to include the new key

## Using Translations in Components

### Template Usage

```vue
<template>
  <div>
    <h1>{{ $t('common.welcome') }}</h1>
    <p>{{ $t('errors.something_went_wrong') }}</p>
  </div>
</template>
```

### Script Usage (Options API)

```vue
<script>
export default {
  methods: {
    showMessage() {
      return this.$t('common.welcome')
    }
  }
}
</script>
```

### Script Usage (Composition API)

```vue
<script setup>
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

// Access translations
const welcomeMessage = t('common.welcome')

// Change language
function changeLanguage(localeCode) {
  locale.value = localeCode
}
</script>
```

## Language Switching

The application includes a `LanguageSwitcher` component that allows users to change their preferred language. The user's language preference is stored in the browser's localStorage for persistence between sessions.

## Adding a New Language

To add a new language:

1. Create a new locale file in `src/locales/`, for example `fr.json`
2. Copy the structure of `en.json` and translate all values
3. Update the `availableLocales` object in `src/components/LanguageSwitcher.vue`
4. Update the supported languages array in `src/i18n/index.ts`

## Best Practices

1. Use nested keys to organize translations (e.g., `common.welcome` instead of just `welcome`)
2. Keep translation keys lowercase and use underscores for spaces
3. Don't hardcode text in components; always use translation keys
4. Use variables for dynamic content: `$t('greeting', { name: userName })`
5. When adding new features, add translations from the beginning rather than as an afterthought 