# Installation Instructions
## Prerequisites
1. You must have nvm installed on your machine. If you don't have it, you can install it by following the steps
[here](https://github.com/nvm-sh/nvm#installing-and-updating)

## Installation Steps
1. Run `nvm install` to install the correct version of node
2. Run `nvm use` to use the correct version of node
3. Run `npm install -g yarn` to install bun
4. Run `yarn install` to install the dependencies
5. Configure the playwright framework by following the UI test readme [here](./tests/ui/README.md)

# General Instructions
## Running the app
Please follow the steps described in the [app-v4-local-setup-with-proxy.md](./app-v4-local-setup-with-proxy.md) file to run the app.
## Cleaning up the project
Since app-v4 is a monorepo which contains many workspaces with individual package.json files and separate node_modules
directories, it can be difficult to clean up the project. To make this easier, we use lerna to clean up the project. 
If you want to learn more about learna you can read about it [here](https://lerna.js.org/). 

The below command should be run from the root of the app-v4 repo. The below script will (1) remove all node_modules directories and 
package-lock.json files from the project via lerna and (2) reinstall all dependencies.
```
yarn tableflip
```

# Testing Instructions
We use lerna as our centralised task runner in the app-v4 repo. 
## Running All Automated Tests
You can run all tests in the project by running the following command
from the root of the project
```
npx lerna run test
```
## Running Automated UI Tests
You can run all the automated UI tests from the root of the project by running the following command from the root of the project
```
npx lerna run test --scope=uiTest
```
Please Note: If you want to learn more about additional options for running automated UI tests please look at the UI readme [here](./tests/ui/README.md)

## Sencha resources
You can find the sencha best practices here - [sencha-best-practices.md](./documentation/sencha-best-practices.md)

# Froala Editor Configuration

A reusable configuration for Froala Editor based on settings used in the OvationCXM application. This repository provides a TypeScript configuration file that can be easily integrated into any TypeScript or JavaScript project.

## Files Included

- **froala-config.ts**: TypeScript configuration file with type definitions
- **froala-example.html**: Example HTML implementation of the editor

## Getting Started

### Installation

1. First, install Froala Editor and its dependencies:

```bash
npm install froala-editor
# If using TypeScript
npm install @types/froala-editor
```

2. Copy the configuration file to your project:
   - `froala-config.ts`

### Basic Usage

```typescript
// Import the configuration function
import buildFroalaConfig from './froala/froala-config';

// Initialize the editor
document.addEventListener('DOMContentLoaded', function() {
  // Get the configuration with dynamic values
  const froalaConfig = buildFroalaConfig();
  
  const editor = new FroalaEditor('#editor', froalaConfig);
  
  // You can modify configuration as needed
  editor.opts.imageUploadParams.object = 'your-object-name';
  editor.opts.imageUploadParams.object_id = 'your-object-id';
});
```

#### TypeScript

```typescript
// Import the configuration function
import buildFroalaConfig from './froala/froala-config';

// Initialize the editor
document.addEventListener('DOMContentLoaded', function() {
  // Get the configuration with dynamic values
  const froalaConfig = buildFroalaConfig();
  
  const editor = new FroalaEditor('#editor', froalaConfig);
  
  // With TypeScript you get type checking
  editor.opts.imageUploadParams.object = 'your-object-name';
  editor.opts.imageUploadParams.object_id = 'your-object-id';
});
```

### Customizing the Configuration

The configuration is highly customizable. You can modify any part of it to suit your needs:

```typescript
// Import the configuration function
import buildFroalaConfig from './froala/froala-config';

// Get the base configuration
const baseConfig = buildFroalaConfig();

// Create a custom configuration by extending the base config
const customConfig = {
  ...baseConfig,
  placeholderText: 'Type your content here...',
  toolbarButtons: {
    // Your custom toolbar configuration
  }
};

// Initialize the editor with your custom config
const editor = new FroalaEditor('#editor', customConfig);
```

## Features

The configuration includes settings for:

- Basic editor settings (placeholder text, character count)
- Toolbar buttons and organization
- Paragraph styles (info blocks, warning blocks, etc.)
- Text formatting options
- File upload handling
- HTML attribute allowlists
- Word paste handling
- Link settings
- Table styling
- and more...

## Example HTML Page

An example HTML page is included (`froala-example.html`) that demonstrates how to set up the editor in a web page with:

- Required CSS and JavaScript includes
- Toolbar configuration
- Custom paragraph styles
- Event handling

To use it, simply open the HTML file in a browser or integrate it into your project.

## Additional Notes

- The configuration is based on Froala Editor v3.1.1 but should be compatible with other v3.x versions
- Custom CSS styles for the various paragraph formats (alert blocks, etc.) should be included in your project
- Make sure to configure the file upload endpoints according to your server setup
- The TypeScript configuration includes complete type definitions for better development experience

## License

This configuration is provided as-is under the MIT license. Froala Editor itself requires a separate license for production use.