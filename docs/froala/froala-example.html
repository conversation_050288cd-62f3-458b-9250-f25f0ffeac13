<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Froala Editor Example</title>
  
  <!-- Include Froala Editor CSS -->
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/froala_editor.pkgd.min.css" rel="stylesheet">
  
  <!-- Include Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
  
  <!-- Include Froala CSS plugins -->
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/code_view.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/colors.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/emoticons.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/image_manager.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/image.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/table.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/css/plugins/video.min.css" rel="stylesheet">
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1 {
      color: #444;
      margin-bottom: 30px;
    }
    
    label {
      display: block;
      margin-bottom: 10px;
      font-weight: bold;
    }
    
    #editor-container {
      margin-bottom: 20px;
    }
    
    .btn-container {
      margin-top: 20px;
    }
    
    button {
      padding: 8px 16px;
      background-color: #0078ff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    
    button:hover {
      background-color: #0066cc;
    }
    
    #output {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    
    .alert-info {
      background-color: #d1ecf1;
      border: 1px solid #bee5eb;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    
    .alert-warning {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    
    .alert-danger {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    
    .alert-success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    
    .borderemphasis {
      border: 3px solid #666;
      padding: 10px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <h1>Froala Editor Implementation Example</h1>
  
  <div id="editor-container">
    <label for="editor">Rich Text Editor:</label>
    <textarea id="editor"></textarea>
  </div>
  
  <div class="btn-container">
    <button id="get-html">Get HTML</button>
    <button id="get-text">Get Text</button>
    <button id="clear-editor">Clear Editor</button>
    <button id="add-info-block">Add Info Block</button>
  </div>
  
  <div id="output" style="display: none;">
    <h3>Output:</h3>
    <div id="output-content"></div>
  </div>

  <!-- Include jQuery -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  
  <!-- Include Froala Editor JS -->
  <script src="https://cdn.jsdelivr.net/npm/froala-editor@3.1.1/js/froala_editor.pkgd.min.js"></script>

  <!-- Initialize Froala Editor -->
  <script>
    // Initialize editor with our configuration
    document.addEventListener('DOMContentLoaded', function() {
      // Froala configuration
      const froalaConfig = {
        // Basic Configuration
        placeholderText: 'Enter content here...',
        charCounterCount: true,
        charCounterMax: -1,
        tooltipStrict: true,
        iframe: false,
        
        // Toolbar Configuration
        toolbarSticky: true,
        toolbarStickyOffset: 0,
        
        // Content Styling
        paragraphStyles: {
          'alert-info': 'Info/Notice',
          'alert-warning': 'Warning',
          'alert-danger': 'Danger',
          'alert-success': 'Success',
          'borderemphasis': 'Emphasized border',
        },
        
        // Enter Key Behavior
        enter: 'P',
        
        // HTML Configuration
        htmlAllowedAttrs: [
          'accept', 'accept-charset', 'accesskey', 'action', 'align', 'allowfullscreen', 'allowtransparency', 'alt', 'async',
          'autocomplete', 'autofocus', 'autoplay', 'autosave', 'background', 'bgcolor', 'border', 'charset', 'cellpadding',
          'cellspacing', 'checked', 'cite', 'class', 'color', 'cols', 'colspan', 'content', 'contenteditable', 'contextmenu',
          'controls', 'coords', 'data', 'data-.*', 'datetime', 'default', 'defer', 'dir', 'dirname', 'disabled', 'download',
          'draggable', 'dropzone', 'enctype', 'for', 'form', 'formaction', 'frameborder', 'headers', 'height', 'hidden', 'high',
          'href', 'hreflang', 'http-equiv', 'icon', 'id', 'ismap', 'itemprop', 'keytype', 'kind', 'label', 'lang', 'language',
          'list', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'mozallowfullscreen', 'multiple', 'muted', 'name',
          'novalidate', 'open', 'optimum', 'pattern', 'ping', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate',
          'radiogroup', 'readonly', 'rel', 'required', 'reversed', 'rows', 'rowspan', 'sandbox', 'scope', 'scoped', 'scrolling',
          'seamless', 'selected', 'shape', 'size', 'sizes', 'span', 'src', 'srcdoc', 'srclang', 'srcset', 'start', 'step', 'summary',
          'spellcheck', 'style', 'tabindex', 'target', 'title', 'type', 'translate', 'usemap', 'value', 'valign', 'webkitallowfullscreen',
          'width', 'wrap', 'aria-.*'
        ],
        
        // Word Paste Handling
        wordDeniedTags: [],
        wordDeniedAttrs: [],
        wordAllowedStyleProps: [
          'font-family', 'font-size', 'background', 'color', 'width', 'text-align', 
          'vertical-align', 'background-color', 'padding', 'margin', 'height', 
          'margin-top', 'margin-left', 'margin-right', 'margin-bottom',
          'text-decoration', 'font-weight', 'font-style', 'border'
        ],
        
        // Link Configuration
        linkNoOpener: true,
        linkNoReferrer: true,
        
        // Inline Classes
        inlineClasses: {
          'fr-class-code': 'Code',
          'fr-class-highlighted': 'Highlighted',
          'fr-class-transparent': 'Transparent'
        },
        
        // Custom toolbar buttons configuration
        toolbarButtons: {
          'moreText': {
            'buttons': ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'textColor', 'backgroundColor', 'clearFormatting'],
            'buttonsVisible': 3
          },
          'moreParagraph': {
            'buttons': ['alignLeft', 'alignCenter', 'alignRight', 'formatOL', 'formatUL', 'paragraphStyle', 'outdent', 'indent', 'quote'],
            'buttonsVisible': 3
          },
          'moreRich': {
            'buttons': ['insertLink', 'insertTable', 'emoticons', 'specialCharacters', 'insertHR'],
            'buttonsVisible': 3
          },
          'moreMisc': {
            'buttons': ['undo', 'redo', 'fullscreen', 'html'],
            'buttonsVisible': 2,
            'align': 'right'
          }
        },
        
        // Events
        events: {
          'initialized': function() {
            console.log('Editor initialized');
          },
          'contentChanged': function() {
            console.log('Content changed');
          }
        }
      };
      
      // Initialize the editor
      const editor = new FroalaEditor('#editor', froalaConfig);
      
      // Button handlers
      document.getElementById('get-html').addEventListener('click', function() {
        const html = editor.html.get();
        document.getElementById('output').style.display = 'block';
        document.getElementById('output-content').innerHTML = escapedHTML(html);
      });
      
      document.getElementById('get-text').addEventListener('click', function() {
        const text = editor.text.get();
        document.getElementById('output').style.display = 'block';
        document.getElementById('output-content').textContent = text;
      });
      
      document.getElementById('clear-editor').addEventListener('click', function() {
        editor.html.set('');
        document.getElementById('output').style.display = 'none';
      });
      
      document.getElementById('add-info-block').addEventListener('click', function() {
        editor.html.insert('<div class="alert-info">This is an information notice block</div>');
      });
      
      // Helper function to show HTML as text
      function escapedHTML(html) {
        return html.replace(/&/g, '&amp;')
                  .replace(/</g, '&lt;')
                  .replace(/>/g, '&gt;')
                  .replace(/"/g, '&quot;')
                  .replace(/'/g, '&#039;');
      }
    });
  </script>
</body>
</html> 