# Two-Step Authentication Flow

## Overview

This document describes the implementation of a two-step authentication flow in our application. The new flow improves security and user experience by separating the email verification step from the password submission step and supporting Single Sign-On (SSO) providers.

## Implementation Details

### Authentication Flow

1. **Initial State**:
   - User is presented with only an email input field, "Next" button, and "Forgot password" link
   - The password field is not visible

2. **Email Verification**:
   - When the user enters an email and clicks "Next", we call the `/api/core/?sAction=getUserAuth` endpoint
   - This endpoint returns information about available authentication methods for the email

3. **Authentication Methods Handling**:
   - Based on the response, the UI adapts to show appropriate authentication options:

     **Internal Authentication Only**:
     - If `canAuthenticateInternal` is true and `canAuthenticateExternal` is false,
       the password field is shown for traditional login

     **SSO Authentication Only**:
     - If `canAuthenticateInternal` is false and `canAuthenticateExternal` is true:
       - If there's only one SSO provider, automatically redirect to its URL
       - If there are multiple SSO providers, display a dialog for the user to choose one

     **Hybrid Authentication**:
     - If both `canAuthenticateInternal` and `canAuthenticateExternal` are true,
       show the password field for internal login and SSO options below

     **No Authentication**:
     - If both `canAuthenticateInternal` and `canAuthenticateExternal` are false,
       display an error message

4. **Password Authentication**:
   - When the user enters a password and clicks "Login", the system proceeds with the standard authentication flow using the existing login endpoint

5. **External Authentication**:
   - If the user selects an external authentication provider, they are redirected to the SSO login URL

### Response Format

The `/admin/v4/core/?sAction=getUserAuth` endpoint returns the following data structure:

```json
{
    "success": true,
    "canAuthenticateInternal": true,
    "canAuthenticateExternal": true,
    "externalIdPs": [
        {
            "logo": "https://example.com/sso-logo.png",
            "name": "Company SSO",
            "description": "Login with your company credentials",
            "url": "https://auth.example.com/sso-login"
        },
        {
            "logo": null,
            "name": "SAML Identity Provider",
            "description": "SAML protected single sign-on/logout (SSO/SLO) via an external identity provider",
            "url": "https://app.local-env.goboomtown.com/api/sso/saml/XGWPW7/sso"
        }
    ],
    "current_server_time": "2025-05-07T08:09:12+00:00"
}
```

### Key Components

1. **UserAuth Store** (`src/stores/userAuth.ts`):
   - A new store that handles the getUserAuth API call
   - Does not cache authentication data, used only for UI flow control

2. **TwoStepLogin Component** (`src/components/TwoStepLogin.vue`):
   - Replaces the previous login component
   - Handles the two-step authentication UI logic
   - Manages SSO provider integration and redirection

3. **SsoProviderDialog Component** (`src/components/SsoProviderDialog.vue`):
   - Modal dialog that displays a list of available SSO providers
   - Shows when multiple SSO options are available

4. **Login View** (`src/views/Login.vue`):
   - Updated to use the TwoStepLogin component instead of BravoLoginScreen

## API Service Refactoring

As part of improving the application architecture, we've refactored all API services to use Vue 3 composables:

1. **Composable Pattern**:
   - API services now follow a composable pattern (e.g., `useUserAPI`, `useSettingsAPI`) 
   - Composables properly initialize Pinia stores only when called within component lifecycles
   - This prevents "getActivePinia() was called but there was no active Pinia" errors

2. **Authentication Impact**:
   - The `auth` store now safely manages authentication state
   - Login/logout flows properly initialize all dependent services
   - Form handling for login correctly uses `URLSearchParams` for credential submission

3. **Migration Approach**:
   - Original class-based API services are preserved with deprecation warnings
   - Type interfaces are re-exported for backward compatibility
   - New API consumers should use the composable versions

4. **Refactored Services**:
   - `useKnowledgeAPI`
   - `useUserAPI` 
   - `useIssuesAPI`
   - `useSettingsAPI`
   - `usePartnerAPI`
   - `useMetaAPI`
   - `useInteractionEventsAPI`

5. **HTTP Client Improvements**:
   - Fixed handling of form data with `URLSearchParams`
   - Proper content-type headers for different request types
   - More consistent error handling

## Testing

The implementation includes comprehensive test coverage:

1. **Unit Tests**:
   - `src/components/__tests__/TwoStepLogin.spec.ts` - Tests for the TwoStepLogin component
   - `src/components/__tests__/SsoProviderDialog.spec.ts` - Tests for the SsoProviderDialog component
   - `src/stores/__tests__/userAuth.spec.ts` - Tests for the userAuth store

2. **Mock API**:
   - The mock API implementation has been extended to support SSO flows
   - Added mock responses for the getUserAuth endpoint in `src/mock-api/data/auth.mock.ts`
   - Added API handlers in `src/mock-api/handlers/auth.handlers.ts`
   - Test mock emails:
     - `<EMAIL>` - Normal internal authentication
     - `<EMAIL>` - Hybrid authentication (internal + external)
     - `<EMAIL>` - Single SSO provider authentication
     - `<EMAIL>` - Multiple SSO providers authentication

## Security Considerations

1. **Email Privacy**:
   - The first step validates the email but does not reveal if the account exists
   - A generic "Login failed" message is shown for non-existent accounts

2. **SSO Integration**:
   - External authentication providers are only revealed after a valid email is entered
   - Login URLs are dynamic and unique to each login session
   - SSO authentication handling doesn't unnecessarily expose information about available providers

3. **Federated Identity**:
   - The system supports multiple identity providers for single sign-on
   - Users can seamlessly authenticate via corporate identity providers using standards like SAML and OAuth

## Future Enhancements

1. **Remember Email Preference**:
   - Add option to remember the user's email for future logins

2. **Smart Redirection**:
   - After successful login, redirect users to their original destination if they were redirected to login

3. **Extended Authentication Options**:
   - Support for multi-factor authentication (MFA)
   - Support for WebAuthn/FIDO2 passwordless authentication
   
4. **Preferred Authentication Method**:
   - For users with multiple authentication options, remember their preferred method 