Focus on simplest possible solutions first.
Prefer Vue 3 Composition API and composables over Options API for new components.
Extract reusable logic into composables rather than utility classes or inheritance hierarchies.
Use reactive state and computed properties rather than imperative state mutations when possible.
Prefer pure functions that don't rely on external state for business logic operations.
Group related functionality in targeted composables instead of large monolithic services.
Composables should have clear, focused responsibilities with descriptive names starting with "use".
When implementing complex features, isolate side effects (API calls, DOM manipulation) from core business logic.
For data transformation, favor immutable operations (map, filter, reduce) over mutating arrays or objects.
Keep composable implementation details private when possible - only expose what consumers need.
When suggesting code, start with minimal viable examples instead of over-engineered solutions.
Question complexity - ask yourself "is this actually necessary?" - before proposing complex patterns.
When adding new API functionality always add the Mock-API version of the API request and response, and write unit and playwright coverage that uses the new API request and response.
Static mock data should be static not dynamic.
Handle errors explicitly with appropriate error types; avoid generic catches.
Log errors with contextual information.
For user-facing errors, provide helpful messages without exposing system details.
Use descriptive, consistent naming across the codebase.
Follow language/framework conventions (camelCase, PascalCase, etc.).
Prefix interface names with 'I', enum types with 'E', and avoid abbreviations unless widely understood.
Validate all user inputs.
Never store sensitive data as hardcoded client-side values.
Use parameterized queries to prevent injection attacks.
Implement proper authentication and authorization checks for all API endpoints.
Optimize only after identifying actual bottlenecks.
Minimize network requests and payload sizes.
Consider pagination for large datasets.
Identify performance-critical code and use efficient data structures and algorithms for it.
Add dependencies only when necessary.
Pin version numbers of dependencies precisely.
Regularly audit and update dependencies for security patches.
Document why each dependency was added and its specific purpose in "adr/dependencies_changelog.txt" and modify this file when dependencies are modified or removed.
Use early exit control flows in methods.
When using try/catch blocks use a flat control flow where the try block handles the success flow, the catch block logs specific errors, and then logic at the end of the function and outside of the catch block decides whether to throw or rethrow exceptions that were caught.

#When adding new UI functionality:

- Look first in the Bravo UI Component Library at "node_modules/@services/ui-component-library" for reusable components that could be used for the feature. This component library is mostly made of passthrough components from primevue 4 so the documentation can be found at https://primevue.org/setup/.
- The components are already styled with colors and various states so only add styling for spacing and layout and avoid adding new custom styles for colors, fonts, etc.
- Generally prefer adding CSS classes, but you can use tailwind 3 for simple layout and spacing styling.
- Avoid using inline styling
