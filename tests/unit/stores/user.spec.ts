import { describe, it, expect, vi, beforeEach } from 'vitest';
import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from 'pinia';
import { useUserStore } from '../../../src/stores/user';

describe('User Store', () => {
    const mockPermDefs = {
        "superuser": 1,
        "admin_team_host": 4,
        "issue_view": 6,
        "issue_add": 7,
        "issue_edit": 8,
        "issue_del": 9,
        "issue_merge": 10,
        "cust_view": 11,
        "cust_add": 12,
        "cust_edit": 13,
        "cust_del": 14,
        "cust_merge": 15,
        "cust_user_add": 16,
        "cust_user_edit": 17,
        "cust_user_del": 18,
        "cust_tech_manage": 19,
        "dict_view": 20,
        "dict_add": 21,
        "dict_edit": 22,
        "dict_del": 23,
        "ship_view": 24,
        "ship_add": 25,
        "ship_edit": 26,
        "ship_del": 27,
        "kb_root_add": 28,
        "kb_root_edit": 29,
        "kb_add": 30,
        "kb_edit": 31,
        "kb_publish": 32,
        "kb_del": 33,
        "tech_dispatch": 34,
        "tech_view": 35,
        "tech_add": 36,
        "tech_edit": 37,
        "tech_del": 38,
        "bot_view": 40,
        "bot_add": 41,
        "bot_edit": 42,
        "bot_del": 43,
        "bot_stats": 44,
        "bot_contrib": 45,
        "orgs_add": 46,
        "orgs_del": 48,
        "org_edit": 49,
        "team_edit": 51,
        "user_edit": 52,
        "user_pw": 54,
        "role_view": 55,
        "role_add": 56,
        "role_edit": 57,
        "role_del": 58,
        "manage_channels": 59,
        "manage_integrations": 60,
        "manage_workflows": 61,
        "manage_tags": 62,
        "manage_responses": 63,
        "manage_idr": 64,
        "manage_email_templates": 65,
        "manage_cl_surveys": 66,
        "manage_cl_issue": 67,
        "manage_cl_sow": 68,
        "manage_cl_closeout": 69,
        "manage_api_settings": 70,
        "manage_ml": 71,
        "report_view": 72,
        "org_ml": 73,
        "billing_view": 74,
        "billing_manage": 75,
        "billing_payables": 76,
        "billing_receivables": 77,
        "proactive_dashboard": 78,
        "proactive_view": 79,
        "proactive_checks": 80,
        "proactive_org_matrix": 81,
        "proactive_cust_matrix": 82,
        "packages_view": 83,
        "packages_add": 84,
        "packages_edit": 85,
        "packages_del": 86,
        "profile_edit": 87,
        "profile_avatar": 88,
        "profile_notif": 89,
        "cust_loc_edit": 90,
        "tech_user_edit": 91,
        "tech_user_del": 93,
        "issue_estimate": 94,
        "issue_announce_work_order": 95,
        "issue_announce_training": 96,
        "issue_announce_sales": 97,
        "issue_announce_support": 98,
        "issue_announce_monitoring": 99,
        "kb_view": 100,
        "tech_user_add": 101,
        "issue_rc_session": 102,
        "issue_qa": 103,
        "issue_edit_all": 104,
        "tech_user_pw_edit": 105,
        "rv_add": 106,
        "rv_del": 107,
        "rv_edit": 108,
        "rv_view": 109,
        "journey_view": 111,
        "journey_manage": 112,
        "journey_cases": 113,
        "journey_case_stages": 114,
        "manage_fields": 115,
        "manage_reports": 116,
        "manage_automations": 117,
        "automations_view": 118,
        "manage_templates": 120,
        "dashboards_view": 121,
        "manage_dashboards": 122,
        "tasks_view": 123,
        "tasks_add": 124,
        "tasks_edit": 125,
        "tasks_del": 126,
        "tasks_reopen": 127,
        "tasks_undelete": 128,
        "manage_tokens": 129
    }

    beforeEach(() => {
        // Create a fresh pinia instance for each test
        setActivePinia(createPinia());
    });

    describe('Permission Functions', () => {
        it('should return false for hasPermission when no user data exists', () => {
            const store = useUserStore();
            expect(store.hasPermission('some_permission')).toBe(false);
        });

        it('should check permissions in userData.perms array', () => {
            const store = useUserStore();
            store.persistUserData(
                {
                    perms: ['issue_view', 'issue_edit', 'cust_view'],
                },
                mockPermDefs,
                ['issue_view', 'issue_edit', 'cust_view'],
                {}
            );

            expect(store.hasPermission('issue_view')).toBe(true);
            expect(store.hasPermission('issue_edit')).toBe(true);
            expect(store.hasPermission('cust_view')).toBe(true);
            expect(store.hasPermission('admin_access')).toBe(false);
        });

        it('should check permissions in userData.users.perms array', () => {
            const store = useUserStore();
            // Set mock user data with perms in users object
            store.persistUserData(
                {
                    users: {
                        perms: ['issue_view', 'issue_edit', 'cust_view'],
                    },
                },
                mockPermDefs,
                ['issue_view', 'issue_edit', 'cust_view'],
                {}
            );
            expect(store.hasPermission('issue_view')).toBe(true);
            expect(store.hasPermission('issue_edit')).toBe(true);
            expect(store.hasPermission('cust_view')).toBe(true);
            expect(store.hasPermission('admin_access')).toBe(false);
        });

        it('should check permissions in permSet object', () => {
            const store = useUserStore();
            // Set mock user data with permSet object
            store.persistUserData(
                {
                    perms: {
                        permSet: {
                            issue_view: 'issue_view',
                            issue_edit: 'issue_edit',
                            cust_view: 'cust_view',
                        },
                    },
                },
                mockPermDefs,
                ['issue_view', 'issue_edit', 'cust_view'],
                {}
            );

            expect(store.hasPermission('issue_view')).toBe(true);
            expect(store.hasPermission('issue_edit')).toBe(true);
            expect(store.hasPermission('cust_view')).toBe(true);
            expect(store.hasPermission('admin_access')).toBe(false);
        });

        it('should return empty array from getUserPermissions when no user data exists', () => {
            const store = useUserStore();
            expect(store.getUserPermissions()).toEqual([]);
        });

        it('should get all permissions from perms array', () => {
            const store = useUserStore();
            const expectedPerms = ['issue_view', 'issue_edit', 'cust_view'];

            // Set mock user data with perms array
            store.userData = { perms: expectedPerms };

            expect(store.getUserPermissions()).toEqual(expectedPerms);
        });

        it('should get all permissions from permSet object', () => {
            const store = useUserStore();
            const permSetObj = {
                issue_view: 'issue_view',
                issue_edit: 'issue_edit',
                cust_view: 'cust_view',
            };

            // Set mock user data with permSet object
            store.userData = { perms: { permSet: permSetObj } };

            expect(store.getUserPermissions()).toEqual(Object.keys(permSetObj));
        });

        it('should check if user has any permission', () => {
            const store = useUserStore();
            // Set mock user data with perms array
            store.persistUserData(
                { perms: ['issue_view', 'issue_edit'] },
                mockPermDefs,
                ['issue_view', 'issue_edit', 'cust_view'],
                {}
            );
            expect(store.hasAnyPermission(['issue_view', 'admin_access'])).toBe(true);
            expect(store.hasAnyPermission(['admin_access', 'super_user'])).toBe(false);
        });

        it('should check if user has all permissions', () => {
            const store = useUserStore();
            // Set mock user data with perms array
            store.persistUserData(
                { perms: ['issue_view', 'issue_edit', 'cust_view'] },
                mockPermDefs,
                ['issue_view', 'issue_edit', 'cust_view'],
                {}
            );
            expect(store.hasAllPermissions(['issue_view', 'issue_edit'])).toBe(true);
            expect(store.hasAllPermissions(['issue_view', 'admin_access'])).toBe(false);
        });
    });
});
