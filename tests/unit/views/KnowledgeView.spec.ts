import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import KnowledgeView from '../../../src/modules/knowledge/views/KnowledgeView.vue';
import { createPinia, setActivePinia } from 'pinia';
import { nextTick } from 'vue';
import { useRoute } from 'vue-router';

// Mock route object
const mockRoute = {
    name: 'knowledge-list' as const,
    params: {},
    query: {},
    path: '',
    fullPath: '',
    hash: '',
    matched: [],
    meta: {},
    redirectedFrom: undefined,
};

// Mock vue-router
vi.mock('vue-router', async () => {
    const actual = await vi.importActual('vue-router');
    return {
        ...(actual as any),
        useRoute: vi.fn(() => mockRoute),
    };
});

// Create a reactive mock object to simulate the store
const mockKnowledgeStore = {
    currentNode: null as any,
    loading: false,
    error: null as any,
    totalCount: 0,
    clearCurrentArticle: vi.fn(),
};

// Mock the useKnowledgeStore function
vi.mock('../../../src/modules/knowledge/stores/knowledge', () => ({
    useKnowledgeStore: vi.fn(() => mockKnowledgeStore),
}));

// Mock the child components
vi.mock('../../../src/modules/knowledge/components/KnowledgeSidePanel.vue', () => ({
    default: {
        name: 'SidePanel',
        template: '<div class="mock-side-panel"></div>',
    },
}));

// Mock router-view to avoid router issues in tests
const routerViewStub = {
    template: '<div class="mock-router-view"></div>',
};

// Config for mounting the component with proper mocks
const mountOptions = {
    global: {
        stubs: {
            RouterView: routerViewStub,
            SidePanel: true,
            // Add stubs for PrimeVue components
            Toast: true,
            Button: true,
            Menu: true,
            Dropdown: true,
            DataTable: true,
            Column: true,
            InputText: true,
            Tag: true,
        },
    },
};

describe('KnowledgeView', () => {
    // Reset the mock store before each test
    beforeEach(() => {
        mockKnowledgeStore.currentNode = null;
        mockKnowledgeStore.loading = false;
        mockKnowledgeStore.error = null;

        // Reset vue-router mock to default route
        vi.mocked(useRoute).mockReturnValue({
            ...mockRoute,
            name: 'knowledge-list' as const,
        });

        // Clear any mocks
        vi.clearAllMocks();

        // Setup Pinia
        setActivePinia(createPinia());
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it('renders the component with SidePanel when not in article detail view', () => {
        const wrapper = mount(KnowledgeView, mountOptions);
        expect(wrapper.find('.knowledge-view').exists()).toBe(true);
        expect(wrapper.findComponent({ name: 'SidePanel' }).exists()).toBe(true);
    });

    it("doesn't render SidePanel in article detail view", async () => {
        // Create a detailed mock route for article detail view
        const articleDetailRoute = {
            ...mockRoute,
            name: 'knowledge-article' as const,
            params: { id: '123' },
            path: '/knowledge/article/123',
            fullPath: '/knowledge/article/123',
        };

        // Set the route name to 'knowledge-article'
        vi.mocked(useRoute).mockImplementation(() => articleDetailRoute);

        const wrapper = mount(KnowledgeView, mountOptions);
        await nextTick();

        expect(wrapper.find('.knowledge-view').classes()).toContain('no-sidebar');
        expect(wrapper.findComponent({ name: 'SidePanel' }).exists()).toBe(false);
    });

    // Test content type display based on selected node
    it('displays router view when no item is selected', () => {
        // The store already has currentNode set to null from beforeEach
        const wrapper = mount(KnowledgeView, mountOptions);
        expect(wrapper.find('.mock-router-view').exists()).toBe(true);
    });

    it('computes selectedItem correctly from store.currentNode', () => {
        const testItem = {
            id: 'test123',
            title: 'Test Item',
            content: 'Test content',
            type: 'article',
        };
        mockKnowledgeStore.currentNode = testItem;
        const wrapper = mount(KnowledgeView, mountOptions);
        const vm = wrapper.vm as any;
        expect(vm.selectedItem).toEqual(testItem);
    });

    // Test the isArticleDetail computed property
    it('detects article detail pages based on route name', async () => {
        // Set the route name to 'knowledge-article' *before* mounting the component
        vi.mocked(useRoute).mockReturnValue({
            name: 'knowledge-article',
            params: {},
            query: {},
            path: '',
            fullPath: '',
            hash: '',
            matched: [],
            meta: {},
            redirectedFrom: undefined,
        });

        const wrapper = mount(KnowledgeView, mountOptions);

        // Access the computed property
        const vm = wrapper.vm as any;

        // Wait for Vue to process the updated computed property
        await nextTick();

        // Now check the value
        expect(vm.isArticleDetail).toBe(true);
    });

});
