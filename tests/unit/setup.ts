import { vi } from 'vitest';

/*
// Mock window APIs
vi.stubGlobal('BroadcastChannel', class {
  onmessage: ((msg: any) => void) | null = null;
  postMessage = vi.fn();
  close = vi.fn();
  constructor() {}
  addEventListener(event: string, handler: (msg: any) => void) {
    if (event === 'message') this.onmessage = handler;
  }
});/** */

vi.stubGlobal('setInterval', vi.fn());
vi.stubGlobal('clearInterval', vi.fn());