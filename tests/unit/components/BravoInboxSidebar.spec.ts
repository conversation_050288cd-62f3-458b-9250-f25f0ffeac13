import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { shallowMount, VueWrapper } from '@vue/test-utils';
import BravoInboxSidebar from '../../../src/modules/inbox/components/BravoInboxSidebar.vue';
import { createPinia, setActivePinia } from 'pinia';

// Define component type for typechecking
interface ComponentInstance {
    sidebarItems: Array<{
        id: string;
        label: string;
        count: number;
        icon: string;
        type: 'item' | 'folder';
        isOpen?: boolean;
        children?: any[];
    }>;
    contextMenu: {
        visible: boolean;
        x: number;
        y: number;
        targetId: string | null;
    };
    toggleFolder: (folder: any) => void;
    addNewFolder: () => void;
    showContextMenu: (event: MouseEvent, item: any) => void;
    hideContextMenu: () => void;
    deleteFolder: () => void;
    fetchCustomViews: () => Promise<void>;
    isLoading: boolean;
    loadError: string | null;
}

// Mock uuid
vi.mock('uuid', () => ({
    v4: () => 'mocked-uuid',
}));

// Mock document event listeners
vi.spyOn(document, 'addEventListener').mockImplementation(() => {});
vi.spyOn(document, 'removeEventListener').mockImplementation(() => {});

// Mock Vue's onMounted hook to prevent execution during tests
vi.mock('vue', async () => {
    const actual = await vi.importActual('vue');
    return {
        ...(actual as any),
        onMounted: vi.fn(),
    };
});

// Mock the casesStore
const mockFetchViews = vi.fn().mockResolvedValue(undefined);
vi.mock('../../../src/stores/cases', () => ({
    useCasesStore: vi.fn(() => ({
        fetchViews: mockFetchViews,
        views: [
            { id: 'view1', label: 'Test View 1' },
            { id: 'view2', label: 'Test View 2' },
        ],
    })),
}));

describe('BravoInboxSidebar', () => {
    let wrapper: VueWrapper;

    beforeEach(() => {
        // Clear mock data
        vi.clearAllMocks();

        // Setup Pinia store
        setActivePinia(createPinia());

        // Use shallow mount to avoid issues with nested components
        wrapper = shallowMount(BravoInboxSidebar);
    });

    afterEach(() => {
        wrapper.unmount();
    });

    it('renders the sidebar header correctly', () => {
        expect(wrapper.find('.inbox-sidebar').exists()).toBe(true);
        expect(wrapper.find('.sidebar-header').exists()).toBe(true);
        expect(wrapper.find('.add-folder-btn').exists()).toBe(true);
    });

    it('adds event listeners for document events', () => {
        expect(document.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
        expect(document.addEventListener).toHaveBeenCalledWith('contextmenu', expect.any(Function));
    });

    it('removes event listeners when unmounted', () => {
        wrapper.unmount();
        expect(document.removeEventListener).toHaveBeenCalledWith('click', expect.any(Function));
        expect(document.removeEventListener).toHaveBeenCalledWith('contextmenu', expect.any(Function));
    });

    it('can fetch views when method is called', async () => {
        // Access the fetchCustomViews method
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Call the method directly instead of relying on onMounted
        await vm.fetchCustomViews();

        // Verify the fetchViews was called
        expect(mockFetchViews).toHaveBeenCalled();
    });

    it('shows loading state while fetching views', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Set loading state
        vm.isLoading = true;
        await wrapper.vm.$nextTick();

        // Check if loading state is visible
        expect(wrapper.find('.loading-state').exists()).toBe(true);
    });

    it('shows error state when views fetch fails', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Set error state
        vm.loadError = 'Failed to load views';
        await wrapper.vm.$nextTick();

        // Check if error state is visible
        expect(wrapper.find('.error-message').exists()).toBe(true);
        expect(wrapper.find('.retry-button').exists()).toBe(true);
    });

    it('adds a new folder when add button is clicked', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Store initial length
        const initialLength = vm.sidebarItems.length;

        // Click add button
        await wrapper.find('.add-folder-btn').trigger('click');

        // Check if new folder was added
        expect(vm.sidebarItems.length).toBe(initialLength + 1);

        // Check properties of new folder
        const newFolder = vm.sidebarItems[vm.sidebarItems.length - 1];
        expect(newFolder.id).toBe('mocked-uuid');
        expect(newFolder.label).toBe('New Folder');
        expect(newFolder.type).toBe('folder');
    });

    it('toggles folder when clicked', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Create a test folder
        const testFolder = {
            id: 'test-folder',
            label: 'Test Folder',
            count: 0,
            icon: '📁',
            type: 'folder' as const,
            isOpen: true,
            children: [],
        };

        // Call the toggle method directly
        vm.toggleFolder(testFolder);

        // Check if folder was toggled
        expect(testFolder.isOpen).toBe(false);

        // Toggle again
        vm.toggleFolder(testFolder);
        expect(testFolder.isOpen).toBe(true);
    });

    it('shows context menu on right-click of a folder', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Create a test folder
        const testFolder = {
            id: 'test-folder',
            label: 'Test Folder',
            count: 0,
            icon: '📁',
            type: 'folder' as const,
            isOpen: true,
            children: [],
        };

        // Create a mock event
        const mockEvent = {
            preventDefault: vi.fn(),
            clientX: 100,
            clientY: 100,
        } as unknown as MouseEvent;

        // Call the method directly
        vm.showContextMenu(mockEvent, testFolder);

        // Check context menu state
        expect(vm.contextMenu.visible).toBe(true);
        expect(vm.contextMenu.targetId).toBe('test-folder');
        expect(vm.contextMenu.x).toBe(100);
        expect(vm.contextMenu.y).toBe(100);
        expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('hides context menu', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Set context menu to visible first
        vm.contextMenu.visible = true;

        // Call hide method
        vm.hideContextMenu();

        // Check if hidden
        expect(vm.contextMenu.visible).toBe(false);
    });

    it('deletes a folder when delete option is clicked', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Add a test folder to sidebarItems
        const testFolder = {
            id: 'test-folder',
            label: 'Test Folder',
            count: 0,
            icon: '📁',
            type: 'folder' as const,
            isOpen: true,
            children: [],
        };

        vm.sidebarItems.push(testFolder);
        const initialLength = vm.sidebarItems.length;

        // Set up context menu with target id
        vm.contextMenu.targetId = 'test-folder';

        // Call delete method
        vm.deleteFolder();

        // Check if folder was deleted
        expect(vm.sidebarItems.length).toBe(initialLength - 1);
        expect(vm.sidebarItems.find((item) => item.id === 'test-folder')).toBeUndefined();

        // Check if context menu was hidden
        expect(vm.contextMenu.visible).toBe(false);
    });

    it('prevents deletion of the custom-views folder', async () => {
        // Cast the component instance to our interface
        const vm = wrapper.vm as unknown as ComponentInstance;

        // Find the Custom Views folder
        const customViewsFolder = vm.sidebarItems.find((item) => item.id === 'custom-views');
        expect(customViewsFolder).toBeDefined();

        // Set up context menu with target id being the custom-views folder
        vm.contextMenu.targetId = 'custom-views';
        vm.contextMenu.visible = true;

        // Store initial length
        const initialLength = vm.sidebarItems.length;

        // Call delete method
        vm.deleteFolder();

        // Check that the folder was not deleted
        expect(vm.sidebarItems.length).toBe(initialLength);
        expect(vm.sidebarItems.find((item) => item.id === 'custom-views')).toBeDefined();

        // Context menu should still be hidden
        expect(vm.contextMenu.visible).toBe(false);
    });
});
