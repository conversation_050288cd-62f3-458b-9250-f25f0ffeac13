import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import LanguageSwitcher from '../../../src/components/LanguageSwitcher.vue'
import { createI18n } from 'vue-i18n'
import en from '../../../src/locales/en.json'
import es from '../../../src/locales/es.json'

// Mock setLocale function
vi.mock('../../../src/i18n/index', () => ({
  setLocale: vi.fn((locale) => {
    // Mock implementation that updates i18n directly
    i18n.global.locale.value = locale
    localStorage.setItem('locale', locale)
    document.querySelector('html')?.setAttribute('lang', locale)
  })
}))

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    clear: () => {
      store = {}
    }
  }
})()

// Replace global localStorage with mock
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Create i18n instance for testing
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en,
    es
  }
})

describe('LanguageSwitcher', () => {
  beforeEach(() => {
    localStorage.clear()
    document.querySelector('html')?.removeAttribute('lang')
    i18n.global.locale.value = 'en' // Reset i18n locale before each test
  })

  it('renders properly with default language', () => {
    const wrapper = mount(LanguageSwitcher, {
      global: {
        plugins: [i18n]
      }
    })
    
    expect(wrapper.find('select').exists()).toBe(true)
    expect(wrapper.find('select').element.value).toBe('en')
    expect(wrapper.findAll('option').length).toBe(2)
  })

  it('changes the language when select value changes', async () => {
    const wrapper = mount(LanguageSwitcher, {
      global: {
        plugins: [i18n]
      }
    })
    
    // Change language to Spanish
    await wrapper.find('select').setValue('es')
    
    // Trigger change event
    await wrapper.find('select').trigger('change')
    
    // Wait for next tick to ensure the change is processed
    await wrapper.vm.$nextTick()
    
    // Check if i18n locale has been updated
    expect(i18n.global.locale.value).toBe('es')
    
    // Check if localStorage has been updated
    expect(localStorage.getItem('locale')).toBe('es')
    
    // Check if HTML lang attribute has been updated
    expect(document.querySelector('html')?.getAttribute('lang')).toBe('es')
  })
}) 