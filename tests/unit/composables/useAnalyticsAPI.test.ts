import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia';
import { useAnalyticsAPI } from '@/composables/services/useAnalyticsAPI';

// Mock the permissions composable
vi.mock('@/composables/usePermissions', () => ({
    usePermissions: () => ({
        can: {
            viewDashboards: () => true,
            manageDashboards: () => true,
        }
    })
}));

describe('useAnalyticsAPI', () => {
    beforeEach(() => {
        setActivePinia(createPinia());
    });

    it('should fetch GoodData login successfully', async () => {
        const analyticsAPI = useAnalyticsAPI();
        
        const response = await analyticsAPI.fetchGoodDataLogin();
        
        expect(response).toBeDefined();
        expect(response.success).toBe(true);
        expect(response.jwt).toBeDefined();
        expect(response.dashboard_uri).toBeDefined();
        expect(response.current_server_time).toBeDefined();
        
        // Verify the JWT is a string
        expect(typeof response.jwt).toBe('string');
        expect(response.jwt.length).toBeGreaterThan(0);
        
        // Verify the dashboard URI is a valid URL
        expect(response.dashboard_uri).toMatch(/^https?:\/\//);
        
        // Verify the server time is a valid date string
        expect(new Date(response.current_server_time)).toBeInstanceOf(Date);
    });

    it('should handle API errors gracefully', async () => {
        // This test would need to mock a failed API response
        // For now, we'll just verify the composable exists and can be called
        const analyticsAPI = useAnalyticsAPI();
        expect(analyticsAPI.fetchGoodDataLogin).toBeDefined();
        expect(typeof analyticsAPI.fetchGoodDataLogin).toBe('function');
    });
}); 