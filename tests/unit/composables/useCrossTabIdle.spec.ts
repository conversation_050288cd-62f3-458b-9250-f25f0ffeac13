import { mount } from '@vue/test-utils';
import { useBroadcastChannel, useIdle, useStorage, useTimestamp } from '@vueuse/core';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { nextTick, onScopeDispose, Ref, ref } from 'vue';
import { useCrossTabIdle } from '../../../src/composables/useCrossTabIdle';

// Mock VueUse utilities
vi.mock('@vueuse/core', () => ({
    useIdle: vi.fn(),
    useTimestamp: vi.fn(),
    useStorage: vi.fn(),
    useBroadcastChannel: vi.fn(),
}));

// Mock window objects
const mockLocalStorage = {
    storage: {} as Record<string, string>,
    setItem: vi.fn((key, value) => {
        mockLocalStorage.storage[key] = value;
    }),
    getItem: vi.fn((key) => mockLocalStorage.storage[key] || null),
    removeItem: vi.fn((key) => {
        delete mockLocalStorage.storage[key];
    }),
};

// Mock global objects
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage, writable: true });

describe('useCrossTabIdle', () => {
    let originalAddEventListener: typeof window.addEventListener;
    let originalRemoveEventListener: typeof window.removeEventListener;
    const mockAddEventListener = vi.fn();
    const mockRemoveEventListener = vi.fn();
    let currentTime: number;
    let mockIdle: Ref<boolean>;
    let mockLastActive: Ref<number>;
    let timestampRef: Ref<number>;
    let storageRef: Ref<number>;
    let broadcastChannelData: Ref<{ lastActive: number } | null>;
    let broadcastChannelError: Ref<Event | null>;
    let broadcastChannelPost: vi.fn;
    let broadcastChannelClose: vi.fn;
    let broadcastChannelIsSupported: Ref<boolean>;

    beforeEach(() => {
        // Mock window event listeners
        originalAddEventListener = window.addEventListener;
        originalRemoveEventListener = window.removeEventListener;
        window.addEventListener = mockAddEventListener;
        window.removeEventListener = mockRemoveEventListener;

        // Enable fake timers
        vi.useFakeTimers();

        // Mock Date.now
        currentTime = 1000000000000;
        vi.setSystemTime(currentTime);
        vi.spyOn(Date, 'now').mockImplementation(() => currentTime);

        // Reset mocks
        vi.clearAllMocks();
        mockLocalStorage.storage = {};

        // Mock useIdle
        mockIdle = ref(false);
        mockLastActive = ref(currentTime);
        vi.mocked(useIdle).mockImplementation(() => ({
            idle: mockIdle,
            lastActive: mockLastActive,
        }));

        // Mock useTimestamp
        timestampRef = ref(currentTime);
        vi.mocked(useTimestamp).mockReturnValue(timestampRef);
        vi.spyOn(global, 'setInterval').mockImplementation((cb) => {
            const interval = setInterval(() => {
                currentTime += 1000;
                timestampRef.value = currentTime;
                cb();
            }, 1000);
            return interval as any;
        });

        // Mock useStorage
        storageRef = ref(currentTime);
        vi.mocked(useStorage).mockImplementation(() => storageRef);

        // Mock useBroadcastChannel
        broadcastChannelData = ref(null);
        broadcastChannelError = ref(null);
        broadcastChannelPost = vi.fn();
        broadcastChannelClose = vi.fn();
        broadcastChannelIsSupported = ref(true);
        vi.mocked(useBroadcastChannel).mockImplementation(() => {
            // Simulate tryOnScopeDispose by calling close on scope disposal
            onScopeDispose(() => {
                broadcastChannelClose();
            });
            return {
                isSupported: broadcastChannelIsSupported,
                data: broadcastChannelData,
                error: broadcastChannelError,
                post: broadcastChannelPost,
                close: broadcastChannelClose,
                channel: ref(undefined),
                isClosed: ref(false),
            };
        });
    });

    afterEach(() => {
        window.addEventListener = originalAddEventListener;
        window.removeEventListener = originalRemoveEventListener;
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('localStorage Backend', () => {
        it('initializes with localStorage and sets lastActivity', async () => {
            const { isIdle, lastActive, error } = useCrossTabIdle({
                idleTimeout: 5000,
                backend: 'localStorage',
            });

            await nextTick();
            expect(vi.mocked(useStorage)).toHaveBeenCalledWith(
                'lastActivity',
                expect.any(Number),
                localStorage,
                expect.any(Object)
            );
            expect(isIdle.value).toBe(false);
            expect(lastActive.value).toBe(currentTime);
            expect(error.value).toBe(null);
        });

        it('updates lastActivity on user activity', async () => {
            const { isIdle } = useCrossTabIdle({ idleTimeout: 5000, backend: 'localStorage' });

            currentTime += 1000;
            vi.setSystemTime(currentTime);
            mockLastActive.value = currentTime;
            await nextTick();

            expect(storageRef.value).toBe(currentTime);
            expect(isIdle.value).toBe(false);
        });

        it('sets idle state after timeout', async () => {
            const { isIdle } = useCrossTabIdle({ idleTimeout: 2000, backend: 'localStorage' });

            currentTime += 3000;
            vi.setSystemTime(currentTime);
            vi.advanceTimersByTime(3000);
            timestampRef.value = currentTime;
            await nextTick();

            expect(isIdle.value).toBe(true);
        });

        it('handles storage events from other tabs', async () => {
            const { lastActive } = useCrossTabIdle({ idleTimeout: 5000, backend: 'localStorage' });

            const newTime = currentTime + 2000;
            storageRef.value = newTime;
            await nextTick();

            expect(lastActive.value).toBe(newTime);
        });

        it('falls back to single-tab if localStorage is unavailable', async () => {
            vi.spyOn(window, 'localStorage', 'get').mockReturnValue(undefined as any);

            const { error } = useCrossTabIdle({ idleTimeout: 5000, backend: 'localStorage' });

            await nextTick();
            expect(error.value).toBe('localStorage is unavailable. Idle detection is limited to this tab.');
            expect(vi.mocked(useIdle)).toHaveBeenCalledWith(5000, expect.any(Object));
        });

        it('handles invalid lastActivity data', async () => {
            mockLocalStorage.getItem.mockReturnValue('invalid');
            vi.mocked(useStorage).mockReset();
            vi.mocked(useStorage).mockImplementation((key, initialValue, storage, options) => {
                const value = storage.getItem(key);
                const refValue = options?.serializer?.read(value ?? '') ?? initialValue;
                return ref(refValue) as Ref<number>;
            });

            const { error } = useCrossTabIdle({ idleTimeout: 5000, backend: 'localStorage' });

            await nextTick();
            expect(error.value).toBe('Error accessing localStorage: Invalid data. Idle detection may be inconsistent.');
        });

        it('does not set up manual storage event listeners', async () => {
            useCrossTabIdle({ idleTimeout: 5000, backend: 'localStorage' });
            await nextTick();

            expect(mockAddEventListener).not.toHaveBeenCalledWith('storage', expect.any(Function));
        });
    });

    describe('broadcastChannel Backend', () => {
        it('initializes with BroadcastChannel', async () => {
            const { isIdle, lastActive, error } = useCrossTabIdle({
                idleTimeout: 5000,
                backend: 'broadcastChannel',
            });

            await nextTick();
            expect(vi.mocked(useBroadcastChannel)).toHaveBeenCalledWith({ name: 'activity' });
            expect(isIdle.value).toBe(false);
            expect(lastActive.value).toBe(currentTime);
            expect(error.value).toBe(null);
        });

        it('broadcasts activity to other tabs', async () => {
            const { isIdle } = useCrossTabIdle({ idleTimeout: 5000, backend: 'broadcastChannel' });

            currentTime += 1000;
            vi.setSystemTime(currentTime);
            mockLastActive.value = currentTime;
            await nextTick();

            expect(broadcastChannelPost).toHaveBeenCalledWith({ lastActive: currentTime });
            expect(isIdle.value).toBe(false);
        });

        it('sets idle state after timeout', async () => {
            const { isIdle } = useCrossTabIdle({ idleTimeout: 2000, backend: 'broadcastChannel' });

            currentTime += 3000;
            vi.setSystemTime(currentTime);
            vi.advanceTimersByTime(3000);
            timestampRef.value = currentTime;
            await nextTick();

            expect(isIdle.value).toBe(true);
        });

        it('handles messages from other tabs', async () => {
            const { lastActive } = useCrossTabIdle({ idleTimeout: 5000, backend: 'broadcastChannel' });

            const newTime = currentTime + 2000;
            broadcastChannelData.value = { lastActive: newTime };
            await nextTick();

            expect(lastActive.value).toBe(newTime);
        });

        it('falls back to single-tab if BroadcastChannel is unavailable', async () => {
            vi.mocked(useBroadcastChannel).mockReturnValue({
                isSupported: ref(false),
                data: broadcastChannelData,
                error: broadcastChannelError,
                post: broadcastChannelPost,
                close: broadcastChannelClose,
                channel: ref(undefined),
                isClosed: ref(false),
            });

            const { error } = useCrossTabIdle({ idleTimeout: 5000, backend: 'broadcastChannel' });

            await nextTick();
            expect(error.value).toBe('BroadcastChannel is not supported. Idle detection is limited to this tab.');
            expect(vi.mocked(useIdle)).toHaveBeenCalledWith(5000, expect.any(Object));
        });

        it('handles BroadcastChannel errors', async () => {
            const { error } = useCrossTabIdle({ idleTimeout: 5000, backend: 'broadcastChannel' });

            // Simulate error after initialization
            const errorMsg = new Event('error');
            broadcastChannelError.value = errorMsg;
            await nextTick();

            expect(error.value).toBe('Failed to initialize BroadcastChannel. Idle detection is limited to this tab.');
        });

        it('cleans up BroadcastChannel on unmount', async () => {
            const wrapper = mount({
                setup() {
                    useCrossTabIdle({ idleTimeout: 5000, backend: 'broadcastChannel' });
                    return {};
                },
            });

            await wrapper.unmount();
            await nextTick();

            expect(broadcastChannelClose).toHaveBeenCalled();
        });
    });

    describe('singleTab Fallback', () => {
        it('uses single-tab fallback when backend fails', async () => {
            vi.spyOn(window, 'localStorage', 'get').mockReturnValue(undefined as any);

            const { error, isIdle, lastActive } = useCrossTabIdle({
                idleTimeout: 5000,
                backend: 'localStorage',
            });

            await nextTick();
            expect(error.value).toBe('localStorage is unavailable. Idle detection is limited to this tab.');
            expect(isIdle.value).toBe(false);
            expect(lastActive.value).toBe(currentTime);
        });

        it('tracks idle state in single-tab mode', async () => {
            const { isIdle } = useCrossTabIdle({ idleTimeout: 2000, backend: 'singleTab' as any });

            mockIdle.value = true;
            await nextTick();

            expect(isIdle.value).toBe(true);
        });
    });

    describe('Edge Cases', () => {
        it('handles invalid backend gracefully', async () => {
            const { error } = useCrossTabIdle({ idleTimeout: 5000, backend: 'invalid' as any });

            await nextTick();
            expect(error.value).toBe('Invalid backend specified. Falling back to single-tab idle detection.');
            expect(vi.mocked(useIdle)).toHaveBeenCalledWith(5000, expect.any(Object));
        });

        it('respects custom events', async () => {
            useCrossTabIdle({
                idleTimeout: 5000,
                events: ['click', 'customEvent'],
                backend: 'localStorage',
            });

            expect(vi.mocked(useIdle)).toHaveBeenCalledWith(
                expect.any(Number),
                expect.objectContaining({ events: ['click', 'customEvent'] })
            );
        });

        it('handles rapid activity updates', async () => {
            const { lastActive } = useCrossTabIdle({ idleTimeout: 5000, backend: 'broadcastChannel' });

            currentTime += 100;
            vi.setSystemTime(currentTime);
            mockLastActive.value = currentTime;
            await nextTick();
            currentTime += 100;
            vi.setSystemTime(currentTime);
            mockLastActive.value = currentTime;
            await nextTick();

            expect(broadcastChannelPost).toHaveBeenCalledTimes(2);
            expect(lastActive.value).toBe(currentTime);
        });
    });
});