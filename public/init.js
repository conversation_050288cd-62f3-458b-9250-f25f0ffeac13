(function() {
  // Get query parameters
  const urlParams = new URLSearchParams(window.location.search);
  let previewBuild = urlParams.get('preview_build');

  if (previewBuild) {
    console.log('[init.js] preview build found in URL');
    // Validate build name: alphanumeric, /, _, -, no ..
    const isValidbuild = /^[a-zA-Z0-9\/_-]+$/.test(previewBuild) && !previewBuild.includes('..');

    if (!isValidbuild) {
      console.warn('[init.js] invalid build name:', previewBuild);
      localStorage.removeItem('preview_build');
    } else {
      localStorage.setItem('preview_build', previewBuild);
    }
    // Clean up URL
    const cleanUrl = window.location.pathname + window.location.hash;
    window.history.replaceState({}, document.title, cleanUrl);
  }

  previewBuild = localStorage.getItem('preview_build');

  // set the base tag immediately, before any other scripts run
  // Run a check to see if the preview build index.html exists
  // if not, remove the preview build from localStorage and refresh the page
  if (previewBuild && previewBuild != null) {
    console.log('[init.js] adding the base tag');
    const baseElement = document.createElement('base')
    baseElement.href = `/preview_build/${encodeURI(previewBuild)}/`
    //document.head.appendChild(baseElement)
    document.head.prepend(baseElement)
    console.log('[init.js] Base tag added:', baseElement.href)
    // Check if build assets exist
    fetch(`/preview_build/${encodeURI(previewBuild)}/index.html`, { method: 'HEAD' })
      .then((response) => {
        if (!response.ok) {
          console.warn('build assets not found:', previewBuild);
          localStorage.removeItem('preview_build');
          // refresh page to remove preview build
          window.location.reload();
        }
      })
      .catch((error) => {
        console.error('[init.js] error checking build assets:', error);
        localStorage.removeItem('preview_build');
        // refresh page to remove preview build
        window.location.reload();
      })
      .finally(() => {
        // Clean up URL regardless of outcome
        const cleanUrl = window.location.pathname + window.location.hash;
        window.history.replaceState({}, document.title, cleanUrl);
      });
  } else {
    console.debug('[init.js] setting base to default value of /');
    const baseElement = document.createElement('base')
    baseElement.href = "/"
    document.head.prepend(baseElement)
  }
})();

