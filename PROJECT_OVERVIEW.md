# CXM-UI Project Overview

## Introduction
CXM-UI is a Vue 3 application built with Vite, designed as part of the OvationCXM platform. The project uses modern frontend technologies and follows best practices for Vue development.

## Technology Stack

- **Framework**: Vue 3
- **Build Tool**: Vite
- **Language**: TypeScript
- **Testing**:
  - Unit Testing: Vitest
  - E2E Testing: Playwright
- **Styling**: Tailwind CSS (with preflight disabled)
- **Internationalization**: vue-i18n
- **State Management**: Pinia
- **Component Documentation**: Storybook
- **Rich Text Editing**: Froala Editor

## Project Structure

```
cxm-ui/
├── .storybook/           # Storybook configuration
├── dist/                 # Build output directory
├── docs/                 # Documentation
│   └── froala/           # Froala editor configuration
├── e2e/                  # End-to-end tests
│   └── pages/            # Page Object Models
├── netlify/              # Netlify configuration
├── public/               # Static assets
├── src/
│   ├── assets/           # Project assets
│   ├── components/       # Vue components
│   ├── i18n/             # Internationalization setup
│   ├── locales/          # Translation files
│   ├── mocks/            # Mock Service Worker handlers
│   ├── router/           # Vue Router configuration
│   ├── stores/           # Pinia stores
│   ├── stories/          # Storybook stories
│   └── types/            # TypeScript type definitions
└── tests/                # Unit tests
```

## Development Setup

### Prerequisites
- Node.js (version specified in project)
- npm or yarn

### Installation

```sh
npm install
```

### Development Server

```sh
npm run dev
```

### Building for Production

```sh
npm run build
```

### Running Tests

```sh
# Unit tests
npm run test:unit

# E2E tests
npm run test:e2e

# E2E tests with specific configuration
npm run test:e2e -- --project=chromium
```

## Deployment

The project is configured for deployment to multiple environments:
- Development
- Staging
- Pre-production
- Production

Deployment is managed through GitLab CI/CD pipelines, with different deployment targets based on branch and merge request status.

## Preview Builds

The system supports preview builds for merge requests, allowing stakeholders to review changes before they're merged. Preview builds are accessible via a special URL pattern and can be configured to point to different backend environments.

## Internationalization

The application supports multiple languages through vue-i18n:
- English (en)
- Spanish (es)
- German (de)

Translation files are stored in `src/locales/` directory.

## Component Development

The project uses Storybook for component development and documentation. Components can be developed and tested in isolation before integration into the main application.

```sh
# Run Storybook
npm run storybook
```

## Recommended IDE Setup

- VSCode with Volar extension (and Vetur disabled)
- TypeScript support for `.vue` files is handled by vue-tsc

## Additional Tools

- ESLint for code linting
- Prettier for code formatting
- Tailwind CSS for utility-first styling
- Mock Service Worker for API mocking during development

## CI/CD Pipeline

The project uses GitLab CI/CD for continuous integration and deployment:
- Automated testing
- Preview builds for merge requests
- Deployment to staging, pre-production, and production environments

## Documentation

Additional documentation is available in the `docs/` directory, covering topics such as:
- Froala editor configuration
- Internationalization guide
- Testing strategies