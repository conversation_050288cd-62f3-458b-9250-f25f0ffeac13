build_staging_preview:
  stage: build
  extends:
    - .build
    - .rules_preview
  environment:
    name: stage/$CI_MERGE_REQUEST_IID
    action: prepare
  when: manual
  variables:
    BRANCH_NAME: build/$CI_MERGE_REQUEST_IID
    VITE_BOOMTOWN_API_HOST: https://api.stage.goboomtown.com
deploy_staging_preview:
  stage: deploy
  extends:
    - .deploy_preview_gcs
    - .rules_preview
  needs: [build_staging_preview]
  variables:
    DEPLOY_FOLDER_NAME: build/$CI_MERGE_REQUEST_IID
  environment:
    name: stage/$CI_MERGE_REQUEST_IID
    url: https://appv5.stage-ovationcxm.app?preview_build=build/$CI_MERGE_REQUEST_IID

destroy_staging_preview:
  stage: postdeploy
  extends:
    - .destroy_preview_gcs
    - .rules_preview
  needs: [deploy_staging_preview]
  variables:
    DEPLOY_FOLDER_NAME: build/$CI_MERGE_REQUEST_IID
    GIT_STRATEGY: none
  environment:
    name: stage/$CI_MERGE_REQUEST_IID
    action: stop
  when: manual

build_preproduction_preview:
  stage: build
  extends:
    - .build
    - .rules_preview
  environment:
    name: preprod/$CI_MERGE_REQUEST_IID
    action: prepare
  when: manual
  variables:
    BRANCH_NAME: build/$CI_MERGE_REQUEST_IID
    VITE_BOOMTOWN_API_HOST: https://api.preprod.goboomtown.com

deploy_preproduction_preview:
  stage: deploy
  extends:
    - .deploy_preview_gcs
    - .rules_preview
  needs: [build_preproduction_preview]
  variables:
    DEPLOY_FOLDER_NAME: build/$CI_MERGE_REQUEST_IID
  environment:
    name: preprod/$CI_MERGE_REQUEST_IID
    url: https://appv5.preprod-ovationcxm.app?preview_build=build/$CI_MERGE_REQUEST_IID

destroy_preproduction_preview:
  stage: postdeploy
  extends:
    - .destroy_preview_gcs
    - .rules_preview
  needs: [deploy_preproduction_preview]
  variables:
    DEPLOY_FOLDER_NAME: build/$CI_MERGE_REQUEST_IID
    GIT_STRATEGY: none
  environment:
    name: preprod/$CI_MERGE_REQUEST_IID
    action: stop
  when: manual

build_production_preview:
  stage: build
  extends:
    - .build
    - .rules_preview
  environment:
    name: prod/$CI_MERGE_REQUEST_IID
    action: prepare
  when: manual
  variables:
    BRANCH_NAME: build/$CI_MERGE_REQUEST_IID
    VITE_BOOMTOWN_API_HOST: https://api.goboomtown.com
deploy_production_preview:
  stage: deploy
  extends:
    - .deploy_preview_gcs
    - .rules_preview
  needs: [build_production_preview]
  variables:
    DEPLOY_FOLDER_NAME: build/$CI_MERGE_REQUEST_IID
  environment:
    name: prod/$CI_MERGE_REQUEST_IID
    url: https://appv5.ovationcxm.app?preview_build=build/$CI_MERGE_REQUEST_IID

destroy_production_preview:
  stage: postdeploy
  extends:
    - .destroy_preview_gcs
    - .rules_preview
  needs: [deploy_production_preview]
  variables:
    DEPLOY_FOLDER_NAME: build/$CI_MERGE_REQUEST_IID
    GIT_STRATEGY: none
  environment:
    name: prod/$CI_MERGE_REQUEST_IID
    action: stop
  when: manual
