#!/bin/bash

set -e

# Usage: ./scripts/release.sh [patch|minor|major|explicit] [full|version-only]
# Default: patch, full
# - full: Update package.json, generate changelog, commit, tag, push
# - version-only: Update package.json with new version, no commit/tag
# In CI, if a tag like vX.Y.Z exists, uses the tag version (explicit mode)

VERSION_TYPE=${1:-patch}
MODE=${2:-full}
PACKAGE_JSON="package.json"
CHANGELOG="CHANGELOG.md"
CI_COMMIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

echo "Running on branch: $CI_COMMIT_BRANCH . Default branch: $CI_DEFAULT_BRANCH"
# Ensure we're on the master branch for CI or allow manual runs
if [[ -n "$CI" && "$CI_COMMIT_BRANCH" != "$CI_DEFAULT_BRANCH" ]]; then
    echo "Error: CI runs must be on the master branch"
    exit 1
fi

# Ensure working directory is clean
if [[ -n $(git status --porcelain) ]]; then
    echo "Error: Working directory is not clean"
    exit 1
fi

# Get current version from package.json
CURRENT_VERSION=$(jq -r '.version' "$PACKAGE_JSON")
if [[ -z "$CURRENT_VERSION" ]]; then
    echo "Error: Could not read version from $PACKAGE_JSON"
    exit 1
fi

# Check for explicit version from git tag in CI
if [[ -n "$CI_COMMIT_TAG" && "$CI_COMMIT_TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    VERSION_TYPE="explicit"
    NEW_VERSION="${CI_COMMIT_TAG#v}" # Remove 'v' prefix
    echo "Using explicit version from tag: $NEW_VERSION"
else
    # Split version into major, minor, patch
    IFS='.' read -r MAJOR MINOR PATCH <<< "$CURRENT_VERSION"

    # Bump version based on type
    case "$VERSION_TYPE" in
        major)
            MAJOR=$((MAJOR + 1))
            MINOR=0
            PATCH=0
            ;;
        minor)
            MINOR=$((MINOR + 1))
            PATCH=0
            ;;
        patch)
            PATCH=$((PATCH + 1))
            ;;
        explicit)
            echo "Error: Explicit version requires a tag like vX.Y.Z in CI"
            exit 1
            ;;
        *)
            echo "Error: Invalid version type. Use patch, minor, major, or explicit"
            exit 1
            ;;
    esac
    NEW_VERSION="$MAJOR.$MINOR.$PATCH"
fi

echo "Bumping version from $CURRENT_VERSION to $NEW_VERSION"

# Update package.json
jq ".version = \"$NEW_VERSION\"" "$PACKAGE_JSON" > tmp.json && mv tmp.json "$PACKAGE_JSON"

if [[ "$MODE" == "version-only" ]]; then
    echo "Version-only mode: Updated package.json to $NEW_VERSION"
    exit 0
fi

# Only generate changelog in explicit mode
if [ "$VERSION_TYPE" = "explicit" ]; then
    # Generate changelog entry
    echo "Generating changelog for v$NEW_VERSION"

    # Get commits since last tag (or all if no tags)
    LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
    if [[ -z "$LAST_TAG" ]]; then
        COMMIT_RANGE="HEAD"
    else
        COMMIT_RANGE="$LAST_TAG..HEAD"
    fi

    # Simple changelog based on conventional commits
    {
        echo "## [$NEW_VERSION] - $(date +%Y-%m-%d)"
        echo ""
        echo "### Added"
        git log "$COMMIT_RANGE" --pretty=%s | grep -E "^[A-Z]{3}-[0-9]{1,6} feat:" | sed 's/^\([A-Z]{3}-[0-9]\{1,6\}\) feat: / - \1 /g' || echo " - None"
        echo ""
        echo "### Fixed"
    git log "$COMMIT_RANGE" --pretty=%s | grep -E "^[A-Z]{3}-[0-9]{1,6} fix:" | sed 's/^\([A-Z]{3}-[0-9]\{1,6\}\) fix: / - \1 /g' || echo " - None"
        echo ""
        echo "### Changed"
        git log "$COMMIT_RANGE" --pretty=%s | grep -E "^[A-Z]{3}-[0-9]{1,6} chore:" | sed 's/^\([A-Z]{3}-[0-9]\{1,6\}\) chore: / - \1 /g' || echo " - None"
        echo ""
    } > tmp_changelog.md

    # Prepend to existing changelog
    if [[ -f "$CHANGELOG" ]]; then
        cat tmp_changelog.md "$CHANGELOG" > tmp.md && mv tmp.md "$CHANGELOG"
    else
        mv tmp_changelog.md "$CHANGELOG"
    fi
    git add "$CHANGELOG"
fi

# Commit changes
git add "$PACKAGE_JSON"
git commit -m "CXM- chore(release): v$NEW_VERSION"

# No auto-creating tags for now
# Create tag if not in explicit mode (tag already exists in explicit mode)
#if [[ "$VERSION_TYPE" != "explicit" ]]; then
#    git tag "v$NEW_VERSION"
#fi

# Push changes and tag
if [[ -n "$CI" ]]; then
    # In CI, push to master and tag (if not explicit)
    git push -o ci.skip origin $CI_DEFAULT_BRANCH
    #if [[ "$VERSION_TYPE" != "explicit" ]]; then
        #git push origin "v$NEW_VERSION"
    #fi
else
    # Local runs prompt for push
    read -p "Push changes and tag to origin? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git push -o ci.skip origin $CI_DEFAULT_BRANCH
        if [[ "$VERSION_TYPE" != "explicit" ]]; then
            git push -o ci.skip origin "v$NEW_VERSION"
        fi
    fi
fi

echo "Bumped to v$NEW_VERSION successfully"
