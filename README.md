# cxm-ui

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
npm run test:unit
```

### Run End-to-End Tests with [<PERSON><PERSON>](https://playwright.dev)

```sh
# Install browsers for the first run
npx playwright install

# When testing on CI, must build the project first
npm run build

# Runs the end-to-end tests
npm run test:e2e
# Runs the tests only on Chromium
npm run test:e2e -- --project=chromium
# Runs the tests of a specific file
npm run test:e2e -- tests/example.spec.ts
# Runs the tests in debug mode
npm run test:e2e -- --debug
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## Merge Request Preview Builds

Merge requests can be previewed on stage, preproduction, and production environments before merging, enabling testing and verification of changes as necessary. To deploy to a specific environment, first open a merge request, then in the merge request pipeline, run the job to build staging preview, build preproduction preview, or build production preview. From there, a build will start, followed by a deployment. Currently deployed environments can accessed in GitLab under [Environments](https://git.goboomtown.com/services/cxmengine/cxme-ui/-/environments), nested under the appropriate folder. Environments display buttons to directly navigate to the environment as well as a button to destroy the environment. Make sure to destroy the environment before merging.

Preview builds will display a small banner on top of the page stating that a preview build is being used as well as which specific build. It also includes a button to navigate back to the main build for a given environment.


## Commits

Use the following format for commit messages:

```
CXM-000 feat: My new feature
CXM-000 fix: My bug fix
CXM-000 chore: Something that changes
```

This will automatically populate the changelog.

